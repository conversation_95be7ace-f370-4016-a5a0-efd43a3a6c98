#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=1992, tid=7600
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-8e4fb7c55867a198edc70155b3d76505-sock

Host: Intel(R) Core(TM)2 Quad  CPU   Q9300  @ 2.50GHz, 4 cores, 3G,  Windows 10 , 64 bit Build 16299 (10.0.16299.15)
Time: Sun May 25 14:14:02 2025 Central Europe Daylight Time elapsed time: 12.746488 seconds (0d 0h 0m 12s)

---------------  T H R E A D  ---------------

Current thread (0x000002bee91d6ff0):  JavaThread "Start Level: Equinox Container: 215f94b8-713c-4ce6-a337-8810a4cbbf17" daemon [_thread_in_vm, id=7600, stack(0x000000ca8d100000,0x000000ca8d200000) (1024K)]

Stack: [0x000000ca8d100000,0x000000ca8d200000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0x8a411e]
V  [jvm.dll+0x670385]
V  [jvm.dll+0x6703ea]
V  [jvm.dll+0x672bd2]
V  [jvm.dll+0x672aa2]
V  [jvm.dll+0x670d5e]
V  [jvm.dll+0x308841]
V  [jvm.dll+0x216d33]
V  [jvm.dll+0x20bba7]
V  [jvm.dll+0x5ae3ec]
V  [jvm.dll+0x21d24a]
V  [jvm.dll+0x820d9c]
V  [jvm.dll+0x821dc4]
V  [jvm.dll+0x8225b0]
V  [jvm.dll+0x2169bc]
V  [jvm.dll+0x20bba7]
V  [jvm.dll+0x5ae3ec]
V  [jvm.dll+0x21d24a]
V  [jvm.dll+0x820d9c]
V  [jvm.dll+0x821dc4]
V  [jvm.dll+0x822392]
V  [jvm.dll+0x822018]
V  [jvm.dll+0x26cd7b]
V  [jvm.dll+0x3d479e]
C  0x000002bed9998db1

The last pc belongs to new (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  com.sun.org.apache.xalan.internal.xsltc.runtime.output.TransletOutputHandlerFactory.getSerializationHandler()Lcom/sun/org/apache/xml/internal/serializer/SerializationHandler;+70 java.xml@21.0.6
j  com.sun.org.apache.xalan.internal.xsltc.trax.TransformerImpl.getOutputHandler(Ljavax/xml/transform/Result;)Lcom/sun/org/apache/xml/internal/serializer/SerializationHandler;+320 java.xml@21.0.6
j  com.sun.org.apache.xalan.internal.xsltc.trax.TransformerImpl.transform(Ljavax/xml/transform/Source;Ljavax/xml/transform/Result;)V+46 java.xml@21.0.6
j  org.eclipse.text.templates.TemplateReaderWriter.save([Lorg/eclipse/text/templates/TemplatePersistenceData;Ljavax/xml/transform/stream/StreamResult;)V+472
j  org.eclipse.text.templates.TemplateReaderWriter.save([Lorg/eclipse/text/templates/TemplatePersistenceData;Ljava/io/Writer;)V+10
j  org.eclipse.jdt.ls.core.internal.preferences.PreferenceManager.reloadTemplateStore()V+84
j  org.eclipse.jdt.ls.core.internal.preferences.PreferenceManager.initialize()V+657
j  org.eclipse.jdt.ls.core.internal.preferences.PreferenceManager.<init>()V+40
j  org.eclipse.jdt.ls.core.internal.JavaLanguageServerPlugin.start(Lorg/osgi/framework/BundleContext;)V+50
j  org.eclipse.osgi.internal.framework.BundleContextImpl$2.run()Ljava/lang/Void;+23
j  org.eclipse.osgi.internal.framework.BundleContextImpl$2.run()Ljava/lang/Object;+1
J 2638 c1 java.security.AccessController.doPrivileged(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object; java.base@21.0.6 (22 bytes) @ 0x000002bed2970df4 [0x000002bed2970c80+0x0000000000000174]
j  org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(Lorg/osgi/framework/BundleActivator;)V+9
j  org.eclipse.osgi.internal.framework.BundleContextImpl.start()V+141
j  org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0()V+35
j  org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker()V+4
j  org.eclipse.osgi.container.Module.doStart([Lorg/eclipse/osgi/container/Module$StartOptions;)Lorg/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent;+200
j  org.eclipse.osgi.container.Module.start([Lorg/eclipse/osgi/container/Module$StartOptions;)V+460
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run()V+83
j  org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(Ljava/lang/Runnable;)V+1
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Z)V+193
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V+19
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(Lorg/eclipse/osgi/container/Module;I[Lorg/osgi/framework/FrameworkListener;)V+358
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Lorg/eclipse/osgi/container/Module;[Lorg/osgi/framework/FrameworkListener;ILjava/lang/Integer;)V+32
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V+15
j  org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(Ljava/util/Set;Lorg/eclipse/osgi/framework/eventmgr/EventDispatcher;ILjava/lang/Object;)V+48
j  org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run()V+26
v  ~StubRoutines::call_stub 0x000002bed9981015
new  187 new  [0x000002bed9998c20, 0x000002bed9998e20]  512 bytes
[MachCode]
  0x000002bed9998c20: 4883 ec08 | f30f 1104 | 24eb 1f48 | 83ec 10f2 | 0f11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000002bed9998c40: 4424 0800 | 0000 00eb | 0150 410f | b755 010f | cac1 ea10 | 488b 4de8 | 488b 4908 | 488b 4908 
  0x000002bed9998c60: 488b 4108 | 807c 1004 | 070f 85d3 | 0000 0066 | 8b54 d148 | 488b 4928 | 488b 4cd1 | 0851 80b9 
  0x000002bed9998c80: 2101 0000 | 040f 85b6 | 0000 008b | 5108 f6c2 | 010f 85aa | 0000 0049 | 8b87 b801 | 0000 488d 
  0x000002bed9998ca0: 1c10 493b | 9fc8 0100 | 000f 8792 | 0000 0049 | 899f b801 | 0000 4883 | ea10 0f84 | 0f00 0000 
  0x000002bed9998cc0: 33c9 c1ea | 0348 894c | d008 48ff | ca75 f648 | c700 0100 | 0000 5933 | f689 700c | 49ba 0000 
  0x000002bed9998ce0: 0080 be02 | 0000 492b | ca89 4808 | 49ba 365b | 4580 fb7f | 0000 4180 | 3a00 0f84 | 3c00 0000 
  0x000002bed9998d00: 5048 8bc8 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 004c ef7f | fb7f 0000 
  0x000002bed9998d20: ffd0 4883 | c408 e90c | 0000 0048 | b800 4cef | 7ffb 7f00 | 00ff d048 | 83c4 2058 | e9c5 0000 
  0x000002bed9998d40: 0059 488b | 55e8 488b | 5208 488b | 5208 450f | b745 0141 | 0fc8 41c1 | e810 e805 | 0000 00e9 
  0x000002bed9998d60: a200 0000 | 488d 4424 | 084c 896d | c049 8bcf | 4989 afa8 | 0300 0049 | 8987 9803 | 0000 4883 
  0x000002bed9998d80: ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 5047 | b97f fb7f | 0000 ffd0 | 4883 c408 
  0x000002bed9998da0: e90c 0000 | 0048 b850 | 47b9 7ffb | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 | 0000 0000 
  0x000002bed9998dc0: 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 4983 | 7f08 000f | 8405 0000 
  0x000002bed9998de0: 00e9 1a81 | feff 498b | 87f0 0300 | 0049 c787 | f003 0000 | 0000 0000 | 4c8b 6dc0 | 4c8b 75c8 
  0x000002bed9998e00: 4e8d 74f5 | 00c3 410f | b65d 0349 | 83c5 0349 | ba30 2148 | 80fb 7f00 | 0041 ff24 | da66 6690 
[/MachCode]

Compiled method (c1) 12899 2638   !   3       java.security.AccessController::doPrivileged (22 bytes)
 total in heap  [0x000002bed2970a90,0x000002bed2971340] = 2224
 relocation     [0x000002bed2970bf0,0x000002bed2970c80] = 144
 main code      [0x000002bed2970c80,0x000002bed29710c0] = 1088
 stub code      [0x000002bed29710c0,0x000002bed2971128] = 104
 metadata       [0x000002bed2971128,0x000002bed2971148] = 32
 scopes data    [0x000002bed2971148,0x000002bed29711f0] = 168
 scopes pcs     [0x000002bed29711f0,0x000002bed29712d0] = 224
 dependencies   [0x000002bed29712d0,0x000002bed29712e0] = 16
 handler table  [0x000002bed29712e0,0x000002bed2971328] = 72
 nul chk table  [0x000002bed2971328,0x000002bed2971340] = 24

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x000002be8046ca78} 'doPrivileged' '(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;' in 'java/security/AccessController'
  # parm0:    rdx:rdx   = 'java/security/PrivilegedExceptionAction'
  #           [sp+0x70]  (sp of caller)
  0x000002bed2970c80: 8984 2400 | 80ff ff55 | 4883 ec60 | 4181 7f20 | 0200 0000 

  0x000002bed2970c94: ;   {runtime_call StubRoutines (final stubs)}
  0x000002bed2970c94: 7405 e825 

  0x000002bed2970c98: ;   {metadata(method data for {method} {0x000002be8046ca78} 'doPrivileged' '(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970c98: 7d04 0748 | be30 ccd2 | e5be 0200 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 fe07 
  0x000002bed2970cb8: 0000 85ff | 0f84 ec02 | 0000 4889 

  0x000002bed2970cc4: ;   {metadata(method data for {method} {0x000002be8046ca78} 'doPrivileged' '(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970cc4: 5424 4048 | be30 ccd2 | e5be 0200 | 0048 8386 | 1001 0000 | 0166 0f1f 

  0x000002bed2970cdc: ;   {static_call}
  0x000002bed2970cdc: 4400 00e8 

  0x000002bed2970ce0: ; ImmutableOopMap {[64]=Oop }
                      ;*invokestatic getCallerClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::doPrivileged@2
  0x000002bed2970ce0: 7c44 5d07 

  0x000002bed2970ce4: ;   {other}
  0x000002bed2970ce4: 0f1f 8400 | 5402 0000 

  0x000002bed2970cec: ;   {metadata(method data for {method} {0x000002be8046ca78} 'doPrivileged' '(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970cec: 48ba 30cc | d2e5 be02 | 0000 4883 | 8220 0100 

  0x000002bed2970cfc: ;   {metadata(method data for {method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970cfc: 0001 48ba | e843 2ee5 | be02 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002bed2970d1c: 0085 f60f | 84aa 0200 

  0x000002bed2970d24: ;   {metadata(method data for {method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970d24: 0048 bae8 | 432e e5be | 0200 00ff | 8210 0100 

  0x000002bed2970d34: ;   {metadata(method data for {method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970d34: 0048 bae8 | 432e e5be | 0200 00ff | 8240 0100 | 0048 8944 | 2448 488b | 5424 4048 | 3b02 488b 
  0x000002bed2970d54: ;   {metadata(method data for {method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970d54: f248 bfe8 | 432e e5be | 0200 008b | 7608 49ba | 0000 0080 | be02 0000 | 4903 f248 | 3bb7 b001 
  0x000002bed2970d74: 0000 750d | 4883 87b8 | 0100 0001 | e960 0000 | 0048 3bb7 | c001 0000 | 750d 4883 | 87c8 0100 
  0x000002bed2970d94: 0001 e94a | 0000 0048 | 83bf b001 | 0000 0075 | 1748 89b7 | b001 0000 | 48c7 87b8 | 0100 0001 
  0x000002bed2970db4: 0000 00e9 | 2900 0000 | 4883 bfc0 | 0100 0000 | 7517 4889 | b7c0 0100 | 0048 c787 | c801 0000 
  0x000002bed2970dd4: 0100 0000 | e908 0000 | 0048 8387 | a001 0000 | 0148 b860 | a2a8 e9be 

  0x000002bed2970dec: ;   {virtual_call}
  0x000002bed2970dec: 0200 00e8 

  0x000002bed2970df0: ; ImmutableOopMap {[72]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::executePrivileged@29
                      ; - java.security.AccessController::doPrivileged@9
  0x000002bed2970df0: 1c6a 1107 

  0x000002bed2970df4: ;   {other}
  0x000002bed2970df4: 0f1f 8400 | 6403 0001 

  0x000002bed2970dfc: ;   {metadata(method data for {method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970dfc: 48ba e843 | 2ee5 be02 | 0000 ff82 | d801 0000 | 488b 5424 

  0x000002bed2970e10: ;   {metadata(method data for {method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970e10: 4848 bee8 | 432e e5be | 0200 0048 | 8386 3802 

  0x000002bed2970e20: ;   {metadata(method data for {method} {0x000002be8003d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002bed2970e20: 0000 0148 | bea0 df05 | e5be 0200 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 feff 
  0x000002bed2970e40: 1f00 85ff | 0f84 ab01 

  0x000002bed2970e48: ;   {metadata(method data for {method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970e48: 0000 48ba | e843 2ee5 | be02 0000 | 4883 8248 | 0200 0001 

  0x000002bed2970e5c: ;   {metadata(method data for {method} {0x000002be8003d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002bed2970e5c: 48ba a0df | 05e5 be02 | 0000 8bb2 | cc00 0000 | 83c6 0289 | b2cc 0000 | 0081 e6fe | ff1f 0085 
  0x000002bed2970e7c: f60f 8493 | 0100 0048 | 83c4 605d 

  0x000002bed2970e88: ;   {poll_return}
  0x000002bed2970e88: 493b a748 | 0400 000f | 87a2 0100 | 00c3 498b | 87f8 0400 | 004d 33d2 | 4d89 97f8 | 0400 004d 
  0x000002bed2970ea8: 33d2 4d89 | 9700 0500 | 004c 8bc0 

  0x000002bed2970eb4: ;   {metadata(method data for {method} {0x000002be8046ca78} 'doPrivileged' '(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970eb4: 48ba 30cc | d2e5 be02 | 0000 4883 | 8230 0100 

  0x000002bed2970ec4: ;   {metadata(method data for {method} {0x000002be8046c6e8} 'wrapException' '(Ljava/lang/Exception;)Ljava/security/PrivilegedActionException;' in 'java/security/AccessController')}
  0x000002bed2970ec4: 0001 48ba | 2801 3ae5 | be02 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002bed2970ee4: 0085 f60f | 8460 0100 | 0066 6690 

  0x000002bed2970ef0: ;   {no_reloc}
  0x000002bed2970ef0: e988 0100 | 0000 0000 | 0000 80ba | 2101 0000 | 040f 8585 | 0100 0049 | 8b87 b801 | 0000 488d 
  0x000002bed2970f10: 7828 493b | bfc8 0100 | 000f 876d | 0100 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca 
  0x000002bed2970f30: 49ba 0000 | 0080 be02 | 0000 492b | ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 1048 8948 
  0x000002bed2970f50: 1848 8948 | 2048 8bd0 

  0x000002bed2970f58: ;   {metadata(method data for {method} {0x000002be8046c6e8} 'wrapException' '(Ljava/lang/Exception;)Ljava/security/PrivilegedActionException;' in 'java/security/AccessController')}
  0x000002bed2970f58: 48be 2801 | 3ae5 be02 | 0000 4883 | 8610 0100 | 0001 488b | d048 8944 | 2450 0f1f 

  0x000002bed2970f74: ;   {optimized virtual_call}
  0x000002bed2970f74: 4400 00e8 

  0x000002bed2970f78: ; ImmutableOopMap {[80]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::wrapException@5
                      ; - java.security.AccessController::doPrivileged@18
  0x000002bed2970f78: 04d8 0507 

  0x000002bed2970f7c: ;   {other}
  0x000002bed2970f7c: 0f1f 8400 | ec04 0002 | 488b 4424 | 50e9 2801 | 0000 498b | 87f8 0400 | 004d 33d2 | 4d89 97f8 
  0x000002bed2970f9c: 0400 004d | 33d2 4d89 | 9700 0500 | 00e9 0801 

  0x000002bed2970fac: ;   {metadata({method} {0x000002be8046ca78} 'doPrivileged' '(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970fac: 0000 49ba | 70ca 4680 | be02 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002bed2970fc4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002bed2970fc4: ffe8 3609 

  0x000002bed2970fc8: ; ImmutableOopMap {rdx=Oop }
                      ;*synchronization entry
                      ; - java.security.AccessController::doPrivileged@-1
  0x000002bed2970fc8: 1107 e9f3 

  0x000002bed2970fcc: ;   {metadata({method} {0x000002be8046cdb0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002bed2970fcc: fcff ff49 | baa8 cd46 | 80be 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002bed2970fe4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002bed2970fe4: ffff e815 

  0x000002bed2970fe8: ; ImmutableOopMap {rax=Oop [64]=Oop }
                      ;*synchronization entry
                      ; - java.security.AccessController::executePrivileged@-1
                      ; - java.security.AccessController::doPrivileged@9
  0x000002bed2970fe8: 0911 07e9 | 35fd ffff 

  0x000002bed2970ff0: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002bed2970ff0: e88b b410 

  0x000002bed2970ff4: ; ImmutableOopMap {rdx=Oop [72]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::executePrivileged@29
                      ; - java.security.AccessController::doPrivileged@9
                      ;   {metadata({method} {0x000002be8003d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002bed2970ff4: 0749 ba48 | d003 80be | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002bed297100c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002bed297100c: e8ef 0811 

  0x000002bed2971010: ; ImmutableOopMap {rax=Oop rdx=Oop }
                      ;*synchronization entry
                      ; - java.lang.ref.Reference::reachabilityFence@-1
                      ; - java.security.AccessController::executePrivileged@56
                      ; - java.security.AccessController::doPrivileged@9
  0x000002bed2971010: 07e9 34fe 

  0x000002bed2971014: ;   {metadata({method} {0x000002be8003d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002bed2971014: ffff 49ba | 48d0 0380 | be02 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002bed297102c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002bed297102c: ffe8 ce08 

  0x000002bed2971030: ; ImmutableOopMap {rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.ref.Reference::reachabilityFence@-1
                      ; - java.security.AccessController::executePrivileged@60
                      ; - java.security.AccessController::doPrivileged@9
  0x000002bed2971030: 1107 e94c 

  0x000002bed2971034: ;   {internal_word}
  0x000002bed2971034: feff ff49 | ba88 0e97 | d2be 0200 | 004d 8997 | 6004 0000 

  0x000002bed2971048: ;   {runtime_call SafepointBlob}
  0x000002bed2971048: e933 3606 

  0x000002bed297104c: ;   {metadata({method} {0x000002be8046c6e8} 'wrapException' '(Ljava/lang/Exception;)Ljava/security/PrivilegedActionException;' in 'java/security/AccessController')}
  0x000002bed297104c: 0749 bae0 | c646 80be | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002bed2971064: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002bed2971064: e897 0811 

  0x000002bed2971068: ; ImmutableOopMap {r8=Oop }
                      ;*synchronization entry
                      ; - java.security.AccessController::wrapException@-1
                      ; - java.security.AccessController::doPrivileged@18
  0x000002bed2971068: 07e9 7ffe 

  0x000002bed297106c: ;   {metadata(nullptr)}
  0x000002bed297106c: ffff 48ba | 0000 0000 | 0000 0000 | b800 0f05 

  0x000002bed297107c: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002bed297107c: 0ae8 fef7 

  0x000002bed2971080: ; ImmutableOopMap {r8=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.security.AccessController::wrapException@0
                      ; - java.security.AccessController::doPrivileged@18
  0x000002bed2971080: 1007 e969 

  0x000002bed2971084: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002bed2971084: feff ffe8 

  0x000002bed2971088: ; ImmutableOopMap {r8=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::wrapException@0
                      ; - java.security.AccessController::doPrivileged@18
  0x000002bed2971088: f4b3 1007 

  0x000002bed297108c: ;   {runtime_call fast_new_instance_init_check Runtime1 stub}
  0x000002bed297108c: 488b d2e8 

  0x000002bed2971090: ; ImmutableOopMap {r8=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::wrapException@0
                      ; - java.security.AccessController::doPrivileged@18
  0x000002bed2971090: ecbf 1007 | e9bc feff | ff49 8b87 | f804 0000 | 49c7 87f8 | 0400 0000 | 0000 0049 | c787 0005 
  0x000002bed29710b0: 0000 0000 | 0000 4883 

  0x000002bed29710b8: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002bed29710b8: c460 5de9 | c0a4 1007 
[Stub Code]
  0x000002bed29710c0: ;   {no_reloc}
  0x000002bed29710c0: 0f1f 4400 

  0x000002bed29710c4: ;   {static_stub}
  0x000002bed29710c4: 0048 bb00 | 0000 0000 

  0x000002bed29710cc: ;   {runtime_call nmethod}
  0x000002bed29710cc: 0000 00e9 | fbff ffff 

  0x000002bed29710d4: ;   {static_stub}
  0x000002bed29710d4: 9048 bb00 | 0000 0000 

  0x000002bed29710dc: ;   {runtime_call nmethod}
  0x000002bed29710dc: 0000 00e9 | fbff ffff 

  0x000002bed29710e4: ;   {static_stub}
  0x000002bed29710e4: 48bb 0000 | 0000 0000 

  0x000002bed29710ec: ;   {runtime_call nmethod}
  0x000002bed29710ec: 0000 e9fb 

  0x000002bed29710f0: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002bed29710f0: ffff ffe8 | 88c5 1007 

  0x000002bed29710f8: ;   {external_word}
  0x000002bed29710f8: 48b9 907f | 1b80 fb7f | 0000 4883 

  0x000002bed2971104: ;   {runtime_call}
  0x000002bed2971104: e4f0 48b8 | f042 de7f | fb7f 0000 

  0x000002bed2971110: ;   {section_word}
  0x000002bed2971110: ffd0 f449 | ba13 1197 | d2be 0200 

  0x000002bed297111c: ;   {runtime_call DeoptimizationBlob}
  0x000002bed297111c: 0041 52e9 | 7c2e 0607 | f4f4 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002bee4b25400, length=18, elements={
0x000002becf24e730, 0x000002bee3579f40, 0x000002bee357af00, 0x000002bee357c9d0,
0x000002bee3586a40, 0x000002bee3587a60, 0x000002bee35884b0, 0x000002bee3589560,
0x000002bee359b9d0, 0x000002bee4368c80, 0x000002bee461b720, 0x000002bee4e6b460,
0x000002bee91d60d0, 0x000002bee91d6ff0, 0x000002bee90a5100, 0x000002bee92713f0,
0x000002bee93b93e0, 0x000002bee9364b00
}

Java Threads: ( => current thread )
  0x000002becf24e730 JavaThread "main"                              [_thread_blocked, id=7676, stack(0x000000ca8bd00000,0x000000ca8be00000) (1024K)]
  0x000002bee3579f40 JavaThread "Reference Handler"          daemon [_thread_blocked, id=960, stack(0x000000ca8c100000,0x000000ca8c200000) (1024K)]
  0x000002bee357af00 JavaThread "Finalizer"                  daemon [_thread_blocked, id=7552, stack(0x000000ca8c200000,0x000000ca8c300000) (1024K)]
  0x000002bee357c9d0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=4156, stack(0x000000ca8c300000,0x000000ca8c400000) (1024K)]
  0x000002bee3586a40 JavaThread "Attach Listener"            daemon [_thread_blocked, id=7200, stack(0x000000ca8c400000,0x000000ca8c500000) (1024K)]
  0x000002bee3587a60 JavaThread "Service Thread"             daemon [_thread_blocked, id=4652, stack(0x000000ca8c500000,0x000000ca8c600000) (1024K)]
  0x000002bee35884b0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=4792, stack(0x000000ca8c600000,0x000000ca8c700000) (1024K)]
  0x000002bee3589560 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=8136, stack(0x000000ca8c700000,0x000000ca8c800000) (1024K)]
  0x000002bee359b9d0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=6268, stack(0x000000ca8c800000,0x000000ca8c900000) (1024K)]
  0x000002bee4368c80 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=4648, stack(0x000000ca8c900000,0x000000ca8ca00000) (1024K)]
  0x000002bee461b720 JavaThread "Notification Thread"        daemon [_thread_blocked, id=2504, stack(0x000000ca8cb00000,0x000000ca8cc00000) (1024K)]
  0x000002bee4e6b460 JavaThread "Active Thread: Equinox Container: 215f94b8-713c-4ce6-a337-8810a4cbbf17"        [_thread_blocked, id=1284, stack(0x000000ca8cf00000,0x000000ca8d000000) (1024K)]
  0x000002bee91d60d0 JavaThread "Framework Event Dispatcher: Equinox Container: 215f94b8-713c-4ce6-a337-8810a4cbbf17" daemon [_thread_blocked, id=1828, stack(0x000000ca8d000000,0x000000ca8d100000) (1024K)]
=>0x000002bee91d6ff0 JavaThread "Start Level: Equinox Container: 215f94b8-713c-4ce6-a337-8810a4cbbf17" daemon [_thread_in_vm, id=7600, stack(0x000000ca8d100000,0x000000ca8d200000) (1024K)]
  0x000002bee90a5100 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=6240, stack(0x000000ca8d200000,0x000000ca8d300000) (1024K)]
  0x000002bee92713f0 JavaThread "Worker-JM"                         [_thread_blocked, id=7628, stack(0x000000ca8d400000,0x000000ca8d500000) (1024K)]
  0x000002bee93b93e0 JavaThread "Worker-0"                          [_thread_blocked, id=4944, stack(0x000000ca8d500000,0x000000ca8d600000) (1024K)]
  0x000002bee9364b00 JavaThread "Worker-1"                          [_thread_blocked, id=7704, stack(0x000000ca8d600000,0x000000ca8d700000) (1024K)]
Total: 18

Other Threads:
  0x000002becf30d270 VMThread "VM Thread"                           [id=7916, stack(0x000000ca8c000000,0x000000ca8c100000) (1024K)]
  0x000002bee352c9d0 WatcherThread "VM Periodic Task Thread"        [id=5160, stack(0x000000ca8bf00000,0x000000ca8c000000) (1024K)]
  0x000002becf26fbf0 WorkerThread "GC Thread#0"                     [id=3684, stack(0x000000ca8be00000,0x000000ca8bf00000) (1024K)]
  0x000002bee49f4f70 WorkerThread "GC Thread#1"                     [id=6028, stack(0x000000ca8cc00000,0x000000ca8cd00000) (1024K)]
  0x000002bee49f5df0 WorkerThread "GC Thread#2"                     [id=7752, stack(0x000000ca8cd00000,0x000000ca8ce00000) (1024K)]
  0x000002bee49f56b0 WorkerThread "GC Thread#3"                     [id=1156, stack(0x000000ca8ce00000,0x000000ca8cf00000) (1024K)]
Total: 6

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffb8046c300] Metaspace_lock - owner thread: 0x000002bee91d6ff0

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002be80000000-0x000002be80ba0000-0x000002be80ba0000), size 12189696, SharedBaseAddress: 0x000002be80000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002be81000000-0x000002bec1000000, reserved size: 1073741824
Narrow klass base: 0x000002be80000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 4 total, 4 available
 Memory: 4094M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 4

Heap:
 PSYoungGen      total 29184K, used 10508K [0x00000000eab00000, 0x00000000ed180000, 0x0000000100000000)
  eden space 23552K, 44% used [0x00000000eab00000,0x00000000eb543380,0x00000000ec200000)
  from space 5632K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece80000)
  to   space 7168K, 0% used [0x00000000ec200000,0x00000000ec200000,0x00000000ec900000)
 ParOldGen       total 68608K, used 10432K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 15% used [0x00000000c0000000,0x00000000c0a30020,0x00000000c4300000)
 Metaspace       used 22934K, committed 23488K, reserved 1114112K
  class space    used 2111K, committed 2368K, reserved 1048576K

Card table byte_map: [0x000002becec00000,0x000002becee10000] _byte_map_base: 0x000002bece600000

Marking Bits: (ParMarkBitMap*) 0x00007ffb80473260
 Begin Bits: [0x000002bee1450000, 0x000002bee2450000)
 End Bits:   [0x000002bee2450000, 0x000002bee3450000)

Polling page: 0x000002beccf80000

Metaspace:

Usage:
  Non-class:     20.34 MB used.
      Class:      2.06 MB used.
       Both:     22.40 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      20.63 MB ( 32%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      22.94 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  10.81 MB
       Class:  13.57 MB
        Both:  24.38 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 472.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 367.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1264.
num_chunk_merges: 6.
num_chunk_splits: 853.
num_chunks_enlarged: 567.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1259Kb max_used=1259Kb free=118740Kb
 bounds [0x000002bed9f20000, 0x000002beda190000, 0x000002bee1450000]
CodeHeap 'profiled nmethods': size=120000Kb used=5960Kb max_used=5960Kb free=114040Kb
 bounds [0x000002bed2450000, 0x000002bed2a30000, 0x000002bed9980000]
CodeHeap 'non-nmethods': size=5760Kb used=1312Kb max_used=1346Kb free=4447Kb
 bounds [0x000002bed9980000, 0x000002bed9bf0000, 0x000002bed9f20000]
 total_blobs=3532 nmethods=2934 adapters=505
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 12.287 Thread 0x000002bee359b9d0 nmethod 2929 0x000002bed2a1f290 code [0x000002bed2a1f420, 0x000002bed2a1f588]
Event: 12.287 Thread 0x000002bee359b9d0 2928       1       com.sun.org.apache.xerces.internal.dom.ParentNode::ownerDocument (5 bytes)
Event: 12.287 Thread 0x000002bee359b9d0 nmethod 2928 0x000002beda055d10 code [0x000002beda055ea0, 0x000002beda055f68]
Event: 12.287 Thread 0x000002bee359b9d0 2930       3       java.util.ArrayList::add (65 bytes)
Event: 12.288 Thread 0x000002bee359b9d0 nmethod 2930 0x000002bed2a1f610 code [0x000002bed2a1f820, 0x000002bed2a1fe88]
Event: 12.288 Thread 0x000002bee359b9d0 2931       3       com.sun.org.apache.xerces.internal.dom.AttrImpl::getNodeName (16 bytes)
Event: 12.289 Thread 0x000002bee359b9d0 nmethod 2931 0x000002bed2a20110 code [0x000002bed2a202c0, 0x000002bed2a20608]
Event: 12.289 Thread 0x000002bee359b9d0 2932       3       com.sun.org.apache.xerces.internal.util.XMLChar::isNameStart (22 bytes)
Event: 12.289 Thread 0x000002bee359b9d0 nmethod 2932 0x000002bed2a20710 code [0x000002bed2a208c0, 0x000002bed2a20a68]
Event: 12.289 Thread 0x000002bee359b9d0 2933       3       com.sun.org.apache.xerces.internal.dom.NodeImpl::isNormalized (17 bytes)
Event: 12.289 Thread 0x000002bee359b9d0 nmethod 2933 0x000002bed2a20b10 code [0x000002bed2a20ca0, 0x000002bed2a20e08]
Event: 12.289 Thread 0x000002bee359b9d0 2934       1       com.sun.org.apache.xerces.internal.dom.DocumentImpl::getMutationEvents (5 bytes)
Event: 12.289 Thread 0x000002bee359b9d0 nmethod 2934 0x000002beda056010 code [0x000002beda0561a0, 0x000002beda056270]
Event: 12.307 Thread 0x000002bee359b9d0 2935       3       jdk.internal.misc.Unsafe::weakCompareAndSetReference (11 bytes)
Event: 12.307 Thread 0x000002bee359b9d0 nmethod 2935 0x000002bed2a20e90 code [0x000002bed2a21020, 0x000002bed2a21160]
Event: 12.350 Thread 0x000002bee3589560 nmethod 2903 0x000002beda056310 code [0x000002beda056700, 0x000002beda058940]
Event: 12.539 Thread 0x000002bee359b9d0 2936       3       jdk.xml.internal.SecuritySupport$$Lambda/0x000002be810c7d78::run (8 bytes)
Event: 12.540 Thread 0x000002bee359b9d0 nmethod 2936 0x000002bed2a21210 code [0x000002bed2a21400, 0x000002bed2a217c8]
Event: 12.540 Thread 0x000002bee359b9d0 2937       3       jdk.xml.internal.SecuritySupport::lambda$getSystemProperty$0 (5 bytes)
Event: 12.540 Thread 0x000002bee359b9d0 nmethod 2937 0x000002bed2a21990 code [0x000002bed2a21b60, 0x000002bed2a21eb8]

GC Heap History (14 events):
Event: 1.213 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4322K, committed 4480K, reserved 1114112K
  class space    used 437K, committed 512K, reserved 1048576K
}
Event: 1.223 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3222K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec725800,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 4322K, committed 4480K, reserved 1114112K
  class space    used 437K, committed 512K, reserved 1048576K
}
Event: 2.079 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28822K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec725800,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 8210K, committed 8512K, reserved 1114112K
  class space    used 814K, committed 960K, reserved 1048576K
}
Event: 2.091 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4091K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfefd0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 854K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c00d5b08,0x00000000c4300000)
 Metaspace       used 8210K, committed 8512K, reserved 1114112K
  class space    used 814K, committed 960K, reserved 1048576K
}
Event: 2.939 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29691K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfefd0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 854K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c00d5b08,0x00000000c4300000)
 Metaspace       used 13015K, committed 13568K, reserved 1114112K
  class space    used 1310K, committed 1536K, reserved 1048576K
}
Event: 2.949 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4085K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fd540,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 2726K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 3% used [0x00000000c0000000,0x00000000c02a9b58,0x00000000c4300000)
 Metaspace       used 13015K, committed 13568K, reserved 1114112K
  class space    used 1310K, committed 1536K, reserved 1048576K
}
Event: 3.600 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29685K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fd540,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 2726K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 3% used [0x00000000c0000000,0x00000000c02a9b58,0x00000000c4300000)
 Metaspace       used 15951K, committed 16576K, reserved 1114112K
  class space    used 1589K, committed 1856K, reserved 1048576K
}
Event: 3.613 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4080K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfc228,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 5624K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c057e3a8,0x00000000c4300000)
 Metaspace       used 15951K, committed 16576K, reserved 1114112K
  class space    used 1589K, committed 1856K, reserved 1048576K
}
Event: 4.174 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29172K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 98% used [0x00000000eab00000,0x00000000ec381048,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfc228,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 5624K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c057e3a8,0x00000000c4300000)
 Metaspace       used 20144K, committed 20800K, reserved 1114112K
  class space    used 1891K, committed 2176K, reserved 1048576K
}
Event: 4.180 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4078K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fbae8,0x00000000ec800000)
  to   space 5632K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece80000)
 ParOldGen       total 68608K, used 5894K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c05c1b88,0x00000000c4300000)
 Metaspace       used 20144K, committed 20800K, reserved 1114112K
  class space    used 1891K, committed 2176K, reserved 1048576K
}
Event: 4.630 GC heap before
{Heap before GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 24658K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 25088K, 82% used [0x00000000eab00000,0x00000000ebf190b8,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fbae8,0x00000000ec800000)
  to   space 5632K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece80000)
 ParOldGen       total 68608K, used 5894K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c05c1b88,0x00000000c4300000)
 Metaspace       used 20945K, committed 21504K, reserved 1114112K
  class space    used 1929K, committed 2176K, reserved 1048576K
}
Event: 4.646 GC heap after
{Heap after GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 5605K [0x00000000eab00000, 0x00000000ed180000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec200000)
  from space 5632K, 99% used [0x00000000ec900000,0x00000000ece79588,0x00000000ece80000)
  to   space 7168K, 0% used [0x00000000ec200000,0x00000000ec200000,0x00000000ec900000)
 ParOldGen       total 68608K, used 6771K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c069cea0,0x00000000c4300000)
 Metaspace       used 20945K, committed 21504K, reserved 1114112K
  class space    used 1929K, committed 2176K, reserved 1048576K
}
Event: 4.646 GC heap before
{Heap before GC invocations=7 (full 1):
 PSYoungGen      total 29184K, used 5605K [0x00000000eab00000, 0x00000000ed180000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec200000)
  from space 5632K, 99% used [0x00000000ec900000,0x00000000ece79588,0x00000000ece80000)
  to   space 7168K, 0% used [0x00000000ec200000,0x00000000ec200000,0x00000000ec900000)
 ParOldGen       total 68608K, used 6771K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c069cea0,0x00000000c4300000)
 Metaspace       used 20945K, committed 21504K, reserved 1114112K
  class space    used 1929K, committed 2176K, reserved 1048576K
}
Event: 4.696 GC heap after
{Heap after GC invocations=7 (full 1):
 PSYoungGen      total 29184K, used 0K [0x00000000eab00000, 0x00000000ed180000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec200000)
  from space 5632K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece80000)
  to   space 7168K, 0% used [0x00000000ec200000,0x00000000ec200000,0x00000000ec900000)
 ParOldGen       total 68608K, used 10432K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 15% used [0x00000000c0000000,0x00000000c0a30020,0x00000000c4300000)
 Metaspace       used 20932K, committed 21504K, reserved 1114112K
  class space    used 1925K, committed 2176K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.020 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 0.160 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.206 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 0.213 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 0.215 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 0.221 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.249 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 0.391 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 2.006 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 2.937 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
Event: 2.996 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT PACKING pc=0x000002bed9fae684 sp=0x000000ca8d1fd990
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT UNPACKING pc=0x000002bed99d3a9c sp=0x000000ca8d1fd880 mode 2
Event: 4.781 Thread 0x000002bee91d6ff0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002bed9fae684 relative=0x0000000000000984
Event: 4.781 Thread 0x000002bee91d6ff0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002bed9fae684 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 56 c2
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT PACKING pc=0x000002bed9fae684 sp=0x000000ca8d1fd990
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT UNPACKING pc=0x000002bed99d3a9c sp=0x000000ca8d1fd880 mode 2
Event: 4.781 Thread 0x000002bee91d6ff0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002bed9fae6b4 relative=0x00000000000009b4
Event: 4.781 Thread 0x000002bee91d6ff0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002bed9fae6b4 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 152 c2
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT PACKING pc=0x000002bed9fae6b4 sp=0x000000ca8d1fd990
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT UNPACKING pc=0x000002bed99d3a9c sp=0x000000ca8d1fd888 mode 2
Event: 4.781 Thread 0x000002bee91d6ff0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002bed9fae684 relative=0x0000000000000984
Event: 4.781 Thread 0x000002bee91d6ff0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002bed9fae684 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 56 c2
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT PACKING pc=0x000002bed9fae684 sp=0x000000ca8d1fd990
Event: 4.781 Thread 0x000002bee91d6ff0 DEOPT UNPACKING pc=0x000002bed99d3a9c sp=0x000000ca8d1fd880 mode 2
Event: 5.121 Thread 0x000002bee91d6ff0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002beda01f93c relative=0x00000000000009bc
Event: 5.121 Thread 0x000002bee91d6ff0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002beda01f93c method=java.util.regex.Pattern$BmpCharPropertyGreedy.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 12 c2
Event: 5.121 Thread 0x000002bee91d6ff0 DEOPT PACKING pc=0x000002beda01f93c sp=0x000000ca8d1fd620
Event: 5.121 Thread 0x000002bee91d6ff0 DEOPT UNPACKING pc=0x000002bed99d3a9c sp=0x000000ca8d1fd4e8 mode 2
Event: 11.185 Thread 0x000002bee91d6ff0 DEOPT PACKING pc=0x000002bed29e6493 sp=0x000000ca8d1fcbc0
Event: 11.185 Thread 0x000002bee91d6ff0 DEOPT UNPACKING pc=0x000002bed99d4223 sp=0x000000ca8d1fc058 mode 0

Classes loaded (20 events):
Event: 12.722 Loading class com/sun/org/apache/xml/internal/serializer/SerializerBase
Event: 12.723 Loading class com/sun/org/apache/xml/internal/serializer/SerializationHandler
Event: 12.723 Loading class com/sun/org/apache/xml/internal/serializer/ExtendedContentHandler
Event: 12.738 Loading class com/sun/org/apache/xml/internal/serializer/ExtendedContentHandler done
Event: 12.738 Loading class com/sun/org/apache/xml/internal/serializer/ExtendedLexicalHandler
Event: 12.738 Loading class org/xml/sax/ext/LexicalHandler
Event: 12.738 Loading class org/xml/sax/ext/LexicalHandler done
Event: 12.738 Loading class com/sun/org/apache/xml/internal/serializer/ExtendedLexicalHandler done
Event: 12.738 Loading class com/sun/org/apache/xml/internal/serializer/XSLOutputAttributes
Event: 12.738 Loading class com/sun/org/apache/xml/internal/serializer/XSLOutputAttributes done
Event: 12.738 Loading class org/xml/sax/ext/DeclHandler
Event: 12.739 Loading class org/xml/sax/ext/DeclHandler done
Event: 12.739 Loading class com/sun/org/apache/xml/internal/serializer/DOMSerializer
Event: 12.739 Loading class com/sun/org/apache/xml/internal/serializer/DOMSerializer done
Event: 12.739 Loading class com/sun/org/apache/xml/internal/serializer/Serializer
Event: 12.739 Loading class com/sun/org/apache/xml/internal/serializer/Serializer done
Event: 12.739 Loading class com/sun/org/apache/xml/internal/serializer/SerializationHandler done
Event: 12.739 Loading class com/sun/org/apache/xml/internal/serializer/SerializerConstants
Event: 12.739 Loading class com/sun/org/apache/xml/internal/serializer/SerializerConstants done
Event: 12.740 Loading class com/sun/org/apache/xml/internal/serializer/SerializerBase done

Classes unloaded (7 events):
Event: 4.658 Thread 0x000002becf30d270 Unloading class 0x000002be81184c00 'java/lang/invoke/LambdaForm$MH+0x000002be81184c00'
Event: 4.658 Thread 0x000002becf30d270 Unloading class 0x000002be81184800 'java/lang/invoke/LambdaForm$MH+0x000002be81184800'
Event: 4.658 Thread 0x000002becf30d270 Unloading class 0x000002be81184400 'java/lang/invoke/LambdaForm$MH+0x000002be81184400'
Event: 4.658 Thread 0x000002becf30d270 Unloading class 0x000002be81184000 'java/lang/invoke/LambdaForm$MH+0x000002be81184000'
Event: 4.658 Thread 0x000002becf30d270 Unloading class 0x000002be81183c00 'java/lang/invoke/LambdaForm$BMH+0x000002be81183c00'
Event: 4.658 Thread 0x000002becf30d270 Unloading class 0x000002be81183800 'java/lang/invoke/LambdaForm$DMH+0x000002be81183800'
Event: 4.658 Thread 0x000002becf30d270 Unloading class 0x000002be81182400 'java/lang/invoke/LambdaForm$DMH+0x000002be81182400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3.365 Thread 0x000002bee93b93e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb21e680}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eb21e680) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.396 Thread 0x000002bee91d6ff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb3fa868}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb3fa868) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.460 Thread 0x000002bee91d6ff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb6cd440}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000eb6cd440) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.461 Thread 0x000002bee91d6ff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb6d0b00}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eb6d0b00) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.644 Thread 0x000002bee91d6ff0 Exception <a 'java/io/FileNotFoundException'{0x00000000eae32e08}> (0x00000000eae32e08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.644 Thread 0x000002bee91d6ff0 Exception <a 'java/io/FileNotFoundException'{0x00000000eae33c28}> (0x00000000eae33c28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.644 Thread 0x000002bee91d6ff0 Exception <a 'java/io/FileNotFoundException'{0x00000000eae34b50}> (0x00000000eae34b50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.905 Thread 0x000002bee91d6ff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb7c11f0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb7c11f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.035 Thread 0x000002bee91d6ff0 Implicit null exception at 0x000002beda020ef6 to 0x000002beda020fc8
Event: 4.183 Thread 0x000002bee91d6ff0 Exception <a 'java/io/FileNotFoundException'{0x00000000eabcf390}> (0x00000000eabcf390) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.184 Thread 0x000002bee91d6ff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabd6bf8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eabd6bf8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.316 Thread 0x000002bee91d6ff0 Exception <a 'java/io/FileNotFoundException'{0x00000000eb6c31d0}> (0x00000000eb6c31d0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.551 Thread 0x000002bee91d6ff0 Exception <a 'java/io/FileNotFoundException'{0x00000000ebdba400}> (0x00000000ebdba400) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.963 Thread 0x000002bee91d6ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac2ecb8}> (0x00000000eac2ecb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.964 Thread 0x000002bee91d6ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac328c0}> (0x00000000eac328c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 5.194 Thread 0x000002bee91d6ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eacfd4f8}> (0x00000000eacfd4f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 5.950 Thread 0x000002bee91d6ff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eadedd90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x00000000eadedd90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 11.501 Thread 0x000002bee91d6ff0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf38580}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x00000000eaf38580) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 12.005 Thread 0x000002bee91d6ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb246bb0}> (0x00000000eb246bb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 12.006 Thread 0x000002bee91d6ff0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb24eeb8}> (0x00000000eb24eeb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 4.458 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.458 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.629 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 4.696 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 4.780 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.780 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.781 Executing VM operation: Cleanup
Event: 5.781 Executing VM operation: Cleanup done
Event: 6.782 Executing VM operation: Cleanup
Event: 6.782 Executing VM operation: Cleanup done
Event: 8.783 Executing VM operation: Cleanup
Event: 8.783 Executing VM operation: Cleanup done
Event: 11.537 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 11.537 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 11.537 Executing VM operation: Cleanup
Event: 11.537 Executing VM operation: Cleanup done
Event: 11.538 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 11.538 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.539 Executing VM operation: Cleanup
Event: 12.539 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.083 Thread 0x000002becf24e730 Thread added: 0x000002bee359b9d0
Event: 0.134 Thread 0x000002becf24e730 Thread added: 0x000002bee4368c80
Event: 0.381 Thread 0x000002bee359b9d0 Thread added: 0x000002bee4555880
Event: 0.512 Thread 0x000002becf24e730 Thread added: 0x000002bee461b720
Event: 1.735 Thread 0x000002becf24e730 Thread added: 0x000002bee4e6b460
Event: 1.984 Thread 0x000002becf24e730 Thread added: 0x000002bee91d60d0
Event: 1.996 Thread 0x000002becf24e730 Thread added: 0x000002bee91d6ff0
Event: 2.049 Thread 0x000002bee91d6ff0 Thread added: 0x000002bee90a5100
Event: 2.211 Thread 0x000002bee91d6ff0 Thread added: 0x000002bee91a0260
Event: 2.636 Thread 0x000002bee91d6ff0 Thread added: 0x000002bee92713f0
Event: 2.709 Thread 0x000002bee4555880 Thread exited: 0x000002bee4555880
Event: 3.325 Thread 0x000002bee91d6ff0 Thread added: 0x000002bee93b93e0
Event: 3.366 Thread 0x000002bee91d6ff0 Thread added: 0x000002bee9364b00
Event: 3.588 Thread 0x000002bee359b9d0 Thread added: 0x000002bee94d40b0
Event: 3.779 Thread 0x000002bee94d40b0 Thread exited: 0x000002bee94d40b0
Event: 4.350 Thread 0x000002bee359b9d0 Thread added: 0x000002bee95f99c0
Event: 4.537 Thread 0x000002bee91d6ff0 Thread added: 0x000002beea590650
Event: 4.877 Thread 0x000002bee95f99c0 Thread exited: 0x000002bee95f99c0
Event: 5.814 Thread 0x000002beea590650 Thread exited: 0x000002beea590650
Event: 8.412 Thread 0x000002bee91a0260 Thread exited: 0x000002bee91a0260


Dynamic libraries:
0x00007ff75f730000 - 0x00007ff75f73e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffbb9f90000 - 0x00007ffbba170000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffbb7970000 - 0x00007ffbb7a1e000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffbb6eb0000 - 0x00007ffbb7116000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffbb7310000 - 0x00007ffbb7406000 	C:\Windows\System32\ucrtbase.dll
0x00007ffbb1120000 - 0x00007ffbb1138000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffbb7f20000 - 0x00007ffbb80af000 	C:\Windows\System32\USER32.dll
0x00007ffbb72f0000 - 0x00007ffbb7310000 	C:\Windows\System32\win32u.dll
0x00007ffbb7a20000 - 0x00007ffbb7a48000 	C:\Windows\System32\GDI32.dll
0x00007ffbb6430000 - 0x00007ffbb65c4000 	C:\Windows\System32\gdi32full.dll
0x00007ffbb6390000 - 0x00007ffbb642b000 	C:\Windows\System32\msvcp_win.dll
0x00007ffbb0b40000 - 0x00007ffbb0b5e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffbafe00000 - 0x00007ffbb0069000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f\COMCTL32.dll
0x00007ffbb9ec0000 - 0x00007ffbb9f5d000 	C:\Windows\System32\msvcrt.dll
0x00007ffbb8570000 - 0x00007ffbb8878000 	C:\Windows\System32\combase.dll
0x00007ffbb7460000 - 0x00007ffbb757f000 	C:\Windows\System32\RPCRT4.dll
0x00007ffbb65d0000 - 0x00007ffbb6642000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffbb7780000 - 0x00007ffbb77ad000 	C:\Windows\System32\IMM32.DLL
0x00007ffbb1640000 - 0x00007ffbb164c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffbaace0000 - 0x00007ffbaad6d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ffb7f7c0000 - 0x00007ffb80550000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffbb8880000 - 0x00007ffbb8921000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffbb8510000 - 0x00007ffbb856b000 	C:\Windows\System32\sechost.dll
0x00007ffbb62e0000 - 0x00007ffbb632c000 	C:\Windows\System32\POWRPROF.dll
0x00007ffbb7810000 - 0x00007ffbb787c000 	C:\Windows\System32\WS2_32.dll
0x00007ffbb1a90000 - 0x00007ffbb1a9a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffbb47c0000 - 0x00007ffbb47e3000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffbb4730000 - 0x00007ffbb475a000 	C:\Windows\SYSTEM32\WINMMBASE.dll
0x00007ffbb7410000 - 0x00007ffbb745a000 	C:\Windows\System32\cfgmgr32.dll
0x00007ffbb6370000 - 0x00007ffbb6381000 	C:\Windows\System32\kernel.appcore.dll
0x00007ffbb15f0000 - 0x00007ffbb15fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffb9e7e0000 - 0x00007ffb9e9a8000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffbaf590000 - 0x00007ffbaf5b9000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffbb1110000 - 0x00007ffbb111f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffbb0600000 - 0x00007ffbb061f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffbb8930000 - 0x00007ffbb9d67000 	C:\Windows\System32\SHELL32.dll
0x00007ffbb8340000 - 0x00007ffbb83e6000 	C:\Windows\System32\shcore.dll
0x00007ffbb66b0000 - 0x00007ffbb6df7000 	C:\Windows\System32\windows.storage.dll
0x00007ffbb7720000 - 0x00007ffbb7771000 	C:\Windows\System32\shlwapi.dll
0x00007ffbb6330000 - 0x00007ffbb634b000 	C:\Windows\System32\profapi.dll
0x00007ffbb0350000 - 0x00007ffbb0368000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffbb05f0000 - 0x00007ffbb0600000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffbb0260000 - 0x00007ffbb033e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffbb5b10000 - 0x00007ffbb5b76000 	C:\Windows\system32\mswsock.dll
0x00007ffbab5b0000 - 0x00007ffbab5c6000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffbb0340000 - 0x00007ffbb0350000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffba59b0000 - 0x00007ffba59f5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffbb9d70000 - 0x00007ffbb9eb9000 	C:\Windows\System32\ole32.dll
0x00007ffbab3d0000 - 0x00007ffbab3da000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
0x00007ffbab3c0000 - 0x00007ffbab3cb000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll
0x00007ffbb82d0000 - 0x00007ffbb82d8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffbb5ce0000 - 0x00007ffbb5cf7000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffbb5720000 - 0x00007ffbb5753000 	C:\Windows\system32\rsaenh.dll
0x00007ffbb5de0000 - 0x00007ffbb5e05000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffbb61e0000 - 0x00007ffbb6209000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffbb5cd0000 - 0x00007ffbb5cdb000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffbb58a0000 - 0x00007ffbb58d9000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffbb80b0000 - 0x00007ffbb80b8000 	C:\Windows\System32\NSI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-8e4fb7c55867a198edc70155b3d76505-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17.0.14+7
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\python313\Scripts\;C:\python313\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\ffmpeg-master-latest-win64-gpl;C:\ffmpeg\bin;C:\Program Files\Git\cmd;C:\Windows\System32;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\java\jdk-24;C:\Program Files\Java\jdk-24\bin;C:\src\flutter\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;C:\dart-sdk\bin;C:\Program Files\Java\jdk-17.0.14+7\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=snk
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 23 Stepping 7, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 16299 (10.0.16299.15)
OS uptime: 0 days 1:16 hours

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 23 stepping 7 microcode 0x70b, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, tsc, clflush
Processor Information for the first 4 processors :
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500

Memory: 4k page, system-wide physical 4094M (738M free)
TotalPageFile size 4094M (AvailPageFile size 161M)
current process WorkingSet (physical memory assigned to process): 137M, peak: 153M
current process commit charge ("private bytes"): 225M, peak: 243M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
