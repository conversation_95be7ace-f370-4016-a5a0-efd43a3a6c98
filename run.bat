@echo off
echo ========================================
echo       VPN Gaming - Android App
echo ========================================
echo.

echo تحقق من متطلبات النظام...
where gradle >nul 2>nul
if %errorlevel% neq 0 (
    echo خطأ: Gradle غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Gradle أو استخدام Android Studio
    pause
    exit /b 1
)

echo تنظيف المشروع...
call gradlew clean

echo بناء المشروع...
call gradlew build

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo تم بناء المشروع بنجاح!
    echo ========================================
    echo.
    echo لتشغيل التطبيق:
    echo 1. افتح Android Studio
    echo 2. اربط جهاز Android أو شغل المحاكي
    echo 3. اضغط على زر Run
    echo.
    echo أو استخدم الأمر:
    echo gradlew installDebug
    echo.
) else (
    echo.
    echo ========================================
    echo فشل في بناء المشروع!
    echo ========================================
    echo يرجى التحقق من الأخطاء أعلاه
    echo.
)

pause
