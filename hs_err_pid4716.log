#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1584816 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=4716, tid=7704
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-67b775b5290b748a41f444560a4660ae-sock

Host: Intel(R) Core(TM)2 Quad  CPU   Q9300  @ 2.50GHz, 4 cores, 3G,  Windows 10 , 64 bit Build 16299 (10.0.16299.15)
Time: Sun May 25 14:13:33 2025 Central Europe Daylight Time elapsed time: 19.761379 seconds (0d 0h 0m 19s)

---------------  T H R E A D  ---------------

Current thread (0x00000284e4ac3810):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=7704, stack(0x0000009f5c900000,0x0000009f5ca00000) (1024K)]


Current CompileTask:
C2:19761 2594       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)

Stack: [0x0000009f5c900000,0x0000009f5ca00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0xc500d]
V  [jvm.dll+0xc5543]
V  [jvm.dll+0x3b6752]
V  [jvm.dll+0x382935]
V  [jvm.dll+0x381d9a]
V  [jvm.dll+0x2479f0]
V  [jvm.dll+0x246fcf]
V  [jvm.dll+0x1c75ee]
V  [jvm.dll+0x25685a]
V  [jvm.dll+0x254dfa]
V  [jvm.dll+0x3f0256]
V  [jvm.dll+0x851f8b]
V  [jvm.dll+0x6cc5ed]
C  [ucrtbase.dll+0x1d885]
C  [KERNEL32.DLL+0x11fe4]
C  [ntdll.dll+0x6ef91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000284e5c10750, length=20, elements={
0x00000284d0762d60, 0x00000284e4a89060, 0x00000284e4a8ace0, 0x00000284e4a8ec00,
0x00000284e4a91bd0, 0x00000284e4a934f0, 0x00000284e4a948a0, 0x00000284e4ac3810,
0x00000284e4ac8510, 0x00000284e4a94f30, 0x00000284e4a962e0, 0x00000284e4a94210,
0x00000284e4a955c0, 0x00000284e4a95c50, 0x00000284e4a93b80, 0x00000284ea65f1e0,
0x00000284ea65f870, 0x00000284ea662cf0, 0x00000284ea65c3f0, 0x00000284eb7e0fc0
}

Java Threads: ( => current thread )
  0x00000284d0762d60 JavaThread "main"                              [_thread_blocked, id=6716, stack(0x0000009f5bf00000,0x0000009f5c000000) (1024K)]
  0x00000284e4a89060 JavaThread "Reference Handler"          daemon [_thread_blocked, id=6956, stack(0x0000009f5c300000,0x0000009f5c400000) (1024K)]
  0x00000284e4a8ace0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=8120, stack(0x0000009f5c400000,0x0000009f5c500000) (1024K)]
  0x00000284e4a8ec00 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=8144, stack(0x0000009f5c500000,0x0000009f5c600000) (1024K)]
  0x00000284e4a91bd0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=4944, stack(0x0000009f5c600000,0x0000009f5c700000) (1024K)]
  0x00000284e4a934f0 JavaThread "Service Thread"             daemon [_thread_blocked, id=228, stack(0x0000009f5c700000,0x0000009f5c800000) (1024K)]
  0x00000284e4a948a0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=3732, stack(0x0000009f5c800000,0x0000009f5c900000) (1024K)]
=>0x00000284e4ac3810 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=7704, stack(0x0000009f5c900000,0x0000009f5ca00000) (1024K)]
  0x00000284e4ac8510 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=7884, stack(0x0000009f5ca00000,0x0000009f5cb00000) (1024K)]
  0x00000284e4a94f30 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=6788, stack(0x0000009f5cb00000,0x0000009f5cc00000) (1024K)]
  0x00000284e4a962e0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=1148, stack(0x0000009f5ce00000,0x0000009f5cf00000) (1024K)]
  0x00000284e4a94210 JavaThread "Active Thread: Equinox Container: df3b818b-142a-4060-a735-7d98a95b81b5"        [_thread_blocked, id=6656, stack(0x0000009f5d500000,0x0000009f5d600000) (1024K)]
  0x00000284e4a955c0 JavaThread "Framework Event Dispatcher: Equinox Container: df3b818b-142a-4060-a735-7d98a95b81b5" daemon [_thread_blocked, id=1956, stack(0x0000009f5d600000,0x0000009f5d700000) (1024K)]
  0x00000284e4a95c50 JavaThread "Start Level: Equinox Container: df3b818b-142a-4060-a735-7d98a95b81b5" daemon [_thread_blocked, id=7564, stack(0x0000009f5d700000,0x0000009f5d800000) (1024K)]
  0x00000284e4a93b80 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=7344, stack(0x0000009f5d800000,0x0000009f5d900000) (1024K)]
  0x00000284ea65f1e0 JavaThread "Worker-JM"                         [_thread_blocked, id=5916, stack(0x0000009f5db00000,0x0000009f5dc00000) (1024K)]
  0x00000284ea65f870 JavaThread "Worker-0"                          [_thread_blocked, id=1204, stack(0x0000009f5dc00000,0x0000009f5dd00000) (1024K)]
  0x00000284ea662cf0 JavaThread "Worker-1"                          [_thread_blocked, id=4908, stack(0x0000009f5dd00000,0x0000009f5de00000) (1024K)]
  0x00000284ea65c3f0 JavaThread "Java indexing"              daemon [_thread_blocked, id=7464, stack(0x0000009f5e100000,0x0000009f5e200000) (1024K)]
  0x00000284eb7e0fc0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=7492, stack(0x0000009f5e200000,0x0000009f5e300000) (1024K)]
Total: 20

Other Threads:
  0x00000284d08212a0 VMThread "VM Thread"                           [id=4564, stack(0x0000009f5c200000,0x0000009f5c300000) (1024K)]
  0x00000284d07cf170 WatcherThread "VM Periodic Task Thread"        [id=7536, stack(0x0000009f5c100000,0x0000009f5c200000) (1024K)]
  0x00000284d0780fa0 WorkerThread "GC Thread#0"                     [id=4940, stack(0x0000009f5c000000,0x0000009f5c100000) (1024K)]
  0x00000284ea4ccb50 WorkerThread "GC Thread#1"                     [id=2016, stack(0x0000009f5d100000,0x0000009f5d200000) (1024K)]
  0x00000284ea4ec2c0 WorkerThread "GC Thread#2"                     [id=4444, stack(0x0000009f5d200000,0x0000009f5d300000) (1024K)]
  0x00000284ea0a4b00 WorkerThread "GC Thread#3"                     [id=4440, stack(0x0000009f5d300000,0x0000009f5d400000) (1024K)]
Total: 6

Threads with active compile tasks:
C2 CompilerThread0  20930 2594       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)
C2 CompilerThread1  20930 2757   !   4       java.util.concurrent.ConcurrentHashMap::transfer (849 bytes)
Total: 2

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffb8046e380] Threads_lock - owner thread: 0x00000284d08212a0
[0x00007ffb8046e480] Heap_lock - owner thread: 0x00000284e4a95c50

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000028480000000-0x0000028480ba0000-0x0000028480ba0000), size 12189696, SharedBaseAddress: 0x0000028480000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000028481000000-0x00000284c1000000, reserved size: 1073741824
Narrow klass base: 0x0000028480000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 4 total, 4 available
 Memory: 4094M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 4

Heap:
 PSYoungGen      total 30720K, used 5608K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 5632K, 99% used [0x00000000ec900000,0x00000000ece7a068,0x00000000ece80000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 6385K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c063c468,0x00000000c4300000)
 Metaspace       used 20939K, committed 21504K, reserved 1114112K
  class space    used 1928K, committed 2176K, reserved 1048576K

Card table byte_map: [0x00000284d0110000,0x00000284d0320000] _byte_map_base: 0x00000284cfb10000

Marking Bits: (ParMarkBitMap*) 0x00007ffb80473260
 Begin Bits: [0x00000284e2960000, 0x00000284e3960000)
 End Bits:   [0x00000284e3960000, 0x00000284e4960000)

Polling page: 0x00000284ce750000

Metaspace:

Usage:
  Non-class:     18.57 MB used.
      Class:      1.88 MB used.
       Both:     20.45 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      18.88 MB ( 29%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.13 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      21.00 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  12.39 MB
       Class:  13.91 MB
        Both:  26.29 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 466.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 336.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3.
num_chunks_taken_from_freelist: 1217.
num_chunk_merges: 0.
num_chunk_splits: 827.
num_chunks_enlarged: 556.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1181Kb max_used=1181Kb free=118818Kb
 bounds [0x00000284db430000, 0x00000284db6a0000, 0x00000284e2960000]
CodeHeap 'profiled nmethods': size=120000Kb used=5517Kb max_used=5517Kb free=114482Kb
 bounds [0x00000284d3960000, 0x00000284d3ed0000, 0x00000284dae90000]
CodeHeap 'non-nmethods': size=5760Kb used=1303Kb max_used=1320Kb free=4456Kb
 bounds [0x00000284dae90000, 0x00000284db100000, 0x00000284db430000]
 total_blobs=3344 nmethods=2756 adapters=495
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 19.498 Thread 0x00000284e4ac8510 2660       3       org.lombokweb.asm.MethodWriter::visitLocalVariable (222 bytes)
Event: 19.500 Thread 0x00000284e4ac8510 nmethod 2660 0x00000284d3e7ce90 code [0x00000284d3e7d1e0, 0x00000284d3e7e260]
Event: 19.500 Thread 0x00000284e4ac8510 2662       3       lombok.patcher.scripts.ReplaceMethodCallScript$ReplaceMethodCall::visitMethodInsn (250 bytes)
Event: 19.502 Thread 0x00000284e4ac8510 nmethod 2662 0x00000284d3e7eb10 code [0x00000284d3e7eec0, 0x00000284d3e80448]
Event: 19.505 Thread 0x00000284e4ac8510 2663       3       org.lombokweb.asm.MethodWriter::visitTypeInsn (116 bytes)
Event: 19.505 Thread 0x00000284e4ac8510 nmethod 2663 0x00000284d3e80d90 code [0x00000284d3e80f80, 0x00000284d3e813e8]
Event: 19.506 Thread 0x00000284e4ac8510 2664       3       org.lombokweb.asm.MethodVisitor::visitLocalVariable (24 bytes)
Event: 19.507 Thread 0x00000284e4ac8510 nmethod 2664 0x00000284d3e81610 code [0x00000284d3e817c0, 0x00000284d3e819d0]
Event: 19.514 Thread 0x00000284e4ac8510 2665       3       org.lombokweb.asm.MethodWriter::endCurrentBasicBlockWithNoSuccessor (98 bytes)
Event: 19.515 Thread 0x00000284e4ac8510 nmethod 2665 0x00000284d3e81a90 code [0x00000284d3e81c80, 0x00000284d3e82148]
Event: 19.515 Thread 0x00000284e4ac8510 2666       3       org.lombokweb.asm.MethodVisitor::visitTypeInsn (17 bytes)
Event: 19.515 Thread 0x00000284e4ac8510 nmethod 2666 0x00000284d3e82310 code [0x00000284d3e824c0, 0x00000284d3e826c8]
Event: 19.550 Thread 0x00000284e4ac8510 2668   !   3       jdk.internal.loader.BuiltinClassLoader$2::run (114 bytes)
Event: 19.551 Thread 0x00000284e4ac8510 nmethod 2668 0x00000284d3e82790 code [0x00000284d3e82a40, 0x00000284d3e83840]
Event: 19.551 Thread 0x00000284e4ac8510 2667       3       jdk.internal.loader.BuiltinClassLoader$2::run (5 bytes)
Event: 19.551 Thread 0x00000284e4ac8510 nmethod 2667 0x00000284d3e83d10 code [0x00000284d3e83ec0, 0x00000284d3e84018]
Event: 19.553 Thread 0x00000284e4ac8510 2669       3       org.eclipse.osgi.internal.hookregistry.ClassLoaderHook::preFindLocalResource (1 bytes)
Event: 19.554 Thread 0x00000284e4ac8510 nmethod 2669 0x00000284d3e84110 code [0x00000284d3e842a0, 0x00000284d3e843a8]
Event: 19.554 Thread 0x00000284e4ac8510 2670       3       org.eclipse.osgi.internal.hookregistry.ClassLoaderHook::postFindLocalResource (1 bytes)
Event: 19.554 Thread 0x00000284e4ac8510 nmethod 2670 0x00000284d3e84410 code [0x00000284d3e845a0, 0x00000284d3e846a8]

GC Heap History (10 events):
Event: 5.930 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4347K, committed 4544K, reserved 1114112K
  class space    used 438K, committed 512K, reserved 1048576K
}
Event: 6.031 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3222K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec725800,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 4347K, committed 4544K, reserved 1114112K
  class space    used 438K, committed 512K, reserved 1048576K
}
Event: 9.007 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28822K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec725800,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 8185K, committed 8448K, reserved 1114112K
  class space    used 811K, committed 960K, reserved 1048576K
}
Event: 9.018 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4091K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfefd0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 840K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c00d2020,0x00000000c4300000)
 Metaspace       used 8185K, committed 8448K, reserved 1114112K
  class space    used 811K, committed 960K, reserved 1048576K
}
Event: 13.227 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29691K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfefd0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 840K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c00d2020,0x00000000c4300000)
 Metaspace       used 12975K, committed 13568K, reserved 1114112K
  class space    used 1306K, committed 1536K, reserved 1048576K
}
Event: 13.238 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4093K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7ff6c8,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 2688K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 3% used [0x00000000c0000000,0x00000000c02a01e0,0x00000000c4300000)
 Metaspace       used 12975K, committed 13568K, reserved 1114112K
  class space    used 1306K, committed 1536K, reserved 1048576K
}
Event: 16.117 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29693K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7ff6c8,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 2688K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 3% used [0x00000000c0000000,0x00000000c02a01e0,0x00000000c4300000)
 Metaspace       used 15757K, committed 16320K, reserved 1114112K
  class space    used 1572K, committed 1792K, reserved 1048576K
}
Event: 16.131 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4090K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfe930,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 5349K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 7% used [0x00000000c0000000,0x00000000c05396c8,0x00000000c4300000)
 Metaspace       used 15757K, committed 16320K, reserved 1114112K
  class space    used 1572K, committed 1792K, reserved 1048576K
}
Event: 19.205 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29000K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 97% used [0x00000000eab00000,0x00000000ec3538a8,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfe930,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 5349K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 7% used [0x00000000c0000000,0x00000000c05396c8,0x00000000c4300000)
 Metaspace       used 19999K, committed 20608K, reserved 1114112K
  class space    used 1877K, committed 2112K, reserved 1048576K
}
Event: 19.216 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4090K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fe940,0x00000000ec800000)
  to   space 5632K, 0% used [0x00000000ec900000,0x00000000ec900000,0x00000000ece80000)
 ParOldGen       total 68608K, used 6217K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 9% used [0x00000000c0000000,0x00000000c0612468,0x00000000c4300000)
 Metaspace       used 19999K, committed 20608K, reserved 1114112K
  class space    used 1877K, committed 2112K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.897 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 2.357 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 2.722 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 2.764 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 2.789 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 2.795 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 3.069 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 3.449 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 8.538 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 13.293 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
Event: 13.417 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 15.234 Thread 0x00000284e4a95c50 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000284db528d90 relative=0x00000000000003b0
Event: 15.234 Thread 0x00000284e4a95c50 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000284db528d90 method=java.util.regex.Pattern$BmpCharPropertyGreedy.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 70 c2
Event: 15.234 Thread 0x00000284e4a95c50 DEOPT PACKING pc=0x00000284db528d90 sp=0x0000009f5d7ef5d0
Event: 15.234 Thread 0x00000284e4a95c50 DEOPT UNPACKING pc=0x00000284daee3a9c sp=0x0000009f5d7ef578 mode 2
Event: 15.234 Thread 0x00000284e4a95c50 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000284db528d90 relative=0x00000000000003b0
Event: 15.234 Thread 0x00000284e4a95c50 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000284db528d90 method=java.util.regex.Pattern$BmpCharPropertyGreedy.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 70 c2
Event: 15.234 Thread 0x00000284e4a95c50 DEOPT PACKING pc=0x00000284db528d90 sp=0x0000009f5d7ef450
Event: 15.234 Thread 0x00000284e4a95c50 DEOPT UNPACKING pc=0x00000284daee3a9c sp=0x0000009f5d7ef3f8 mode 2
Event: 16.018 Thread 0x00000284e4a95c50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000284db5388d8 relative=0x00000000000006d8
Event: 16.018 Thread 0x00000284e4a95c50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000284db5388d8 method=org.lombokweb.asm.ClassReader.readUTF8(I[C)Ljava/lang/String; @ 11 c2
Event: 16.018 Thread 0x00000284e4a95c50 DEOPT PACKING pc=0x00000284db5388d8 sp=0x0000009f5d7ef550
Event: 16.018 Thread 0x00000284e4a95c50 DEOPT UNPACKING pc=0x00000284daee3a9c sp=0x0000009f5d7ef538 mode 2
Event: 18.216 Thread 0x00000284e4a95c50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000284db5443e0 relative=0x0000000000000140
Event: 18.216 Thread 0x00000284e4a95c50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000284db5443e0 method=org.lombokweb.asm.ByteVector.putByteArray([BII)Lorg/lombokweb/asm/ByteVector; @ 20 c2
Event: 18.216 Thread 0x00000284e4a95c50 DEOPT PACKING pc=0x00000284db5443e0 sp=0x0000009f5d7f2760
Event: 18.216 Thread 0x00000284e4a95c50 DEOPT UNPACKING pc=0x00000284daee3a9c sp=0x0000009f5d7f2730 mode 2
Event: 18.216 Thread 0x00000284e4a95c50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000284db544ab0 relative=0x0000000000000370
Event: 18.216 Thread 0x00000284e4a95c50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000284db544ab0 method=org.lombokweb.asm.Label.resolve([BLorg/lombokweb/asm/ByteVector;I)Z @ 81 c2
Event: 18.216 Thread 0x00000284e4a95c50 DEOPT PACKING pc=0x00000284db544ab0 sp=0x0000009f5d7f27f0
Event: 18.216 Thread 0x00000284e4a95c50 DEOPT UNPACKING pc=0x00000284daee3a9c sp=0x0000009f5d7f26f8 mode 2

Classes loaded (20 events):
Event: 15.187 Loading class java/io/StringReader
Event: 15.187 Loading class java/io/StringReader done
Event: 15.551 Loading class java/util/zip/GZIPInputStream
Event: 15.552 Loading class java/util/zip/GZIPInputStream done
Event: 15.576 Loading class java/net/SocketTimeoutException
Event: 15.576 Loading class java/io/InterruptedIOException
Event: 15.576 Loading class java/io/InterruptedIOException done
Event: 15.576 Loading class java/net/SocketTimeoutException done
Event: 15.576 Loading class java/net/SocketException
Event: 15.607 Loading class java/net/SocketException done
Event: 15.607 Loading class java/net/UnknownHostException
Event: 15.608 Loading class java/net/UnknownHostException done
Event: 15.608 Loading class java/net/ProtocolException
Event: 15.608 Loading class java/net/ProtocolException done
Event: 15.857 Loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 15.857 Loading class java/lang/ThreadLocal$SuppliedThreadLocal done
Event: 15.912 Loading class java/io/FileWriter
Event: 15.912 Loading class java/io/FileWriter done
Event: 16.333 Loading class java/lang/NegativeArraySizeException
Event: 16.333 Loading class java/lang/NegativeArraySizeException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 14.227 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb1c20d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000eb1c20d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.228 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb1c9860}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000eb1c9860) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.228 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb1cd4e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000eb1cd4e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.384 Thread 0x00000284e4a95c50 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eb2cc888}: Found class java.lang.Object, but interface was expected> (0x00000000eb2cc888) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 14.385 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb2d0258}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eb2d0258) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.386 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb2d7af0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eb2d7af0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.387 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb2db5e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eb2db5e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.388 Thread 0x00000284ea65f870 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb21f7b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eb21f7b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.389 Thread 0x00000284ea65f870 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb2264e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eb2264e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 14.556 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb409b20}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb409b20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 15.064 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb629370}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000eb629370) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 15.064 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb62ca30}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eb62ca30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 16.359 Thread 0x00000284e4a95c50 Exception <a 'java/io/FileNotFoundException'{0x00000000ead891e0}> (0x00000000ead891e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 16.359 Thread 0x00000284e4a95c50 Exception <a 'java/io/FileNotFoundException'{0x00000000ead8a000}> (0x00000000ead8a000) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 16.359 Thread 0x00000284e4a95c50 Exception <a 'java/io/FileNotFoundException'{0x00000000ead8b180}> (0x00000000ead8b180) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17.776 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb6d6f78}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb6d6f78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 18.216 Thread 0x00000284e4a95c50 Implicit null exception at 0x00000284db5442f6 to 0x00000284db5443c8
Event: 19.222 Thread 0x00000284e4a95c50 Exception <a 'java/io/FileNotFoundException'{0x00000000eabcf3b8}> (0x00000000eabcf3b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 19.223 Thread 0x00000284e4a95c50 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabd6c20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eabd6c20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.324 Thread 0x00000284e4a95c50 Exception <a 'java/io/FileNotFoundException'{0x00000000eb600aa8}> (0x00000000eb600aa8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 13.806 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 13.806 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 13.813 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 13.813 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 13.832 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 13.832 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 14.843 Executing VM operation: Cleanup
Event: 14.843 Executing VM operation: Cleanup done
Event: 15.843 Executing VM operation: Cleanup
Event: 15.843 Executing VM operation: Cleanup done
Event: 16.117 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 16.131 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 17.139 Executing VM operation: Cleanup
Event: 17.139 Executing VM operation: Cleanup done
Event: 18.141 Executing VM operation: Cleanup
Event: 18.141 Executing VM operation: Cleanup done
Event: 19.145 Executing VM operation: Cleanup
Event: 19.145 Executing VM operation: Cleanup done
Event: 19.205 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.216 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 4.595 Thread 0x00000284e4ac8510 Thread added: 0x00000284e5afb0d0
Event: 4.995 Thread 0x00000284e5afb0d0 Thread exited: 0x00000284e5afb0d0
Event: 5.673 Thread 0x00000284e4ac8510 Thread added: 0x00000284ea108b10
Event: 5.851 Thread 0x00000284ea108b10 Thread exited: 0x00000284ea108b10
Event: 6.762 Thread 0x00000284e4ac3810 Thread added: 0x00000284ea0c9000
Event: 7.722 Thread 0x00000284d0762d60 Thread added: 0x00000284e4a94210
Event: 7.949 Thread 0x00000284ea0c9000 Thread exited: 0x00000284ea0c9000
Event: 8.257 Thread 0x00000284d0762d60 Thread added: 0x00000284e4a955c0
Event: 8.267 Thread 0x00000284d0762d60 Thread added: 0x00000284e4a95c50
Event: 8.807 Thread 0x00000284e4a95c50 Thread added: 0x00000284e4a93b80
Event: 10.034 Thread 0x00000284e4a95c50 Thread added: 0x00000284e4a96970
Event: 10.739 Thread 0x00000284e4ac3810 Thread added: 0x00000284ea336050
Event: 10.895 Thread 0x00000284ea336050 Thread exited: 0x00000284ea336050
Event: 12.490 Thread 0x00000284e4a95c50 Thread added: 0x00000284ea65f1e0
Event: 14.282 Thread 0x00000284e4a95c50 Thread added: 0x00000284ea65f870
Event: 14.389 Thread 0x00000284e4a95c50 Thread added: 0x00000284ea662cf0
Event: 16.069 Thread 0x00000284e4ac8510 Thread added: 0x00000284ea3dc080
Event: 16.567 Thread 0x00000284ea3dc080 Thread exited: 0x00000284ea3dc080
Event: 19.376 Thread 0x00000284e4ac8510 Thread added: 0x00000284eb317f00
Event: 19.567 Thread 0x00000284e4a96970 Thread exited: 0x00000284e4a96970


Dynamic libraries:
0x00007ff75f730000 - 0x00007ff75f73e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffbb9f90000 - 0x00007ffbba170000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffbb7970000 - 0x00007ffbb7a1e000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffbb6eb0000 - 0x00007ffbb7116000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffbb7310000 - 0x00007ffbb7406000 	C:\Windows\System32\ucrtbase.dll
0x00007ffbb0b40000 - 0x00007ffbb0b58000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffbb7f20000 - 0x00007ffbb80af000 	C:\Windows\System32\USER32.dll
0x00007ffbb72f0000 - 0x00007ffbb7310000 	C:\Windows\System32\win32u.dll
0x00007ffbb7a20000 - 0x00007ffbb7a48000 	C:\Windows\System32\GDI32.dll
0x00007ffbafe00000 - 0x00007ffbb0069000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f\COMCTL32.dll
0x00007ffbb6430000 - 0x00007ffbb65c4000 	C:\Windows\System32\gdi32full.dll
0x00007ffbb9ec0000 - 0x00007ffbb9f5d000 	C:\Windows\System32\msvcrt.dll
0x00007ffbb6390000 - 0x00007ffbb642b000 	C:\Windows\System32\msvcp_win.dll
0x00007ffbb8570000 - 0x00007ffbb8878000 	C:\Windows\System32\combase.dll
0x00007ffbb7460000 - 0x00007ffbb757f000 	C:\Windows\System32\RPCRT4.dll
0x00007ffbb65d0000 - 0x00007ffbb6642000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffbb0600000 - 0x00007ffbb061e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffbb7780000 - 0x00007ffbb77ad000 	C:\Windows\System32\IMM32.DLL
0x00007ffbb16f0000 - 0x00007ffbb16fc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffbaad70000 - 0x00007ffbaadfd000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ffb7f7c0000 - 0x00007ffb80550000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffbb8880000 - 0x00007ffbb8921000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffbb8510000 - 0x00007ffbb856b000 	C:\Windows\System32\sechost.dll
0x00007ffbb62e0000 - 0x00007ffbb632c000 	C:\Windows\System32\POWRPROF.dll
0x00007ffbb7810000 - 0x00007ffbb787c000 	C:\Windows\System32\WS2_32.dll
0x00007ffbb1a90000 - 0x00007ffbb1a9a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffbb47c0000 - 0x00007ffbb47e3000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffbb4730000 - 0x00007ffbb475a000 	C:\Windows\SYSTEM32\WINMMBASE.dll
0x00007ffbb7410000 - 0x00007ffbb745a000 	C:\Windows\System32\cfgmgr32.dll
0x00007ffbb6370000 - 0x00007ffbb6381000 	C:\Windows\System32\kernel.appcore.dll
0x00007ffbb1640000 - 0x00007ffbb164a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffb9e7e0000 - 0x00007ffb9e9a8000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffbaf590000 - 0x00007ffbaf5b9000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffbb15f0000 - 0x00007ffbb15ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffbb0350000 - 0x00007ffbb036f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffbb8930000 - 0x00007ffbb9d67000 	C:\Windows\System32\SHELL32.dll
0x00007ffbb8340000 - 0x00007ffbb83e6000 	C:\Windows\System32\shcore.dll
0x00007ffbb66b0000 - 0x00007ffbb6df7000 	C:\Windows\System32\windows.storage.dll
0x00007ffbb7720000 - 0x00007ffbb7771000 	C:\Windows\System32\shlwapi.dll
0x00007ffbb6330000 - 0x00007ffbb634b000 	C:\Windows\System32\profapi.dll
0x00007ffbab5b0000 - 0x00007ffbab5c8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffbb05f0000 - 0x00007ffbb0600000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffbb0260000 - 0x00007ffbb033e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffbb5b10000 - 0x00007ffbb5b76000 	C:\Windows\system32\mswsock.dll
0x00007ffbab3c0000 - 0x00007ffbab3d6000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffbb0340000 - 0x00007ffbb0350000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffbaad20000 - 0x00007ffbaad65000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffbb9d70000 - 0x00007ffbb9eb9000 	C:\Windows\System32\ole32.dll
0x00007ffbab3b0000 - 0x00007ffbab3ba000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
0x00007ffbaae40000 - 0x00007ffbaae4b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll
0x00007ffbb82d0000 - 0x00007ffbb82d8000 	C:\Windows\System32\PSAPI.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-67b775b5290b748a41f444560a4660ae-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17.0.14+7
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\python313\Scripts\;C:\python313\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\ffmpeg-master-latest-win64-gpl;C:\ffmpeg\bin;C:\Program Files\Git\cmd;C:\Windows\System32;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\java\jdk-24;C:\Program Files\Java\jdk-24\bin;C:\src\flutter\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;C:\dart-sdk\bin;C:\Program Files\Java\jdk-17.0.14+7\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=snk
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 23 Stepping 7, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 16299 (10.0.16299.15)
OS uptime: 0 days 1:15 hours

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 23 stepping 7 microcode 0x70b, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, tsc, clflush
Processor Information for the first 4 processors :
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500

Memory: 4k page, system-wide physical 4094M (615M free)
TotalPageFile size 4094M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 146M, peak: 146M
current process commit charge ("private bytes"): 238M, peak: 240M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
