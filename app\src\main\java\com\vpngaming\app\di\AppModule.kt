package com.vpngaming.app.di

import android.content.Context
import com.vpngaming.app.data.database.VpnDao
import com.vpngaming.app.data.repository.VpnRepository
import com.vpngaming.app.vpn.VpnManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * وحدة Hilt الرئيسية للتطبيق
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideVpnRepository(vpnDao: VpnDao): VpnRepository {
        return VpnRepository(vpnDao)
    }

    @Provides
    @Singleton
    fun provideVpnManager(@ApplicationContext context: Context): VpnManager {
        return VpnManager(context)
    }
}
