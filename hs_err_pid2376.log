#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32784 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=2376, tid=5076
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-7a998c8c4d01f3ee37263b1b12ec28f4-sock

Host: Intel(R) Core(TM)2 Quad  CPU   Q9300  @ 2.50GHz, 4 cores, 3G,  Windows 10 , 64 bit Build 16299 (10.0.16299.15)
Time: Sun May 25 14:13:34 2025 Central Europe Daylight Time elapsed time: 20.599717 seconds (0d 0h 0m 20s)

---------------  T H R E A D  ---------------

Current thread (0x000001e85d1406c0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=5076, stack(0x0000008912f00000,0x0000008913000000) (1024K)]


Current CompileTask:
C2:20599 2914       4       com.sun.org.apache.xerces.internal.impl.XMLEntityScanner::scanQName (580 bytes)

Stack: [0x0000008912f00000,0x0000008913000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0xc500d]
V  [jvm.dll+0xc5543]
V  [jvm.dll+0xc5135]
V  [jvm.dll+0x6a954c]
V  [jvm.dll+0x5f667f]
V  [jvm.dll+0x250bb2]
V  [jvm.dll+0x249526]
V  [jvm.dll+0x246ec4]
V  [jvm.dll+0x1c75ee]
V  [jvm.dll+0x25685a]
V  [jvm.dll+0x254dfa]
V  [jvm.dll+0x3f0256]
V  [jvm.dll+0x851f8b]
V  [jvm.dll+0x6cc5ed]
C  [ucrtbase.dll+0x1d885]
C  [KERNEL32.DLL+0x11fe4]
C  [ntdll.dll+0x6ef91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001e8630ff0f0, length=19, elements={
0x000001e807d474a0, 0x000001e85d127fa0, 0x000001e85d129f70, 0x000001e85d12ebd0,
0x000001e85d130830, 0x000001e85d134510, 0x000001e85d136f80, 0x000001e85d1406c0,
0x000001e85d143240, 0x000001e807db3c60, 0x000001e8622829a0, 0x000001e8629aa160,
0x000001e863133c70, 0x000001e86285bc50, 0x000001e863062060, 0x000001e862b08f00,
0x000001e86305eee0, 0x000001e86305f540, 0x000001e86310ec00
}

Java Threads: ( => current thread )
  0x000001e807d474a0 JavaThread "main"                              [_thread_blocked, id=6520, stack(0x0000008912500000,0x0000008912600000) (1024K)]
  0x000001e85d127fa0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7780, stack(0x0000008912900000,0x0000008912a00000) (1024K)]
  0x000001e85d129f70 JavaThread "Finalizer"                  daemon [_thread_blocked, id=7072, stack(0x0000008912a00000,0x0000008912b00000) (1024K)]
  0x000001e85d12ebd0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=6812, stack(0x0000008912b00000,0x0000008912c00000) (1024K)]
  0x000001e85d130830 JavaThread "Attach Listener"            daemon [_thread_blocked, id=4816, stack(0x0000008912c00000,0x0000008912d00000) (1024K)]
  0x000001e85d134510 JavaThread "Service Thread"             daemon [_thread_blocked, id=2580, stack(0x0000008912d00000,0x0000008912e00000) (1024K)]
  0x000001e85d136f80 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=7388, stack(0x0000008912e00000,0x0000008912f00000) (1024K)]
=>0x000001e85d1406c0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=5076, stack(0x0000008912f00000,0x0000008913000000) (1024K)]
  0x000001e85d143240 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=7360, stack(0x0000008913000000,0x0000008913100000) (1024K)]
  0x000001e807db3c60 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=1920, stack(0x0000008913100000,0x0000008913200000) (1024K)]
  0x000001e8622829a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=3752, stack(0x0000008913300000,0x0000008913400000) (1024K)]
  0x000001e8629aa160 JavaThread "Active Thread: Equinox Container: 7e33ebbc-aa3f-4abb-88de-3d16078044bf"        [_thread_blocked, id=7420, stack(0x0000008913a00000,0x0000008913b00000) (1024K)]
  0x000001e863133c70 JavaThread "Refresh Thread: Equinox Container: 7e33ebbc-aa3f-4abb-88de-3d16078044bf" daemon [_thread_blocked, id=6028, stack(0x0000008913c00000,0x0000008913d00000) (1024K)]
  0x000001e86285bc50 JavaThread "Framework Event Dispatcher: Equinox Container: 7e33ebbc-aa3f-4abb-88de-3d16078044bf" daemon [_thread_blocked, id=7792, stack(0x0000008913d00000,0x0000008913e00000) (1024K)]
  0x000001e863062060 JavaThread "Start Level: Equinox Container: 7e33ebbc-aa3f-4abb-88de-3d16078044bf" daemon [_thread_in_native, id=7752, stack(0x0000008913e00000,0x0000008913f00000) (1024K)]
  0x000001e862b08f00 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=7600, stack(0x0000008914000000,0x0000008914100000) (1024K)]
  0x000001e86305eee0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=1284, stack(0x0000008914100000,0x0000008914200000) (1024K)]
  0x000001e86305f540 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=1828, stack(0x0000008914200000,0x0000008914300000) (1024K)]
  0x000001e86310ec00 JavaThread "Worker-JM"                         [_thread_blocked, id=1156, stack(0x0000008914400000,0x0000008914500000) (1024K)]
Total: 19

Other Threads:
  0x000001e807e031e0 VMThread "VM Thread"                           [id=2504, stack(0x0000008912800000,0x0000008912900000) (1024K)]
  0x000001e85d0dcb00 WatcherThread "VM Periodic Task Thread"        [id=512, stack(0x0000008912700000,0x0000008912800000) (1024K)]
  0x000001e807d65390 WorkerThread "GC Thread#0"                     [id=6484, stack(0x0000008912600000,0x0000008912700000) (1024K)]
  0x000001e86228cd00 WorkerThread "GC Thread#1"                     [id=7856, stack(0x0000008913600000,0x0000008913700000) (1024K)]
  0x000001e86228d0a0 WorkerThread "GC Thread#2"                     [id=3396, stack(0x0000008913700000,0x0000008913800000) (1024K)]
  0x000001e86228d440 WorkerThread "GC Thread#3"                     [id=5964, stack(0x0000008913800000,0x0000008913900000) (1024K)]
Total: 6

Threads with active compile tasks:
C2 CompilerThread0  20930 2914       4       com.sun.org.apache.xerces.internal.impl.XMLEntityScanner::scanQName (580 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001e81c000000-0x000001e81cba0000-0x000001e81cba0000), size 12189696, SharedBaseAddress: 0x000001e81c000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001e81d000000-0x000001e85d000000, reserved size: 1073741824
Narrow klass base: 0x000001e81c000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 4 total, 4 available
 Memory: 4094M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 4

Heap:
 PSYoungGen      total 29696K, used 14570K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 41% used [0x00000000eab00000,0x00000000eb5409b8,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbf9ec0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 7096K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 10% used [0x00000000c0000000,0x00000000c06ee010,0x00000000c4300000)
 Metaspace       used 14148K, committed 14656K, reserved 1114112K
  class space    used 1398K, committed 1600K, reserved 1048576K

Card table byte_map: [0x000001e8076f0000,0x000001e807900000] _byte_map_base: 0x000001e8070f0000

Marking Bits: (ParMarkBitMap*) 0x00007ffb80473260
 Begin Bits: [0x000001e819f40000, 0x000001e81af40000)
 End Bits:   [0x000001e81af40000, 0x000001e81bf40000)

Polling page: 0x000001e805a70000

Metaspace:

Usage:
  Non-class:     12.45 MB used.
      Class:      1.37 MB used.
       Both:     13.82 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      12.75 MB ( 20%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.56 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      14.31 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  2.48 MB
       Class:  14.44 MB
        Both:  16.93 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 356.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 229.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 826.
num_chunk_merges: 0.
num_chunk_splits: 528.
num_chunks_enlarged: 331.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1286Kb max_used=1286Kb free=118713Kb
 bounds [0x000001e812a10000, 0x000001e812c80000, 0x000001e819f40000]
CodeHeap 'profiled nmethods': size=120000Kb used=6232Kb max_used=6232Kb free=113767Kb
 bounds [0x000001e80af40000, 0x000001e80b560000, 0x000001e812470000]
CodeHeap 'non-nmethods': size=5760Kb used=1245Kb max_used=1264Kb free=4514Kb
 bounds [0x000001e812470000, 0x000001e8126e0000, 0x000001e812a10000]
 total_blobs=3578 nmethods=3060 adapters=425
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 20.545 Thread 0x000001e85d143240 nmethod 2944 0x000001e812b4d110 code [0x000001e812b4d2a0, 0x000001e812b4d368]
Event: 20.545 Thread 0x000001e85d143240 2943       3       java.lang.invoke.DirectMethodHandle$Holder::newInvokeSpecial (24 bytes)
Event: 20.545 Thread 0x000001e85d143240 nmethod 2943 0x000001e80b502d10 code [0x000001e80b502f00, 0x000001e80b503688]
Event: 20.545 Thread 0x000001e85d143240 2945       3       org.apache.felix.scr.impl.manager.AbstractComponentManager::getLogger (10 bytes)
Event: 20.546 Thread 0x000001e85d143240 nmethod 2945 0x000001e80b503890 code [0x000001e80b503a40, 0x000001e80b503c18]
Event: 20.546 Thread 0x000001e85d143240 2948       1       org.apache.felix.scr.impl.metadata.ReferenceMetadata::getScope (5 bytes)
Event: 20.546 Thread 0x000001e85d143240 nmethod 2948 0x000001e812b4d410 code [0x000001e812b4d5a0, 0x000001e812b4d668]
Event: 20.546 Thread 0x000001e85d143240 2949       1       org.apache.felix.scr.impl.metadata.ReferenceMetadata::isOptional (5 bytes)
Event: 20.546 Thread 0x000001e85d143240 nmethod 2949 0x000001e812b4d710 code [0x000001e812b4d8a0, 0x000001e812b4d970]
Event: 20.546 Thread 0x000001e85d143240 2946       3       org.eclipse.osgi.framework.eventmgr.CopyOnWriteIdentityMap::isEmpty (11 bytes)
Event: 20.546 Thread 0x000001e85d143240 nmethod 2946 0x000001e80b503d10 code [0x000001e80b503ec0, 0x000001e80b5040c0]
Event: 20.546 Thread 0x000001e85d143240 2950       1       org.apache.felix.scr.impl.manager.ServiceTracker::tracked (5 bytes)
Event: 20.546 Thread 0x000001e85d143240 nmethod 2950 0x000001e812b4da10 code [0x000001e812b4dba0, 0x000001e812b4dc68]
Event: 20.546 Thread 0x000001e85d143240 2951       1       java.util.concurrent.atomic.AtomicInteger::get (5 bytes)
Event: 20.546 Thread 0x000001e85d143240 nmethod 2951 0x000001e812b4dd10 code [0x000001e812b4dea0, 0x000001e812b4df68]
Event: 20.546 Thread 0x000001e85d143240 2952       3       java.util.LinkedList::<init> (10 bytes)
Event: 20.546 Thread 0x000001e85d143240 nmethod 2952 0x000001e80b504190 code [0x000001e80b504340, 0x000001e80b5045d0]
Event: 20.547 Thread 0x000001e85d143240 2953       3       java.util.AbstractSequentialList::<init> (5 bytes)
Event: 20.547 Thread 0x000001e85d143240 nmethod 2953 0x000001e80b504710 code [0x000001e80b5048c0, 0x000001e80b504af0]
Event: 20.547 Thread 0x000001e85d143240 2954       3       org.eclipse.osgi.internal.framework.FilterImpl$Parser::parse_filter (125 bytes)

GC Heap History (8 events):
Event: 5.930 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4328K, committed 4480K, reserved 1114112K
  class space    used 438K, committed 512K, reserved 1048576K
}
Event: 6.031 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3205K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec7217f0,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 4328K, committed 4480K, reserved 1114112K
  class space    used 438K, committed 512K, reserved 1048576K
}
Event: 8.378 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28805K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec7217f0,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 8097K, committed 8384K, reserved 1114112K
  class space    used 803K, committed 960K, reserved 1048576K
}
Event: 8.401 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4091K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfeec8,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1636K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 2% used [0x00000000c0000000,0x00000000c01991e0,0x00000000c4300000)
 Metaspace       used 8097K, committed 8384K, reserved 1114112K
  class space    used 803K, committed 960K, reserved 1048576K
}
Event: 16.860 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29691K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfeec8,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1636K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 2% used [0x00000000c0000000,0x00000000c01991e0,0x00000000c4300000)
 Metaspace       used 9293K, committed 9600K, reserved 1114112K
  class space    used 940K, committed 1088K, reserved 1048576K
}
Event: 16.873 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4065K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7f8710,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5775K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c05a3f40,0x00000000c4300000)
 Metaspace       used 9293K, committed 9600K, reserved 1114112K
  class space    used 940K, committed 1088K, reserved 1048576K
}
Event: 20.344 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29665K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7f8710,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5775K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c05a3f40,0x00000000c4300000)
 Metaspace       used 12610K, committed 12992K, reserved 1114112K
  class space    used 1269K, committed 1472K, reserved 1048576K
}
Event: 20.351 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4071K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbf9ec0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 7096K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 10% used [0x00000000c0000000,0x00000000c06ee010,0x00000000c4300000)
 Metaspace       used 12610K, committed 12992K, reserved 1114112K
  class space    used 1269K, committed 1472K, reserved 1048576K
}

Dll operation events (9 events):
Event: 0.897 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 2.357 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 2.721 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 2.763 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 2.788 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 2.793 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 3.069 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 3.449 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 8.672 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll

Deoptimization events (20 events):
Event: 20.154 Thread 0x000001e863062060 DEOPT PACKING pc=0x000001e812b380a8 sp=0x0000008913ef8840
Event: 20.154 Thread 0x000001e863062060 DEOPT UNPACKING pc=0x000001e8124c3a9c sp=0x0000008913ef87b0 mode 2
Event: 20.154 Thread 0x000001e863062060 DEOPT PACKING pc=0x000001e80b43cf28 sp=0x0000008913ef86f0
Event: 20.154 Thread 0x000001e863062060 DEOPT UNPACKING pc=0x000001e8124c4223 sp=0x0000008913ef7bb0 mode 0
Event: 20.243 Thread 0x000001e863062060 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e812b41098 relative=0x0000000000000378
Event: 20.243 Thread 0x000001e863062060 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e812b41098 method=com.sun.org.apache.xerces.internal.impl.XMLEntityScanner.skipSpaces()Z @ 88 c2
Event: 20.243 Thread 0x000001e863062060 DEOPT PACKING pc=0x000001e812b41098 sp=0x0000008913ef8aa0
Event: 20.243 Thread 0x000001e863062060 DEOPT UNPACKING pc=0x000001e8124c3a9c sp=0x0000008913ef8a38 mode 2
Event: 20.243 Thread 0x000001e863062060 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e812b3dffc relative=0x00000000000000dc
Event: 20.243 Thread 0x000001e863062060 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e812b3dffc method=com.sun.org.apache.xerces.internal.impl.XMLEntityScanner.peekChar()I @ 51 c2
Event: 20.243 Thread 0x000001e863062060 DEOPT PACKING pc=0x000001e812b3dffc sp=0x0000008913ef89f0
Event: 20.243 Thread 0x000001e863062060 DEOPT UNPACKING pc=0x000001e8124c3a9c sp=0x0000008913ef8978 mode 2
Event: 20.303 Thread 0x000001e863062060 Uncommon trap: trap_request=0xffffffcc fr.pc=0x000001e812b3e7f0 relative=0x00000000000001b0
Event: 20.303 Thread 0x000001e863062060 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x000001e812b3e7f0 method=java.util.Arrays.copyOf([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object; @ 35 c2
Event: 20.303 Thread 0x000001e863062060 DEOPT PACKING pc=0x000001e812b3e7f0 sp=0x0000008913efbef0
Event: 20.303 Thread 0x000001e863062060 DEOPT UNPACKING pc=0x000001e8124c3a9c sp=0x0000008913efbe80 mode 2
Event: 20.364 Thread 0x000001e863062060 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e812b4391c relative=0x000000000000041c
Event: 20.364 Thread 0x000001e863062060 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e812b4391c method=com.sun.org.apache.xerces.internal.impl.XMLEntityScanner.scanLiteral(ILcom/sun/org/apache/xerces/internal/xni/XMLString;Z)I @ 299 c2
Event: 20.364 Thread 0x000001e863062060 DEOPT PACKING pc=0x000001e812b4391c sp=0x0000008913efdaf0
Event: 20.364 Thread 0x000001e863062060 DEOPT UNPACKING pc=0x000001e8124c3a9c sp=0x0000008913efda80 mode 2

Classes loaded (20 events):
Event: 19.405 Loading class java/util/concurrent/CompletionStage
Event: 19.406 Loading class java/util/concurrent/CompletionStage done
Event: 19.408 Loading class java/util/Timer
Event: 19.408 Loading class java/util/Timer done
Event: 19.408 Loading class java/util/TaskQueue
Event: 19.408 Loading class java/util/TaskQueue done
Event: 19.409 Loading class java/util/TimerThread
Event: 19.409 Loading class java/util/TimerThread done
Event: 19.409 Loading class java/util/Timer$ThreadReaper
Event: 19.409 Loading class java/util/Timer$ThreadReaper done
Event: 19.457 Loading class java/util/concurrent/locks/ReentrantLock$FairSync
Event: 19.457 Loading class java/util/concurrent/locks/ReentrantLock$FairSync done
Event: 19.936 Loading class java/lang/IllegalCallerException
Event: 19.936 Loading class java/lang/IllegalCallerException done
Event: 19.942 Loading class java/lang/invoke/DirectMethodHandle$1
Event: 19.942 Loading class java/lang/invoke/DirectMethodHandle$1 done
Event: 19.944 Loading class org/xml/sax/SAXParseException
Event: 19.945 Loading class org/xml/sax/SAXParseException done
Event: 20.380 Loading class java/lang/Process
Event: 20.406 Loading class java/lang/Process done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 6.251 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead9e328}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000ead9e328) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.251 Thread 0x000001e807d474a0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eada1860}: Found class java.lang.Object, but interface was expected> (0x00000000eada1860) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 6.287 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eada5a18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000eada5a18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.287 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eada8dc0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eada8dc0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.293 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eadafe40}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eadafe40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.642 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf11a08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eaf11a08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.493 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb8fad20}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000eb8fad20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.620 Thread 0x000001e807d474a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb976190}> (0x00000000eb976190) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.622 Thread 0x000001e807d474a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb97e158}> (0x00000000eb97e158) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.624 Thread 0x000001e807d474a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb98acd8}> (0x00000000eb98acd8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 8.025 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebf492f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object)'> (0x00000000ebf492f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.092 Thread 0x000001e807d474a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec062ba8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000ec062ba8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.494 Thread 0x000001e807d474a0 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000000eaba73e8}: 'void org.eclipse.equinox.launcher.JNIBridge._update_splash()'> (0x00000000eaba73e8) 
thrown [s\src\hotspot\share\prims\nativeLookup.cpp, line 415]
Event: 9.270 Thread 0x000001e863062060 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb0475a0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eb0475a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 9.534 Thread 0x000001e863062060 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb1ebe90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x00000000eb1ebe90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 9.653 Thread 0x000001e863062060 Exception <a 'java/lang/ClassNotFoundException'{0x00000000eb2431d0}: sun/util/logging/resources/spi/loggingProvider> (0x00000000eb2431d0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 19.225 Thread 0x000001e863062060 Exception <a 'java/lang/NullPointerException'{0x00000000eb12c318}> (0x00000000eb12c318) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]
Event: 19.225 Thread 0x000001e863062060 Exception <a 'java/lang/NullPointerException'{0x00000000eb12c5f8}> (0x00000000eb12c5f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]
Event: 19.930 Thread 0x000001e863062060 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebaf2c48}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ebaf2c48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 20.327 Thread 0x000001e863062060 Exception <a 'java/io/FileNotFoundException'{0x00000000ec35ae40}> (0x00000000ec35ae40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 11.978 Executing VM operation: Cleanup
Event: 11.978 Executing VM operation: Cleanup done
Event: 12.979 Executing VM operation: Cleanup
Event: 12.979 Executing VM operation: Cleanup done
Event: 13.979 Executing VM operation: Cleanup
Event: 13.979 Executing VM operation: Cleanup done
Event: 14.979 Executing VM operation: Cleanup
Event: 14.980 Executing VM operation: Cleanup done
Event: 15.980 Executing VM operation: Cleanup
Event: 15.980 Executing VM operation: Cleanup done
Event: 16.860 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 16.873 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 18.871 Executing VM operation: Cleanup
Event: 18.871 Executing VM operation: Cleanup done
Event: 19.220 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.220 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 19.458 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.458 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 20.344 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 20.351 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 3.740 Thread 0x000001e807d474a0 Thread added: 0x000001e8622829a0
Event: 4.607 Thread 0x000001e85d143240 Thread added: 0x000001e8622d5ef0
Event: 5.152 Thread 0x000001e8622d5ef0 Thread exited: 0x000001e8622d5ef0
Event: 5.595 Thread 0x000001e85d1406c0 Thread added: 0x000001e8622ac3b0
Event: 6.032 Thread 0x000001e8622ac3b0 Thread exited: 0x000001e8622ac3b0
Event: 6.775 Thread 0x000001e85d1406c0 Thread added: 0x000001e862470450
Event: 7.414 Thread 0x000001e862470450 Thread exited: 0x000001e862470450
Event: 7.956 Thread 0x000001e807d474a0 Thread added: 0x000001e8629aa160
Event: 8.424 Thread 0x000001e85d1406c0 Thread added: 0x000001e86285bc50
Event: 8.488 Thread 0x000001e807d474a0 Thread added: 0x000001e863133c70
Event: 8.673 Thread 0x000001e86285bc50 Thread exited: 0x000001e86285bc50
Event: 8.679 Thread 0x000001e863133c70 Thread added: 0x000001e86285bc50
Event: 8.685 Thread 0x000001e807d474a0 Thread added: 0x000001e863062060
Event: 10.077 Thread 0x000001e85d143240 Thread added: 0x000001e8620710d0
Event: 10.260 Thread 0x000001e8620710d0 Thread exited: 0x000001e8620710d0
Event: 14.030 Thread 0x000001e863062060 Thread added: 0x000001e862b08f00
Event: 19.153 Thread 0x000001e863062060 Thread added: 0x000001e86305eee0
Event: 19.409 Thread 0x000001e863062060 Thread added: 0x000001e86305f540
Event: 20.007 Thread 0x000001e85d143240 Thread added: 0x000001e8621b70f0
Event: 20.336 Thread 0x000001e8621b70f0 Thread exited: 0x000001e8621b70f0


Dynamic libraries:
0x00007ff75f730000 - 0x00007ff75f73e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffbb9f90000 - 0x00007ffbba170000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffbb7970000 - 0x00007ffbb7a1e000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffbb6eb0000 - 0x00007ffbb7116000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffbb7310000 - 0x00007ffbb7406000 	C:\Windows\System32\ucrtbase.dll
0x00007ffbb0b40000 - 0x00007ffbb0b58000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffbb0600000 - 0x00007ffbb061e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffbb7f20000 - 0x00007ffbb80af000 	C:\Windows\System32\USER32.dll
0x00007ffbafe00000 - 0x00007ffbb0069000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f\COMCTL32.dll
0x00007ffbb72f0000 - 0x00007ffbb7310000 	C:\Windows\System32\win32u.dll
0x00007ffbb9ec0000 - 0x00007ffbb9f5d000 	C:\Windows\System32\msvcrt.dll
0x00007ffbb7a20000 - 0x00007ffbb7a48000 	C:\Windows\System32\GDI32.dll
0x00007ffbb6430000 - 0x00007ffbb65c4000 	C:\Windows\System32\gdi32full.dll
0x00007ffbb8570000 - 0x00007ffbb8878000 	C:\Windows\System32\combase.dll
0x00007ffbb6390000 - 0x00007ffbb642b000 	C:\Windows\System32\msvcp_win.dll
0x00007ffbb7460000 - 0x00007ffbb757f000 	C:\Windows\System32\RPCRT4.dll
0x00007ffbb65d0000 - 0x00007ffbb6642000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffbb7780000 - 0x00007ffbb77ad000 	C:\Windows\System32\IMM32.DLL
0x00007ffbb16f0000 - 0x00007ffbb16fc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffbaad70000 - 0x00007ffbaadfd000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ffb7f7c0000 - 0x00007ffb80550000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffbb8880000 - 0x00007ffbb8921000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffbb8510000 - 0x00007ffbb856b000 	C:\Windows\System32\sechost.dll
0x00007ffbb62e0000 - 0x00007ffbb632c000 	C:\Windows\System32\POWRPROF.dll
0x00007ffbb7810000 - 0x00007ffbb787c000 	C:\Windows\System32\WS2_32.dll
0x00007ffbb47c0000 - 0x00007ffbb47e3000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffbb1a90000 - 0x00007ffbb1a9a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffbb4730000 - 0x00007ffbb475a000 	C:\Windows\SYSTEM32\WINMMBASE.dll
0x00007ffbb7410000 - 0x00007ffbb745a000 	C:\Windows\System32\cfgmgr32.dll
0x00007ffbb6370000 - 0x00007ffbb6381000 	C:\Windows\System32\kernel.appcore.dll
0x00007ffbb1640000 - 0x00007ffbb164a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffb9e7e0000 - 0x00007ffb9e9a8000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffbaf590000 - 0x00007ffbaf5b9000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffbb15f0000 - 0x00007ffbb15ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffbb0350000 - 0x00007ffbb036f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffbb8930000 - 0x00007ffbb9d67000 	C:\Windows\System32\SHELL32.dll
0x00007ffbb8340000 - 0x00007ffbb83e6000 	C:\Windows\System32\shcore.dll
0x00007ffbb66b0000 - 0x00007ffbb6df7000 	C:\Windows\System32\windows.storage.dll
0x00007ffbb7720000 - 0x00007ffbb7771000 	C:\Windows\System32\shlwapi.dll
0x00007ffbb6330000 - 0x00007ffbb634b000 	C:\Windows\System32\profapi.dll
0x00007ffbab5b0000 - 0x00007ffbab5c8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffbb05f0000 - 0x00007ffbb0600000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffbb0260000 - 0x00007ffbb033e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffbb5b10000 - 0x00007ffbb5b76000 	C:\Windows\system32\mswsock.dll
0x00007ffbab3c0000 - 0x00007ffbab3d6000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffbb0340000 - 0x00007ffbb0350000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffbaacd0000 - 0x00007ffbaad15000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffbb9d70000 - 0x00007ffbb9eb9000 	C:\Windows\System32\ole32.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-7a998c8c4d01f3ee37263b1b12ec28f4-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17.0.14+7
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\python313\Scripts\;C:\python313\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\ffmpeg-master-latest-win64-gpl;C:\ffmpeg\bin;C:\Program Files\Git\cmd;C:\Windows\System32;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\java\jdk-24;C:\Program Files\Java\jdk-24\bin;C:\src\flutter\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;C:\dart-sdk\bin;C:\Program Files\Java\jdk-17.0.14+7\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=snk
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 23 Stepping 7, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 16299 (10.0.16299.15)
OS uptime: 0 days 1:15 hours

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 23 stepping 7 microcode 0x70b, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, tsc, clflush
Processor Information for the first 4 processors :
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500

Memory: 4k page, system-wide physical 4094M (615M free)
TotalPageFile size 4094M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 114M, peak: 114M
current process commit charge ("private bytes"): 206M, peak: 206M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
