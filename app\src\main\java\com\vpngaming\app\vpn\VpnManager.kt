package com.vpngaming.app.vpn

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.net.VpnService
import android.os.IBinder
import com.vpngaming.app.data.model.VpnConnectionState
import com.vpngaming.app.data.model.VpnServer
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير VPN الرئيسي
 * يدير الاتصال والانقطاع وحالة VPN
 */
@Singleton
class VpnManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    private var vpnService: VpnGamingService? = null
    private var isBound = false

    // حالة الاتصال الحالية
    private val _connectionState = MutableStateFlow(VpnConnectionState.DISCONNECTED)
    val connectionState: StateFlow<VpnConnectionState> = _connectionState.asStateFlow()

    // الخادم المتصل حالياً
    private val _currentServer = MutableStateFlow<VpnServer?>(null)
    val currentServer: StateFlow<VpnServer?> = _currentServer.asStateFlow()

    // اتصال الخدمة
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as VpnGamingService.VpnServiceBinder
            vpnService = binder.getService()
            isBound = true
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            vpnService = null
            isBound = false
        }
    }

    /**
     * التحقق من إذن VPN
     */
    fun checkVpnPermission(): Intent? {
        return VpnService.prepare(context)
    }

    /**
     * الاتصال بخادم VPN
     */
    fun connect(server: VpnServer) {
        try {
            _currentServer.value = server
            _connectionState.value = VpnConnectionState.CONNECTING

            val intent = Intent(context, VpnGamingService::class.java).apply {
                action = VpnGamingService.ACTION_CONNECT
                putExtra(VpnGamingService.EXTRA_SERVER, server)
            }

            context.startForegroundService(intent)
            bindToService()

        } catch (e: Exception) {
            e.printStackTrace()
            _connectionState.value = VpnConnectionState.ERROR
        }
    }

    /**
     * قطع اتصال VPN
     */
    fun disconnect() {
        try {
            _connectionState.value = VpnConnectionState.DISCONNECTING

            val intent = Intent(context, VpnGamingService::class.java).apply {
                action = VpnGamingService.ACTION_DISCONNECT
            }

            context.startService(intent)
            unbindFromService()

            _currentServer.value = null
            _connectionState.value = VpnConnectionState.DISCONNECTED

        } catch (e: Exception) {
            e.printStackTrace()
            _connectionState.value = VpnConnectionState.ERROR
        }
    }

    /**
     * الربط بالخدمة
     */
    private fun bindToService() {
        if (!isBound) {
            val intent = Intent(context, VpnGamingService::class.java)
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
    }

    /**
     * إلغاء الربط من الخدمة
     */
    private fun unbindFromService() {
        if (isBound) {
            context.unbindService(serviceConnection)
            isBound = false
            vpnService = null
        }
    }

    /**
     * التحقق من حالة الاتصال
     */
    fun isConnected(): Boolean {
        return _connectionState.value == VpnConnectionState.CONNECTED
    }

    /**
     * التحقق من حالة الاتصال
     */
    fun isConnecting(): Boolean {
        return _connectionState.value == VpnConnectionState.CONNECTING
    }

    /**
     * الحصول على الخادم الحالي
     */
    fun getCurrentServer(): VpnServer? {
        return _currentServer.value
    }

    /**
     * تحديث حالة الاتصال
     */
    fun updateConnectionState(state: VpnConnectionState) {
        _connectionState.value = state
    }

    /**
     * إعادة الاتصال
     */
    fun reconnect() {
        val server = _currentServer.value
        if (server != null) {
            disconnect()
            // انتظار قصير قبل إعادة الاتصال
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                connect(server)
            }, 1000)
        }
    }

    /**
     * تنظيف الموارد
     */
    fun cleanup() {
        unbindFromService()
        _connectionState.value = VpnConnectionState.DISCONNECTED
        _currentServer.value = null
    }
}
