package com.vpngaming.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import android.content.Context
import com.vpngaming.app.data.model.VpnServer
import com.vpngaming.app.data.model.VpnProtocol

/**
 * قاعدة بيانات VPN الرئيسية
 */
@Database(
    entities = [VpnServer::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(VpnTypeConverters::class)
abstract class VpnDatabase : RoomDatabase() {

    abstract fun vpnDao(): VpnDao

    companion object {
        private const val DATABASE_NAME = "vpn_gaming_database"

        @Volatile
        private var INSTANCE: VpnDatabase? = null

        fun getDatabase(context: Context): VpnDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    VpnDatabase::class.java,
                    DATABASE_NAME
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

/**
 * محولات الأنواع لقاعدة البيانات
 */
class VpnTypeConverters {

    @TypeConverter
    fun fromVpnProtocol(protocol: VpnProtocol): String {
        return protocol.name
    }

    @TypeConverter
    fun toVpnProtocol(protocolName: String): VpnProtocol {
        return VpnProtocol.valueOf(protocolName)
    }
}
