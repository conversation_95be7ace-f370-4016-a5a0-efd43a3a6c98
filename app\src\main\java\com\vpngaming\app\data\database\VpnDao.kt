package com.vpngaming.app.data.database

import androidx.room.*
import com.vpngaming.app.data.model.VpnServer
import kotlinx.coroutines.flow.Flow

/**
 * واجهة الوصول لبيانات VPN
 */
@Dao
interface VpnDao {

    /**
     * الحصول على جميع الخوادم
     */
    @Query("SELECT * FROM vpn_servers ORDER BY country, city")
    fun getAllServers(): Flow<List<VpnServer>>

    /**
     * الحصول على الخوادم المتاحة فقط
     */
    @Query("SELECT * FROM vpn_servers WHERE isOnline = 1 ORDER BY country, city")
    fun getOnlineServers(): Flow<List<VpnServer>>

    /**
     * الحصول على الخوادم حسب البلد
     */
    @Query("SELECT * FROM vpn_servers WHERE country = :country AND isOnline = 1 ORDER BY city")
    fun getServersByCountry(country: String): Flow<List<VpnServer>>

    /**
     * الحصول على الخوادم المجانية
     */
    @Query("SELECT * FROM vpn_servers WHERE isPremium = 0 AND isOnline = 1 ORDER BY load ASC")
    fun getFreeServers(): Flow<List<VpnServer>>

    /**
     * الحصول على أسرع الخوادم
     */
    @Query("SELECT * FROM vpn_servers WHERE isOnline = 1 ORDER BY ping ASC, load ASC LIMIT 10")
    fun getFastestServers(): Flow<List<VpnServer>>

    /**
     * البحث عن خادم بالمعرف
     */
    @Query("SELECT * FROM vpn_servers WHERE id = :serverId")
    suspend fun getServerById(serverId: String): VpnServer?

    /**
     * البحث في الخوادم
     */
    @Query("""
        SELECT * FROM vpn_servers 
        WHERE (name LIKE '%' || :query || '%' 
        OR country LIKE '%' || :query || '%' 
        OR city LIKE '%' || :query || '%') 
        AND isOnline = 1 
        ORDER BY country, city
    """)
    fun searchServers(query: String): Flow<List<VpnServer>>

    /**
     * إدراج خادم جديد
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertServer(server: VpnServer)

    /**
     * إدراج قائمة خوادم
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertServers(servers: List<VpnServer>)

    /**
     * تحديث خادم
     */
    @Update
    suspend fun updateServer(server: VpnServer)

    /**
     * تحديث حالة الخادم
     */
    @Query("UPDATE vpn_servers SET isOnline = :isOnline, updatedAt = :timestamp WHERE id = :serverId")
    suspend fun updateServerStatus(serverId: String, isOnline: Boolean, timestamp: Long = System.currentTimeMillis())

    /**
     * تحديث ping الخادم
     */
    @Query("UPDATE vpn_servers SET ping = :ping, updatedAt = :timestamp WHERE id = :serverId")
    suspend fun updateServerPing(serverId: String, ping: Int, timestamp: Long = System.currentTimeMillis())

    /**
     * تحديث حمولة الخادم
     */
    @Query("UPDATE vpn_servers SET load = :load, updatedAt = :timestamp WHERE id = :serverId")
    suspend fun updateServerLoad(serverId: String, load: Int, timestamp: Long = System.currentTimeMillis())

    /**
     * حذف خادم
     */
    @Delete
    suspend fun deleteServer(server: VpnServer)

    /**
     * حذف جميع الخوادم
     */
    @Query("DELETE FROM vpn_servers")
    suspend fun deleteAllServers()

    /**
     * الحصول على عدد الخوادم
     */
    @Query("SELECT COUNT(*) FROM vpn_servers")
    suspend fun getServersCount(): Int

    /**
     * الحصول على عدد الخوادم المتاحة
     */
    @Query("SELECT COUNT(*) FROM vpn_servers WHERE isOnline = 1")
    suspend fun getOnlineServersCount(): Int
}
