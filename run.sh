#!/bin/bash

echo "========================================"
echo "       VPN Gaming - Android App"
echo "========================================"
echo

echo "تحقق من متطلبات النظام..."
if ! command -v gradle &> /dev/null; then
    echo "خطأ: Gradle غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت Gradle أو استخدام Android Studio"
    exit 1
fi

echo "تنظيف المشروع..."
./gradlew clean

echo "بناء المشروع..."
./gradlew build

if [ $? -eq 0 ]; then
    echo
    echo "========================================"
    echo "تم بناء المشروع بنجاح!"
    echo "========================================"
    echo
    echo "لتشغيل التطبيق:"
    echo "1. افتح Android Studio"
    echo "2. اربط جهاز Android أو شغل المحاكي"
    echo "3. اضغط على زر Run"
    echo
    echo "أو استخدم الأمر:"
    echo "./gradlew installDebug"
    echo
else
    echo
    echo "========================================"
    echo "فشل في بناء المشروع!"
    echo "========================================"
    echo "يرجى التحقق من الأخطاء أعلاه"
    echo
fi
