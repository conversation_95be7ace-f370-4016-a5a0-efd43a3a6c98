version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 1
JvmtiExport can_post_on_exceptions 0
# 503 ciObject found
instanceKlass org/lombokweb/asm/ClassReader
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 16 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x800000069
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x800000068
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getCalendarDataProvider ()Ljava/util/spi/CalendarDataProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000062
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Calendar$Builder
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getCalendarProvider ()Lsun/util/spi/CalendarProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000063
instanceKlass java/util/Calendar
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatProvider ()Ljava/text/spi/DateFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000064
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/DefaultLogFilter
instanceKlass org/eclipse/core/runtime/ILogListener
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/LogHandler
instanceKlass org/eclipse/jdt/internal/core/manipulation/MembersOrderPreferenceCacheCommon
instanceKlass org/eclipse/jdt/core/manipulation/JavaManipulation
instanceKlass org/eclipse/jdt/ls/core/contentassist/ICompletionContributionService
instanceKlass java/nio/channels/AsynchronousByteChannel
instanceKlass java/nio/channels/AsynchronousChannel
instanceKlass java/net/SocketAddress
instanceKlass org/eclipse/jdt/ls/core/internal/lsp/JavaProtocolExtensions
instanceKlass org/eclipse/jdt/ls/core/internal/syntaxserver/IExtendedProtocol
instanceKlass org/eclipse/lsp4j/services/WorkspaceService
instanceKlass org/eclipse/lsp4j/services/TextDocumentService
instanceKlass org/eclipse/lsp4j/services/LanguageServer
instanceKlass org/eclipse/jdt/ls/core/internal/BaseJDTLanguageServer
instanceKlass java/net/Authenticator
instanceKlass org/eclipse/text/templates/ContextTypeRegistry
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$21$1
instanceKlass  @bci org/eclipse/jdt/internal/core/search/processing/JobManager reset ()V 38 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/search/processing/JobManager$$Lambda+0x0000028481214230
instanceKlass org/eclipse/jdt/internal/core/ExternalAnnotationTracker$DirectoryNode
instanceKlass org/eclipse/jdt/internal/core/ExternalAnnotationTracker
instanceKlass org/eclipse/jdt/core/IJavaModelStatusConstants
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481210400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481210000
instanceKlass org/eclipse/jdt/internal/compiler/CompilationResult
instanceKlass lombok/eclipse/agent/PatchDelegate$BindingTuple
instanceKlass org/eclipse/jdt/internal/compiler/ast/TypeOrLambda
instanceKlass org/eclipse/jdt/internal/compiler/ast/Invocation
instanceKlass org/eclipse/jdt/internal/compiler/ast/IPolyExpression
instanceKlass lombok/eclipse/agent/PatchDelegate
instanceKlass lombok/eclipse/agent/PatchDelegatePortal
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$Delegate
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessingState$RootInfos
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$20
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$19
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$14
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$13
instanceKlass org/eclipse/jdt/internal/formatter/DefaultCodeFormatterOptions
instanceKlass org/eclipse/jdt/core/formatter/DefaultCodeFormatterConstants
instanceKlass org/eclipse/jdt/internal/compiler/util/Util
instanceKlass org/eclipse/jdt/internal/compiler/impl/IrritantSet
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000028481209000
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481208c00
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/EclipsePreferences$$Lambda+0x0000028481175e40
instanceKlass  @cpi org/eclipse/core/internal/preferences/EclipsePreferences 1057 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481208800
instanceKlass org/eclipse/jdt/internal/core/util/ICacheEnumeration
instanceKlass org/eclipse/jdt/internal/formatter/TokenTraverser
instanceKlass org/eclipse/text/edits/TextEdit
instanceKlass org/eclipse/jdt/core/formatter/CodeFormatter
instanceKlass lombok/patcher/scripts/WrapperMethodDescriptor
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$1
instanceKlass org/eclipse/jdt/core/SourceRange
instanceKlass org/eclipse/jdt/internal/compiler/util/JRTUtil$JrtFileVisitor
instanceKlass org/eclipse/jdt/core/IOrdinaryClassFile
instanceKlass org/eclipse/jdt/internal/core/util/ReferenceInfoAdapter
instanceKlass org/eclipse/jdt/internal/compiler/ISourceElementRequestor
instanceKlass org/eclipse/jdt/core/search/TypeNameMatchRequestor
instanceKlass org/eclipse/jdt/internal/compiler/env/ISourceType
instanceKlass org/eclipse/jdt/internal/compiler/env/IGenericType
instanceKlass org/eclipse/jdt/internal/compiler/problem/ProblemHandler
instanceKlass org/eclipse/jdt/core/search/MethodNameMatch
instanceKlass org/eclipse/jdt/core/search/TypeNameMatch
instanceKlass org/eclipse/jdt/core/search/IJavaSearchScope
instanceKlass org/eclipse/jdt/core/search/SearchParticipant
instanceKlass org/eclipse/jdt/internal/compiler/ASTVisitor
instanceKlass org/eclipse/jdt/core/search/IParallelizable
instanceKlass org/eclipse/jdt/core/search/SearchPattern
instanceKlass org/eclipse/jdt/internal/core/search/IndexQueryRequestor
instanceKlass org/eclipse/jdt/internal/core/search/BasicSearchEngine
instanceKlass org/eclipse/jdt/core/ISourceRange
instanceKlass org/eclipse/jdt/core/ITypeParameter
instanceKlass org/eclipse/jdt/core/IAnnotation
instanceKlass org/eclipse/jdt/internal/core/AbstractModule
instanceKlass org/eclipse/jdt/core/IModuleDescription
instanceKlass org/eclipse/jdt/internal/core/NameLookup
instanceKlass org/eclipse/jface/text/IDocument
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache$1
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache
instanceKlass org/eclipse/jdt/core/IType
instanceKlass org/eclipse/jdt/core/IAnnotatable
instanceKlass org/eclipse/jdt/core/IMember
instanceKlass org/eclipse/jdt/internal/core/hierarchy/HierarchyBuilder
instanceKlass org/eclipse/jdt/internal/core/hierarchy/TypeHierarchy
instanceKlass org/eclipse/jdt/core/ITypeHierarchy
instanceKlass org/eclipse/jdt/core/dom/ASTNode
instanceKlass org/eclipse/jdt/core/dom/StructuralPropertyDescriptor
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEvent
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEventStore
instanceKlass org/eclipse/jdt/core/dom/ASTVisitor
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor
instanceKlass org/eclipse/jdt/internal/codeassist/CompletionEngine$1
instanceKlass  @bci org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding <clinit> ()V 46 <appendix> argL0 ; # org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$$Lambda+0x00000284811e47f8
instanceKlass  @bci org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding <clinit> ()V 46 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811e8400
instanceKlass  @cpi org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding 1523 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811e8000
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ParameterNonNullDefaultProvider
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$3
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$2
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReductionResult
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ElementValuePair
instanceKlass org/eclipse/jdt/internal/compiler/lookup/AnnotationBinding
instanceKlass org/eclipse/jdt/internal/compiler/env/IUpdatableModule
instanceKlass org/eclipse/jdt/internal/compiler/lookup/HotSwappable
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeBindingVisitor
instanceKlass org/eclipse/jdt/internal/compiler/parser/ScannerHelper
instanceKlass org/eclipse/jdt/core/Signature
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants$CloseMethodRecord
instanceKlass org/eclipse/jdt/internal/core/IJavaElementRequestor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Substitution
instanceKlass org/eclipse/jdt/internal/codeassist/UnresolvedReferenceNameFinder$UnresolvedReferenceNameRequestor
instanceKlass org/eclipse/jdt/core/search/SearchRequestor
instanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$1
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNode
instanceKlass org/eclipse/jdt/core/CompletionRequestor
instanceKlass org/eclipse/jdt/internal/codeassist/MissingTypesGuesser$GuessedTypeRequestor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Binding
instanceKlass org/eclipse/jdt/internal/core/INamingRequestor
instanceKlass org/eclipse/jdt/core/CompletionProposal
instanceKlass org/eclipse/jdt/core/compiler/IProblem
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Scope
instanceKlass org/eclipse/jdt/core/CompletionContext
instanceKlass org/eclipse/jdt/internal/compiler/env/INameEnvironment
instanceKlass org/eclipse/jdt/internal/compiler/lookup/InvocationSite
instanceKlass org/eclipse/jdt/internal/compiler/ast/ASTNode
instanceKlass org/eclipse/jdt/internal/codeassist/impl/Engine
instanceKlass org/eclipse/jdt/internal/codeassist/ICompletionEngine
instanceKlass org/eclipse/jdt/internal/codeassist/RelevanceConstants
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants
instanceKlass org/eclipse/jdt/internal/codeassist/ISearchRequestor
instanceKlass org/eclipse/jdt/internal/compiler/impl/ReferenceContext
instanceKlass org/eclipse/jdt/internal/compiler/ICompilerRequestor
instanceKlass org/eclipse/jdt/internal/compiler/Compiler
instanceKlass org/eclipse/jdt/internal/compiler/problem/ProblemSeverities
instanceKlass org/eclipse/jdt/internal/compiler/impl/ITypeRequestor
instanceKlass org/eclipse/jdt/internal/core/builder/ClasspathLocation
instanceKlass org/eclipse/jdt/core/IBuffer
instanceKlass org/eclipse/jdt/core/IBufferFactory
instanceKlass org/eclipse/jdt/internal/core/BufferManager
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$8
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager <clinit> ()V 139 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x00000284811bbd50
instanceKlass  @bci org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry <init> (Ljava/io/File;Lorg/eclipse/core/runtime/IPath;)V 24 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry$$Lambda+0x00000284811bbb38
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry
instanceKlass org/eclipse/jdt/internal/compiler/util/SimpleLookupTable
instanceKlass org/eclipse/jdt/internal/core/index/IndexLocation
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexRequest
instanceKlass org/eclipse/jdt/internal/compiler/parser/Parser
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeIds
instanceKlass org/eclipse/jdt/internal/compiler/ast/OperatorIds
instanceKlass org/eclipse/jdt/internal/compiler/parser/ConflictedParser
instanceKlass org/eclipse/jdt/internal/compiler/parser/ParserBasicInformation
instanceKlass org/eclipse/jdt/internal/compiler/IProblemFactory
instanceKlass org/eclipse/jdt/internal/core/search/processing/JobManager
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IIndexConstants
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager <init> ()V 404 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x00000284811ba3e8
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$3
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$2
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$EclipsePreferencesListener
instanceKlass org/eclipse/jdt/core/IElementChangedListener
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessingState
instanceKlass org/eclipse/core/internal/dtree/DataTreeLookup
instanceKlass org/eclipse/jdt/internal/core/ExternalFoldersManager
instanceKlass org/eclipse/jdt/internal/core/util/Util$Comparer
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$CompilationParticipants
instanceKlass org/eclipse/jdt/internal/core/BatchInitializationMonitor
instanceKlass org/eclipse/jdt/internal/core/JavaModelOperation
instanceKlass org/eclipse/jdt/internal/core/JavaElementInfo
instanceKlass org/eclipse/jdt/internal/codeassist/ISelectionRequestor
instanceKlass org/eclipse/jdt/core/IJavaModelStatus
instanceKlass org/eclipse/jdt/internal/compiler/env/IElementInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$1
instanceKlass org/eclipse/jdt/internal/core/search/processing/IJob
instanceKlass org/eclipse/jdt/core/IAccessRule
instanceKlass org/eclipse/jdt/core/IClassFile
instanceKlass org/eclipse/jdt/core/IPackageFragment
instanceKlass org/eclipse/jdt/internal/core/search/IRestrictedAccessTypeRequestor
instanceKlass org/eclipse/jdt/internal/core/util/LRUCache
instanceKlass org/eclipse/jdt/core/IJavaElementDelta
instanceKlass org/eclipse/jdt/internal/compiler/util/SuffixConstants
instanceKlass org/eclipse/jdt/internal/compiler/env/ICompilationUnit
instanceKlass org/eclipse/jdt/internal/compiler/env/IDependent
instanceKlass org/eclipse/jdt/core/ICompilationUnit
instanceKlass org/eclipse/jdt/core/ISourceManipulation
instanceKlass org/eclipse/jdt/core/ITypeRoot
instanceKlass org/eclipse/jdt/core/ICodeAssist
instanceKlass org/eclipse/jdt/core/ISourceReference
instanceKlass org/lombokweb/asm/Handle
instanceKlass org/eclipse/jdt/core/IBufferChangedListener
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284811abc00
instanceKlass org/eclipse/jdt/core/IPackageFragmentRoot
instanceKlass org/eclipse/jdt/internal/compiler/util/Util$Displayable
instanceKlass org/eclipse/jdt/core/IJavaProject
instanceKlass org/eclipse/jdt/core/IClasspathContainer
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager
instanceKlass  @bci java/util/Comparator thenComparing (Ljava/util/Comparator;)Ljava/util/Comparator; 7 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x00000284810f65b0
instanceKlass  @cpi java/util/Comparator 251 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811ab800
instanceKlass  @bci java/util/Comparator comparingDouble (Ljava/util/function/ToDoubleFunction;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x00000284810f6320
instanceKlass  @bci org/eclipse/jdt/core/JavaCore <clinit> ()V 200 <appendix> argL0 ; # org/eclipse/jdt/core/JavaCore$$Lambda+0x00000284811a9cb0
instanceKlass  @cpi org/eclipse/jdt/core/JavaCore 2468 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811ab400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000284811ab000
instanceKlass java/util/function/ToDoubleFunction
instanceKlass org/eclipse/jdt/core/compiler/CharOperation
instanceKlass org/eclipse/jdt/internal/compiler/impl/CompilerOptions
instanceKlass org/eclipse/jdt/core/search/TypeNameRequestor
instanceKlass org/eclipse/jdt/core/IWorkingCopy
instanceKlass org/eclipse/jdt/core/IClasspathAttribute
instanceKlass org/eclipse/jdt/internal/compiler/env/IModule
instanceKlass org/eclipse/jdt/core/IClasspathEntry
instanceKlass org/eclipse/jdt/core/IRegion
instanceKlass org/eclipse/jdt/core/IJavaModel
instanceKlass org/eclipse/jdt/core/IParent
instanceKlass org/eclipse/jdt/core/IOpenable
instanceKlass org/eclipse/jdt/core/IJavaElement
instanceKlass org/eclipse/core/resources/IWorkspaceRunnable
instanceKlass org/eclipse/core/internal/jobs/JobQueue$2
instanceKlass org/eclipse/jdt/core/WorkingCopyOwner
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ISourceDownloader
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager
instanceKlass org/eclipse/core/internal/events/NodeIDMap
instanceKlass org/eclipse/core/internal/events/ResourceDeltaInfo
instanceKlass org/eclipse/core/internal/dtree/NodeComparison
instanceKlass org/eclipse/core/internal/events/ResourceComparator
instanceKlass org/eclipse/core/internal/events/ResourceDeltaFactory
instanceKlass org/eclipse/core/resources/IMarkerDelta
instanceKlass org/eclipse/core/runtime/SubMonitor$RootInfo
instanceKlass org/eclipse/core/runtime/SubMonitor
instanceKlass org/eclipse/core/resources/team/ResourceRuleFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284811a2800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284811a2400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284811a2000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284811a1c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000284811a1800
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$KeyIterator
instanceKlass org/eclipse/core/resources/IResourceChangeEvent
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass  @bci org/apache/felix/scr/impl/manager/ComponentContextImpl createNewFieldHandlerMap ()Ljava/util/Map; 4 <appendix> argL0 ; # org/apache/felix/scr/impl/manager/ComponentContextImpl$$Lambda+0x000002848114ea80
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getLog (Lorg/osgi/framework/Bundle;)Lorg/eclipse/core/runtime/ILog; 13 <appendix> member <vmtarget> ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x000002848118ba98
instanceKlass org/eclipse/core/internal/runtime/Log
instanceKlass org/eclipse/core/internal/preferences/BundleStateScope
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$Resolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$FieldSearchResult
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$ReferenceMethodImpl
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$State
instanceKlass org/apache/felix/scr/impl/inject/InitReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler
instanceKlass org/apache/felix/scr/impl/inject/field/FieldMethods
instanceKlass org/eclipse/core/internal/resources/CheckMissingNaturesListener
instanceKlass org/eclipse/core/internal/resources/Rules
instanceKlass org/eclipse/core/internal/resources/ResourceChangeListenerRegistrar
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x00000284811a1400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811a1000
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x0000028481162ca0
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1512 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811a0c00
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x00000284811a0800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811a0400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x0000028481162a68
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1468 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284811a0000
instanceKlass  @bci org/eclipse/core/internal/resources/AliasManager$LocationMap <init> (Lorg/eclipse/core/internal/resources/AliasManager;)V 14 <appendix> argL0 ; # org/eclipse/core/internal/resources/AliasManager$LocationMap$$Lambda+0x000002848119b000
instanceKlass org/eclipse/core/filesystem/IFileStore
instanceKlass org/eclipse/core/internal/resources/AliasManager$LocationMap
instanceKlass org/eclipse/core/internal/resources/AliasManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshMonitor
instanceKlass org/eclipse/core/internal/refresh/MonitorManager
instanceKlass org/eclipse/core/resources/IResourceDeltaVisitor
instanceKlass org/eclipse/core/resources/IPathVariableChangeListener
instanceKlass org/eclipse/core/internal/refresh/RefreshManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshResult
instanceKlass  @bci org/eclipse/core/internal/resources/ContentDescriptionManager getCurrentPlatformState ()Ljava/lang/String; 39 <appendix> argL0 ; # org/eclipse/core/internal/resources/ContentDescriptionManager$$Lambda+0x000002848119e320
instanceKlass org/eclipse/core/internal/resources/ProjectContentTypes
instanceKlass org/eclipse/core/internal/utils/Cache
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager
instanceKlass  @bci org/eclipse/core/internal/resources/CharsetManager initPreferenceChangeListener ()V 2 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/CharsetManager$$Lambda+0x000002848119d3f0
instanceKlass  @bci org/eclipse/core/internal/jobs/Worker <init> (Lorg/eclipse/core/internal/jobs/WorkerPool;)V 10 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002848119a400
instanceKlass  @bci org/eclipse/core/internal/jobs/Worker <init> (Lorg/eclipse/core/internal/jobs/WorkerPool;)V 10 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002848119a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481199c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481199800
instanceKlass  @bci org/eclipse/core/internal/jobs/Worker <init> (Lorg/eclipse/core/internal/jobs/WorkerPool;)V 10 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481199400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481199000
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager now ()J 5 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x0000028481162830
instanceKlass java/util/function/LongUnaryOperator
instanceKlass org/eclipse/core/internal/jobs/JobChangeEvent
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000028481198c00
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481198800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x00000284811623a0
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1485 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481198400
instanceKlass org/eclipse/core/internal/resources/CharsetDeltaJob$ICharsetListenerFilter
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 29 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002848116f428
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 19 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002848116f1e8
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 9 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002848116efb0
instanceKlass org/eclipse/core/runtime/PerformanceStats
instanceKlass org/eclipse/core/internal/events/ResourceStats
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList$ListenerEntry
instanceKlass org/eclipse/core/internal/resources/CharsetManager$ResourceChangeListener
instanceKlass org/eclipse/core/resources/IResourceChangeListener
instanceKlass org/eclipse/core/internal/resources/CharsetManager
instanceKlass org/eclipse/core/internal/localstore/Bucket$Entry
instanceKlass org/eclipse/core/internal/localstore/BucketTree
instanceKlass org/eclipse/core/internal/localstore/Bucket$Visitor
instanceKlass org/eclipse/core/internal/localstore/Bucket
instanceKlass org/eclipse/core/internal/properties/PropertyManager2
instanceKlass org/eclipse/core/resources/IFileState
instanceKlass org/eclipse/core/internal/watson/ElementTree$ChildIDsCache
instanceKlass  @bci org/eclipse/core/internal/resources/SaveManager initSnap (Lorg/eclipse/core/runtime/IProgressMonitor;)V 67 <appendix> argL0 ; # org/eclipse/core/internal/resources/SaveManager$$Lambda+0x0000028481194e90
instanceKlass java/io/FilenameFilter
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager cancel (Lorg/eclipse/core/internal/jobs/InternalJob;)Z 15 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481198000
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager cancel (Lorg/eclipse/core/internal/jobs/InternalJob;)Z 15 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x0000028481162168
instanceKlass org/eclipse/core/resources/ISaveContext
instanceKlass org/eclipse/core/resources/ISavedState
instanceKlass org/eclipse/core/internal/resources/SaveManager
instanceKlass org/eclipse/core/internal/watson/IElementInfoFlattener
instanceKlass org/eclipse/core/internal/resources/SyncInfoWriter
instanceKlass org/eclipse/core/internal/resources/Synchronizer
instanceKlass org/eclipse/core/internal/resources/MarkerWriter
instanceKlass org/eclipse/core/internal/resources/MarkerDeltaManager
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache$MarkerTypeDefinition
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache
instanceKlass org/eclipse/core/internal/resources/MarkerInfo
instanceKlass org/eclipse/core/internal/resources/IMarkerSetElement
instanceKlass org/eclipse/core/internal/resources/MarkerManager
instanceKlass  @bci org/eclipse/core/internal/events/NotificationManager$NotifyJob <init> (Lorg/eclipse/core/internal/events/NotificationManager;)V 13 <appendix> argL0 ; # org/eclipse/core/internal/events/NotificationManager$NotifyJob$$Lambda+0x0000028481191608
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList
instanceKlass org/eclipse/core/internal/events/NotificationManager
instanceKlass java/util/stream/SortedOps
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getBundles0 (Ljava/lang/String;Ljava/lang/String;)Ljava/util/stream/Stream; 90 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x000002848118b2f8
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getBundles0 (Ljava/lang/String;Ljava/lang/String;)Ljava/util/stream/Stream; 80 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x000002848118b0c8
instanceKlass org/eclipse/core/internal/events/BuildManager$DeltaCache
instanceKlass org/eclipse/core/resources/ICommand
instanceKlass org/eclipse/core/resources/IResourceDelta
instanceKlass org/eclipse/core/internal/events/InternalBuilder
instanceKlass org/eclipse/core/resources/IBuildContext
instanceKlass org/eclipse/core/internal/events/BuildManager
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager$1
instanceKlass org/eclipse/core/internal/resources/FilterDescriptor
instanceKlass org/eclipse/core/resources/IFilterMatcherDescriptor
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager
instanceKlass org/eclipse/core/resources/IProjectNatureDescriptor
instanceKlass org/eclipse/core/internal/resources/NatureManager
instanceKlass org/eclipse/core/internal/events/ILifecycleListener
instanceKlass org/eclipse/core/internal/resources/PathVariableManager
instanceKlass  @bci org/eclipse/core/internal/resources/WorkspaceRoot getProject (Ljava/lang/String;)Lorg/eclipse/core/resources/IProject; 95 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/WorkspaceRoot$$Lambda+0x000002848118e968
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/FieldPosition$Delegate
instanceKlass java/text/Format$FieldDelegate
instanceKlass java/text/DigitList
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDecimalFormatSymbolsProvider ()Ljava/text/spi/DecimalFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000066
instanceKlass java/text/DecimalFormatSymbols
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000010
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x80000005f
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getNumberFormatProvider ()Ljava/text/spi/NumberFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000067
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000061
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/text/FieldPosition
instanceKlass java/text/Format
instanceKlass  @bci org/eclipse/core/internal/localstore/FileSystemResourceManager <init> (Lorg/eclipse/core/internal/resources/Workspace;)V 6 <appendix> member <vmtarget> ; # org/eclipse/core/internal/localstore/FileSystemResourceManager$$Lambda+0x000002848118d598
instanceKlass org/eclipse/core/internal/localstore/RefreshLocalVisitor
instanceKlass org/eclipse/core/internal/localstore/ILocalStoreConstants
instanceKlass org/eclipse/core/internal/localstore/IHistoryStore
instanceKlass org/eclipse/core/internal/localstore/IUnifiedTreeVisitor
instanceKlass org/eclipse/core/internal/localstore/FileSystemResourceManager
instanceKlass org/eclipse/core/runtime/jobs/MultiRule
instanceKlass org/eclipse/core/internal/jobs/OrderedLock
instanceKlass java/lang/invoke/DirectMethodHandle$1
instanceKlass org/eclipse/core/internal/runtime/LocalizationUtils
instanceKlass org/eclipse/core/runtime/Status
instanceKlass org/eclipse/core/internal/resources/WorkManager$NotifyRule
instanceKlass org/eclipse/core/internal/resources/WorkManager
instanceKlass org/eclipse/core/runtime/NullProgressMonitor
instanceKlass org/eclipse/core/runtime/preferences/IExportedPreferences
instanceKlass org/eclipse/core/runtime/Preferences$1
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences firePreferenceEvent (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V 41 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/EclipsePreferences$$Lambda+0x0000028481175088
instanceKlass sun/management/Util
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x00000284810f1588
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x00000284810f1368
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x00000284810f1158
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 63 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x00000284810f0f20
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 47 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x00000284810f0cd8
instanceKlass java/util/Collections$2
instanceKlass sun/management/RuntimeImpl
instanceKlass java/util/stream/ReduceOps$2ReducingSink
instanceKlass  @bci java/lang/management/ManagementFactory$PlatformMBeanFinder findSingleton (Ljava/lang/Class;)Lsun/management/spi/PlatformMBeanProvider$PlatformComponent; 30 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda+0x00000284810eff38
instanceKlass  @bci java/lang/management/ManagementFactory$PlatformMBeanFinder findSingleton (Ljava/lang/Class;)Lsun/management/spi/PlatformMBeanProvider$PlatformComponent; 19 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda+0x00000284810efcf0
instanceKlass jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent
instanceKlass jdk/management/jfr/FlightRecorderMXBean
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$11
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$10
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$9
instanceKlass java/util/logging/LogManager
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess$1
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$8
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$7
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$6
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$5
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$4
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$3
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$2
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$1
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$5
instanceKlass sun/management/VMManagementImpl
instanceKlass sun/management/VMManagement
instanceKlass sun/management/ManagementFactoryHelper
instanceKlass sun/management/NotificationEmitterSupport
instanceKlass javax/management/NotificationEmitter
instanceKlass javax/management/NotificationBroadcaster
instanceKlass com/sun/management/DiagnosticCommandMBean
instanceKlass javax/management/DynamicMBean
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$4
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$3
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$2
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$1
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent
instanceKlass  @bci com/sun/management/internal/PlatformMBeanProviderImpl <clinit> ()V 8 <appendix> argL0 ; # com/sun/management/internal/PlatformMBeanProviderImpl$$Lambda+0x00000284810eafe8
instanceKlass sun/management/spi/PlatformMBeanProvider
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$1
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder
instanceKlass java/lang/management/RuntimeMXBean
instanceKlass java/lang/management/PlatformManagedObject
instanceKlass  @bci java/lang/management/ManagementFactory loadNativeLib ()V 0 <appendix> argL0 ; # java/lang/management/ManagementFactory$$Lambda+0x00000284810e9c08
instanceKlass java/lang/management/ManagementFactory
instanceKlass org/eclipse/core/internal/preferences/PreferencesService$1
instanceKlass  @bci org/eclipse/core/internal/preferences/PreferencesService getLookupOrder (Ljava/lang/String;Ljava/lang/String;)[Ljava/lang/String; 29 <appendix> argL0 ; # org/eclipse/core/internal/preferences/PreferencesService$$Lambda+0x0000028481174a10
instanceKlass java/util/function/IntFunction
instanceKlass  @bci org/eclipse/core/internal/resources/WorkspacePreferences <init> ()V 189 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/WorkspacePreferences$$Lambda+0x0000028481173720
instanceKlass org/eclipse/core/runtime/Preferences$IPropertyChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$INodeChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$IPreferenceChangeListener
instanceKlass  @bci org/eclipse/core/runtime/Plugin getPluginPreferences ()Lorg/eclipse/core/runtime/Preferences; 65 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/Plugin$$Lambda+0x000002848118a500
instanceKlass org/eclipse/core/runtime/Preferences
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481188000
instanceKlass lombok/eclipse/agent/PatchFixesShadowLoaded
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$LombokDeps
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000028481186c00
instanceKlass lombok/launch/PackageShader
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481186800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481186400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481186000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481185c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481185800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481183c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481183800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481183400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481183000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481182800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481182400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481182000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481181c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481181800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481181400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481181000
instanceKlass java/lang/constant/ClassDesc
instanceKlass java/io/ObjectOutput
instanceKlass java/io/ObjectStreamConstants
instanceKlass java/io/ObjectInput
instanceKlass java/lang/foreign/ValueLayout
instanceKlass java/lang/foreign/MemoryLayout
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481180c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481180800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481180400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481180000
instanceKlass org/eclipse/core/internal/runtime/Product
instanceKlass org/eclipse/core/runtime/IProduct
instanceKlass lombok/patcher/scripts/WrapReturnValuesScript$1
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerProxy$1
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerSetter
instanceKlass org/eclipse/osgi/internal/url/NullURLStreamHandlerService
instanceKlass org/osgi/service/url/URLStreamHandlerSetter
instanceKlass  @bci org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl createInternalURLStreamHandler (Ljava/lang/String;)Ljava/net/URLStreamHandler; 18 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl$$Lambda+0x000002848116e458
instanceKlass org/eclipse/equinox/internal/app/ProductExtensionBranding
instanceKlass org/eclipse/core/internal/runtime/FindSupport
instanceKlass org/eclipse/core/runtime/FileLocator
instanceKlass  @bci org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl getBundles (Ljava/lang/String;Ljava/lang/String;)[Lorg/osgi/framework/Bundle; 281 <appendix> argL0 ; # org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl$$Lambda+0x000002848111bce8
instanceKlass org/eclipse/core/runtime/SafeRunner
instanceKlass  @bci org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper runInitializer (Lorg/eclipse/core/runtime/IConfigurationElement;)V 18 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper$$Lambda+0x0000028481174200
instanceKlass org/eclipse/core/runtime/IExecutableExtensionFactory
instanceKlass org/eclipse/core/runtime/IExecutableExtension
instanceKlass org/eclipse/core/runtime/preferences/AbstractPreferenceInitializer
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTree
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTreeNode
instanceKlass org/eclipse/core/internal/watson/ElementTree
instanceKlass org/eclipse/core/internal/runtime/DataArea
instanceKlass org/eclipse/core/internal/runtime/MetaDataKeeper
instanceKlass org/eclipse/core/internal/resources/LocalMetaArea
instanceKlass org/eclipse/core/internal/resources/LocationValidator
instanceKlass org/eclipse/core/runtime/Platform$OS
instanceKlass org/eclipse/core/internal/utils/FileUtil
instanceKlass org/eclipse/core/resources/IResourceFilterDescription
instanceKlass org/eclipse/core/internal/watson/IElementContentVisitor
instanceKlass org/eclipse/core/resources/team/IResourceTree
instanceKlass org/eclipse/core/resources/IMarker
instanceKlass org/eclipse/core/resources/IResourceProxy
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 29 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002848116d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848116d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848116d000
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass java/util/ResourceBundle
instanceKlass  @bci org/eclipse/osgi/util/NLS <clinit> ()V 7 <appendix> argL0 ; # org/eclipse/osgi/util/NLS$$Lambda+0x000002848111bad8
instanceKlass org/eclipse/osgi/util/NLS
instanceKlass org/eclipse/core/runtime/Platform
instanceKlass org/eclipse/core/resources/team/FileModificationValidationContext
instanceKlass org/eclipse/core/resources/IBuildConfiguration
instanceKlass org/eclipse/core/resources/ISynchronizer
instanceKlass org/eclipse/core/resources/IResourceRuleFactory
instanceKlass org/eclipse/core/resources/IPathVariableManager
instanceKlass org/eclipse/core/resources/IProjectDescription
instanceKlass org/eclipse/core/internal/properties/IPropertyManager
instanceKlass org/eclipse/core/internal/resources/IManager
instanceKlass org/eclipse/core/resources/team/IMoveDeleteHook
instanceKlass org/eclipse/core/internal/resources/InternalTeamHook
instanceKlass org/eclipse/core/internal/watson/IElementComparator
instanceKlass org/eclipse/core/internal/dtree/IComparator
instanceKlass org/eclipse/core/resources/IProject
instanceKlass org/eclipse/core/internal/resources/ModelObject
instanceKlass org/eclipse/core/resources/IWorkspaceDescription
instanceKlass org/eclipse/core/resources/IFile
instanceKlass org/eclipse/core/resources/IEncodedStorage
instanceKlass org/eclipse/core/resources/IStorage
instanceKlass org/eclipse/core/resources/IFolder
instanceKlass org/eclipse/core/internal/watson/IPathRequestor
instanceKlass org/eclipse/core/internal/resources/ResourceInfo
instanceKlass org/eclipse/core/internal/utils/IStringPoolParticipant
instanceKlass org/eclipse/core/runtime/ICoreRunnable
instanceKlass org/eclipse/core/internal/watson/IElementTreeData
instanceKlass org/eclipse/core/internal/jobs/WorkerPool
instanceKlass org/eclipse/core/internal/jobs/JobQueue
instanceKlass org/eclipse/core/internal/jobs/DeadlockDetector
instanceKlass org/eclipse/core/internal/jobs/LockManager
instanceKlass org/eclipse/core/runtime/jobs/JobChangeAdapter
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager <init> ()V 52 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002848115fc20
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 50 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002848115fa10
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 41 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002848115f800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 32 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002848115ec58
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 23 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002848115ea48
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 14 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002848115e838
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 5 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002848115e628
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobListeners 385 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002848115f000
instanceKlass org/eclipse/core/internal/jobs/JobListeners$IListenerDoit
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeEvent
instanceKlass org/eclipse/core/internal/jobs/JobListeners
instanceKlass org/eclipse/core/internal/jobs/ImplicitJobs
instanceKlass org/eclipse/core/internal/jobs/JobManager$1
instanceKlass org/eclipse/core/runtime/ProgressMonitorWrapper
instanceKlass org/eclipse/core/runtime/IProgressMonitorWithBlocking
instanceKlass org/eclipse/core/internal/jobs/InternalJobGroup
instanceKlass org/eclipse/core/runtime/jobs/ILock
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeListener
instanceKlass org/eclipse/core/internal/jobs/JobManager
instanceKlass org/eclipse/core/runtime/jobs/IJobManager
instanceKlass org/eclipse/core/internal/jobs/JobOSGiUtils
instanceKlass org/eclipse/core/internal/jobs/JobActivator
instanceKlass org/eclipse/core/resources/IWorkspaceRoot
instanceKlass org/eclipse/core/resources/IContainer
instanceKlass org/eclipse/core/resources/IResource
instanceKlass org/eclipse/core/runtime/jobs/ISchedulingRule
instanceKlass org/eclipse/core/runtime/PlatformObject
instanceKlass org/eclipse/core/internal/resources/ICoreConstants
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass java/util/Formatter
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1075 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000e
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1067 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000d
instanceKlass java/time/Period
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/format/DateTimeFormatterBuilder$TextPrinterParser
instanceKlass java/time/format/DateTimeTextProvider$1
instanceKlass java/time/format/DateTimeTextProvider
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass java/time/format/DateTimeTextProvider$LocaleStore
instanceKlass java/time/format/DateTimeFormatterBuilder$InstantPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$StringLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$ZoneIdPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$OffsetIdPrinterParser
instanceKlass java/time/format/DecimalStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$CompositePrinterParser
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/format/DateTimeFormatterBuilder$CharLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$NumberPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$DateTimePrinterParser
instanceKlass java/time/temporal/JulianFields
instanceKlass java/time/temporal/IsoFields
instanceKlass  @bci java/time/format/DateTimeFormatterBuilder <clinit> ()V 0 <appendix> argL0 ; # java/time/format/DateTimeFormatterBuilder$$Lambda+0x80000000f
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/format/DateTimeFormatterBuilder
instanceKlass java/time/format/DateTimeFormatter
instanceKlass org/eclipse/osgi/internal/debug/EclipseDebugTrace
instanceKlass org/eclipse/core/internal/utils/Policy$1
instanceKlass org/eclipse/core/runtime/IProgressMonitor
instanceKlass org/eclipse/core/internal/utils/Policy
instanceKlass org/eclipse/core/resources/ResourcesPlugin$WorkspaceInitCustomizer
instanceKlass org/eclipse/core/resources/IWorkspace
instanceKlass org/eclipse/core/runtime/IAdaptable
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ProjectsManager
instanceKlass org/eclipse/jdt/ls/core/internal/managers/IProjectsManager
instanceKlass org/eclipse/core/resources/ISaveParticipant
instanceKlass org/eclipse/core/internal/runtime/LogServiceFactory
instanceKlass org/eclipse/equinox/internal/app/AppCommands
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000028481157400
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481157000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481156c00
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form names 6 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481156800
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481156400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481156000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481155c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481155800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481155400
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form names 10 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481155000
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481154c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481154800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481154400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481154000
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer$RegisterService
instanceKlass org/eclipse/core/runtime/spi/RegistryContributor
instanceKlass org/eclipse/equinox/app/IApplicationContext
instanceKlass org/osgi/service/application/ApplicationHandle
instanceKlass org/osgi/service/application/ApplicationDescriptor
instanceKlass org/eclipse/osgi/service/runnable/ApplicationLauncher
instanceKlass org/eclipse/equinox/internal/app/IBranding
instanceKlass org/eclipse/osgi/service/runnable/ApplicationRunnable
instanceKlass org/eclipse/osgi/service/runnable/ParameterizedRunnable
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer
instanceKlass org/osgi/service/application/ScheduledApplication
instanceKlass org/eclipse/equinox/internal/app/AppPersistence
instanceKlass org/eclipse/equinox/internal/app/Activator
instanceKlass org/eclipse/equinox/internal/app/CommandLineArgs
instanceKlass org/eclipse/core/internal/preferences/legacy/InitLegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/legacy/ProductPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/IProductPreferencesService
instanceKlass org/eclipse/core/internal/runtime/AuthorizationHandler
instanceKlass org/eclipse/core/runtime/IBundleGroupProvider
instanceKlass java/util/Collections$ReverseComparator2
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x00000284810e3268
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform <clinit> ()V 98 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x00000284811508a0
instanceKlass org/eclipse/core/runtime/ILog
instanceKlass org/eclipse/core/internal/runtime/InternalPlatform
instanceKlass org/eclipse/core/runtime/Plugin
instanceKlass org/eclipse/osgi/internal/loader/buddy/PolicyHandler
instanceKlass org/osgi/util/promise/DeferredPromiseImpl$ResolveWith
instanceKlass  @bci org/osgi/util/promise/PromiseFactory$All run ()V 81 <appendix> member <vmtarget> ; # org/osgi/util/promise/PromiseFactory$All$$Lambda+0x000002848114b448
instanceKlass  @cpi org/osgi/util/promise/PromiseFactory$All 144 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002848114ac00
instanceKlass org/osgi/util/promise/PromiseImpl$Result
instanceKlass org/osgi/util/promise/PromiseFactory$All
instanceKlass org/osgi/util/promise/PromiseImpl$InlineCallback
instanceKlass org/eclipse/core/runtime/QualifiedName
instanceKlass org/apache/felix/scr/impl/inject/methods/ActivateMethod$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848114a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848114a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848114a000
instanceKlass org/apache/felix/scr/impl/inject/MethodResult
instanceKlass org/eclipse/core/internal/content/ContentTypeManager$ContentTypeRegistryChangeListener
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$OpenStatusImpl
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod$1
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$Resolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$MethodInfo
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$1
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$ISelectionPolicy
instanceKlass org/eclipse/core/internal/content/ContentTypeBuilder
instanceKlass org/eclipse/core/internal/content/ContentTypeCatalog
instanceKlass org/eclipse/core/internal/content/BasicDescription
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$IContentTypeChangeListener
instanceKlass org/eclipse/core/internal/preferences/BundleStateScopeServiceFactory
instanceKlass org/eclipse/core/internal/preferences/Activator$1
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getRequirementFilter (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 1 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481148c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481148800
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getRequirementFilter (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 1 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481148400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481148000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481147c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481147800
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry$ListenerInfo
instanceKlass org/eclipse/core/runtime/IContributor
instanceKlass org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper
instanceKlass  @bci org/eclipse/core/internal/preferences/OSGiPreferencesServiceManager getQualifier (Lorg/osgi/framework/Bundle;)Ljava/lang/String; 6 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481147400
instanceKlass  @bci org/eclipse/core/internal/preferences/OSGiPreferencesServiceManager getQualifier (Lorg/osgi/framework/Bundle;)Ljava/lang/String; 6 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481147000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481146c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481146800
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL1 argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481146400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481146000
instanceKlass org/eclipse/core/runtime/ListenerList$ListenerListIterator
instanceKlass org/eclipse/core/internal/preferences/OSGiPreferencesServiceManager
instanceKlass org/osgi/service/prefs/PreferencesService
instanceKlass org/eclipse/core/internal/preferences/AbstractScope
instanceKlass org/eclipse/core/runtime/preferences/IScopeContext
instanceKlass org/eclipse/core/runtime/Assert
instanceKlass org/eclipse/core/runtime/Path
instanceKlass org/eclipse/core/runtime/Path$Constants
instanceKlass org/eclipse/core/runtime/IPath
instanceKlass org/eclipse/core/internal/preferences/ImmutableMap
instanceKlass org/eclipse/core/internal/preferences/EclipsePreferences
instanceKlass org/eclipse/core/runtime/preferences/IScope
instanceKlass org/eclipse/core/internal/preferences/PreferencesService
instanceKlass org/eclipse/core/runtime/preferences/IPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/ILegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/PreferencesOSGiUtils
instanceKlass org/eclipse/core/internal/preferences/Activator
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils
instanceKlass org/eclipse/core/internal/content/ILazySource
instanceKlass org/eclipse/core/internal/adapter/AdapterManagerListener
instanceKlass org/eclipse/core/internal/runtime/IAdapterManagerProvider
instanceKlass org/eclipse/core/runtime/IRegistryEventListener
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryCommandProvider
instanceKlass org/eclipse/osgi/framework/console/CommandProvider
instanceKlass org/eclipse/core/internal/registry/RegistryProviderFactory
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryProviderOSGI
instanceKlass org/eclipse/core/internal/registry/osgi/EclipseBundleListener
instanceKlass org/eclipse/core/internal/registry/OffsetTable
instanceKlass org/eclipse/osgi/compatibility/state/ReadOnlyState
instanceKlass org/eclipse/core/internal/registry/HashtableOfStringAndInt
instanceKlass org/eclipse/core/internal/registry/KeyedHashSet
instanceKlass org/eclipse/core/runtime/IConfigurationElement
instanceKlass org/eclipse/core/internal/registry/Handle
instanceKlass org/eclipse/core/internal/registry/RegistryObjectManager
instanceKlass org/eclipse/core/internal/registry/IObjectManager
instanceKlass  @bci org/eclipse/core/internal/registry/RegistryProperties getContextProperty (Ljava/lang/String;)Ljava/lang/String; 18 <appendix> member <vmtarget> ; # org/eclipse/core/internal/registry/RegistryProperties$$Lambda+0x000002848113a9f0
instanceKlass org/eclipse/core/internal/registry/RegistryTimestamp
instanceKlass org/eclipse/core/internal/registry/TableReader
instanceKlass org/eclipse/core/runtime/ListenerList
instanceKlass org/eclipse/core/internal/registry/ReadWriteMonitor
instanceKlass org/eclipse/core/internal/registry/RegistryObjectFactory
instanceKlass org/eclipse/core/runtime/IExtensionDelta
instanceKlass org/eclipse/core/runtime/IExtensionPoint
instanceKlass org/eclipse/core/runtime/ISafeRunnable
instanceKlass org/eclipse/core/runtime/IExtension
instanceKlass org/eclipse/core/internal/registry/RegistryObject
instanceKlass org/eclipse/core/internal/registry/KeyedElement
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry
instanceKlass org/eclipse/core/runtime/spi/IDynamicExtensionRegistry
instanceKlass org/eclipse/core/runtime/IExtensionRegistry
instanceKlass org/eclipse/core/runtime/RegistryFactory
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$IEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap
instanceKlass org/eclipse/core/internal/registry/osgi/OSGIUtils
instanceKlass org/eclipse/core/internal/registry/osgi/EquinoxUtils
instanceKlass org/eclipse/core/internal/registry/RegistryProperties
instanceKlass org/eclipse/core/runtime/spi/IRegistryProvider
instanceKlass org/eclipse/core/runtime/spi/RegistryStrategy
instanceKlass org/eclipse/core/internal/registry/osgi/Activator
instanceKlass org/eclipse/core/runtime/IRegistryChangeListener
instanceKlass org/eclipse/core/internal/content/IContentTypeInfo
instanceKlass org/eclipse/core/runtime/content/IContentDescription
instanceKlass org/osgi/service/prefs/Preferences
instanceKlass org/eclipse/core/runtime/content/IContentType
instanceKlass org/eclipse/core/runtime/content/IContentTypeSettings
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentConstructorImpl
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/methods/BindMethods
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotApplicable
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$State
instanceKlass org/apache/felix/scr/impl/inject/BaseParameter
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod
instanceKlass org/eclipse/core/internal/content/ContentTypeMatcher
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager
instanceKlass org/eclipse/core/runtime/content/IContentTypeMatcher
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$1
instanceKlass org/apache/felix/scr/impl/helper/ComponentServiceObjectsHelper
instanceKlass org/apache/felix/scr/impl/manager/EdgeInfo
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl$ComponentInstanceImpl
instanceKlass org/osgi/service/component/ComponentInstance
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegStateWrapper
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator$ListenerInfo
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker$AbstractTracked
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListener
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker
instanceKlass java/util/Collections$ReverseComparator
instanceKlass org/apache/felix/scr/impl/helper/Coercions
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$AbstractCustomizer
instanceKlass org/apache/felix/scr/impl/inject/RefPair
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$Customizer
instanceKlass org/apache/felix/scr/impl/manager/ServiceTrackerCustomizer
instanceKlass org/apache/felix/scr/impl/inject/OpenStatus
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager
instanceKlass org/apache/felix/scr/impl/manager/ReferenceManager
instanceKlass org/osgi/util/promise/Deferred
instanceKlass org/apache/felix/scr/impl/inject/ScrComponentContext
instanceKlass org/apache/felix/scr/component/ExtComponentContext
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$SetImplementationObject
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker$1
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker
instanceKlass java/util/Timer$ThreadReaper
instanceKlass java/util/TaskQueue
instanceKlass java/util/Timer
instanceKlass org/apache/felix/scr/impl/inject/ComponentConstructor
instanceKlass org/apache/felix/scr/impl/inject/LifecycleMethod
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentMethodsImpl
instanceKlass org/apache/felix/scr/impl/metadata/TargetedPID
instanceKlass java/util/concurrent/CompletionStage
instanceKlass org/osgi/util/promise/PromiseImpl
instanceKlass org/osgi/util/promise/Promise
instanceKlass org/osgi/util/promise/PromiseFactory
instanceKlass org/osgi/util/promise/Promises
instanceKlass org/apache/felix/scr/impl/inject/ComponentMethods
instanceKlass org/osgi/service/component/ComponentFactory
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ConfigurableComponentHolder
instanceKlass org/apache/felix/scr/impl/manager/ComponentContainer
instanceKlass org/apache/felix/scr/impl/ComponentRegistryKey
instanceKlass org/apache/felix/scr/impl/metadata/PropertyMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ComponentMetadata
instanceKlass org/apache/felix/scr/impl/xml/XmlConstants
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants$ArrayEnumeration
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$LocatorProxy
instanceKlass org/xml/sax/ext/Locator2
instanceKlass org/xml/sax/Locator
instanceKlass com/sun/org/apache/xerces/internal/util/XMLSymbols
instanceKlass com/sun/org/apache/xerces/internal/util/XMLChar
instanceKlass com/sun/xml/internal/stream/Entity
instanceKlass com/sun/xml/internal/stream/util/BufferAllocator
instanceKlass com/sun/xml/internal/stream/util/ThreadLocalBufferAllocator
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$EncodingInfo
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLLimitAnalyzer
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLInputSource
instanceKlass com/sun/org/apache/xerces/internal/util/ErrorHandlerWrapper
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLErrorHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/ExternalSubsetResolver
instanceKlass com/sun/org/apache/xerces/internal/util/EntityResolverWrapper
instanceKlass org/xml/sax/ext/EntityResolver2
instanceKlass org/xml/sax/InputSource
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$AttributesProxy
instanceKlass org/xml/sax/ext/Attributes2
instanceKlass org/xml/sax/Attributes
instanceKlass org/xml/sax/AttributeList
instanceKlass com/sun/org/apache/xerces/internal/util/FeatureState
instanceKlass com/sun/org/apache/xerces/internal/util/PropertyState
instanceKlass com/sun/org/apache/xerces/internal/impl/msg/XMLMessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/util/MessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLVersionDetector
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationManager
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NMTOKENDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NOTATIONDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ENTITYDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ListDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDREFDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/StringDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DTDDVFactory
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/DTDGrammarBucket
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLAttributeDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLSimpleType
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLElementDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationState
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/ValidationContext
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/RevalidationHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidatorFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentFilter
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLEntityDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDProcessor
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelSource
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDSource
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLDTDDescription
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarDescription
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$TrailingMiscDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$PrologDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$XMLDeclDriver
instanceKlass com/sun/org/apache/xerces/internal/util/NamespaceSupport
instanceKlass com/sun/org/apache/xerces/internal/xni/NamespaceContext
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl$Attribute
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLAttributes
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$FragmentContentDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$Driver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack2
instanceKlass com/sun/org/apache/xerces/internal/xni/QName
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLString
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner
instanceKlass com/sun/xml/internal/stream/XMLBufferListener
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentSource
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLErrorReporter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLLocator
instanceKlass com/sun/xml/internal/stream/XMLEntityStorage
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl$AugmentationsItemsContainer
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/Augmentations
instanceKlass com/sun/org/apache/xerces/internal/util/XMLResourceIdentifierImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLResourceIdentifier
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLEntityResolver
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponent
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable$Entry
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable
instanceKlass jdk/xml/internal/JdkConstants
instanceKlass jdk/xml/internal/JdkXmlUtils
instanceKlass com/sun/org/apache/xerces/internal/util/ParserConfigurationSettings
instanceKlass com/sun/org/apache/xerces/internal/parsers/XML11Configurable
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLPullParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager
instanceKlass com/sun/org/apache/xerces/internal/parsers/XMLParser
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDContentModelHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDocumentHandler
instanceKlass org/xml/sax/XMLReader
instanceKlass org/xml/sax/Parser
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager
instanceKlass javax/xml/parsers/SAXParser
instanceKlass com/sun/org/apache/xerces/internal/xs/PSVIProvider
instanceKlass com/sun/org/apache/xerces/internal/jaxp/JAXPConstants
instanceKlass  @bci javax/xml/parsers/FactoryFinder newInstance (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/ClassLoader;ZZ)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x00000284810c8c90
instanceKlass  @bci jdk/xml/internal/SecuritySupport getContextClassLoader ()Ljava/lang/ClassLoader; 0 <appendix> argL0 ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000284810c8828
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x00000284810c8610
instanceKlass javax/xml/parsers/FactoryFinder$1
instanceKlass  @bci jdk/xml/internal/SecuritySupport getFileInputStream (Ljava/io/File;)Ljava/io/FileInputStream; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000284810c81d8
instanceKlass  @bci jdk/xml/internal/SecuritySupport doesFileExist (Ljava/io/File;)Z 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000284810c7fc0
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 6 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x00000284810c7da8
instanceKlass  @bci jdk/xml/internal/SecuritySupport getSystemProperty (Ljava/lang/String;)Ljava/lang/String; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000284810c7b90
instanceKlass jdk/xml/internal/SecuritySupport
instanceKlass javax/xml/parsers/FactoryFinder
instanceKlass javax/xml/parsers/SAXParserFactory
instanceKlass  @bci org/eclipse/osgi/storage/Storage lambda$7 (Ljava/util/List;Ljava/lang/String;)Ljava/util/stream/Stream; 7 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x0000028481119620
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 37 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x00000284810c6d10
instanceKlass java/util/function/BooleanSupplier
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 24 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x00000284810c6880
instanceKlass java/util/stream/StreamSpliterators
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$1
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass  @bci java/util/stream/AbstractPipeline spliterator ()Ljava/util/Spliterator; 103 <appendix> member <vmtarget> ; # java/util/stream/AbstractPipeline$$Lambda+0x00000284810c5a88
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 101 <appendix> argL0 ; # org/eclipse/osgi/storage/Storage$$Lambda+0x00000284811191a0
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 91 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x0000028481118f68
instanceKlass org/xml/sax/helpers/DefaultHandler
instanceKlass org/xml/sax/ErrorHandler
instanceKlass org/xml/sax/ContentHandler
instanceKlass org/xml/sax/DTDHandler
instanceKlass org/xml/sax/EntityResolver
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListenerContext
instanceKlass java/util/AbstractList$Itr
instanceKlass org/eclipse/core/internal/runtime/IAdapterFactoryExt
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge$LazyAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge
instanceKlass org/eclipse/core/runtime/IAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/TracingOptions$1
instanceKlass org/eclipse/core/internal/runtime/TracingOptions
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass org/osgi/service/url/URLStreamHandlerService
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller current ()Ljava/util/Optional; 4 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002848111f000
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getRequirementFilter (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 1 <appendix> argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002848111e000
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller trackCurrent ()Ljava/util/Optional; 19 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002848111dca8
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller getCurrent ()Ljava/util/Optional; 12 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002848111da70
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass org/eclipse/core/runtime/ServiceCaller$ReferenceAndService
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/eclipse/core/internal/runtime/AdapterManager
instanceKlass org/eclipse/core/runtime/IAdapterManager
instanceKlass org/eclipse/core/internal/runtime/PlatformURLConverter
instanceKlass org/eclipse/core/internal/runtime/RuntimeLog
instanceKlass org/eclipse/core/runtime/IStatus
instanceKlass org/eclipse/core/internal/runtime/PlatformLogWriter
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceImpl
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 39 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002848111c000
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 26 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x0000028481118230
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 17 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x0000028481118000
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 1 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002848110fbf0
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 27 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002848110f9c8
instanceKlass org/osgi/framework/connect/FrameworkUtilHelper
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 8 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002848110f5c8
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 31 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x0000028481116c88
instanceKlass org/osgi/framework/FrameworkUtil
instanceKlass org/eclipse/core/runtime/ServiceCaller
instanceKlass org/eclipse/core/internal/runtime/Activator
instanceKlass org/apache/felix/scr/impl/config/ScrMetaTypeProviderServiceFactory
instanceKlass org/apache/felix/scr/impl/config/ScrManagedServiceServiceFactory
instanceKlass org/apache/felix/scr/impl/ComponentCommands$2
instanceKlass org/apache/felix/scr/impl/ComponentCommands$1
instanceKlass org/apache/felix/scr/impl/ComponentCommands
instanceKlass org/eclipse/osgi/storage/ManifestLocalization$BundleResourceBundle
instanceKlass org/eclipse/osgi/storage/ManifestLocalization
instanceKlass org/apache/felix/scr/impl/Activator$ScrExtension
instanceKlass org/apache/felix/scr/impl/ComponentActorThread$1
instanceKlass org/apache/felix/scr/impl/ComponentActorThread
instanceKlass org/apache/felix/scr/impl/runtime/ServiceComponentRuntimeImpl
instanceKlass org/apache/felix/scr/impl/manager/ComponentHolder
instanceKlass org/apache/felix/scr/impl/manager/RegionConfigurationSupport
instanceKlass java/util/TimerTask
instanceKlass org/apache/felix/scr/impl/ComponentRegistry
instanceKlass org/osgi/util/tracker/BundleTracker
instanceKlass org/apache/felix/scr/impl/logger/ScrLogManager$1
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LogDomain
instanceKlass org/apache/felix/scr/impl/logger/LogManager$Lock
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LoggerFacade
instanceKlass org/apache/felix/scr/impl/logger/BundleLogger
instanceKlass org/apache/felix/scr/impl/logger/ComponentLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLogger
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLoggerFactory
instanceKlass org/osgi/service/component/ComponentContext
instanceKlass org/osgi/service/component/ComponentServiceObjects
instanceKlass org/apache/felix/scr/impl/inject/internal/ClassUtils
instanceKlass org/apache/felix/scr/impl/config/ScrConfigurationImpl
instanceKlass org/osgi/service/component/runtime/ServiceComponentRuntime
instanceKlass org/apache/felix/scr/impl/manager/ScrConfiguration
instanceKlass org/apache/felix/scr/impl/logger/LogConfiguration
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult
instanceKlass org/apache/felix/scr/impl/AbstractExtender
instanceKlass org/osgi/util/tracker/BundleTrackerCustomizer
instanceKlass org/osgi/framework/hooks/weaving/WeavingHook
instanceKlass org/eclipse/osgi/internal/weaving/WeavingHookConfigurator$WovenClassContext
instanceKlass org/eclipse/osgi/internal/weaving/WovenClassImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClass
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager$DefineContext
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$3
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel$2
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481110400
instanceKlass java/util/concurrent/CountDownLatch
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481110000
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1$1
instanceKlass org/osgi/dto/DTO
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread$Queued
instanceKlass  @bci org/eclipse/osgi/framework/eventmgr/EventManager getEventThread ()Lorg/eclipse/osgi/framework/eventmgr/EventManager$EventThread; 24 <appendix> member <vmtarget> ; # org/eclipse/osgi/framework/eventmgr/EventManager$$Lambda+0x000002848110a730
instanceKlass  @bci org/eclipse/osgi/internal/framework/EquinoxEventPublisher notifyEventHooksPrivileged (Lorg/osgi/framework/BundleEvent;Ljava/util/Collection;)V 98 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/EquinoxEventPublisher$$Lambda+0x000002848110a1e8
instanceKlass org/osgi/framework/hooks/bundle/EventHook
instanceKlass  @bci org/eclipse/osgi/internal/framework/BundleContextImpl notifyFindHooksPriviledged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/util/Collection;)V 73 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/BundleContextImpl$$Lambda+0x00000284811097c8
instanceKlass org/osgi/framework/hooks/bundle/FindHook
instanceKlass org/eclipse/osgi/framework/util/FilePath
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass org/eclipse/core/runtime/internal/adaptor/ConsoleManager
instanceKlass org/eclipse/core/runtime/internal/adaptor/DefaultStartupMonitor
instanceKlass org/eclipse/osgi/service/runnable/StartupMonitor
instanceKlass java/lang/Thread$Builder$OfVirtual
instanceKlass java/lang/Thread$Builder$OfPlatform
instanceKlass java/lang/Thread$Builder
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceFactoryUse$1
instanceKlass java/util/LinkedList$ListItr
instanceKlass java/util/LinkedList$Node
instanceKlass org/eclipse/osgi/internal/resolver/StateImpl
instanceKlass org/eclipse/osgi/service/resolver/ImportPackageSpecification
instanceKlass org/eclipse/osgi/service/resolver/ExportPackageDescription
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeDescription
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeSpecification
instanceKlass org/eclipse/osgi/service/resolver/BundleSpecification
instanceKlass org/eclipse/osgi/service/resolver/HostSpecification
instanceKlass org/eclipse/osgi/service/resolver/GenericSpecification
instanceKlass org/eclipse/osgi/service/resolver/VersionConstraint
instanceKlass org/eclipse/osgi/service/resolver/GenericDescription
instanceKlass org/eclipse/osgi/service/resolver/BundleDescription
instanceKlass org/eclipse/osgi/service/resolver/BaseDescription
instanceKlass org/eclipse/osgi/internal/resolver/StateObjectFactoryImpl
instanceKlass org/eclipse/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/service/resolver/State
instanceKlass org/eclipse/osgi/service/resolver/StateObjectFactory
instanceKlass org/eclipse/osgi/compatibility/state/PlatformAdminImpl
instanceKlass org/eclipse/osgi/service/resolver/PlatformAdmin
instanceKlass org/eclipse/osgi/compatibility/state/Activator
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$2
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer
instanceKlass org/eclipse/osgi/service/security/TrustEngine
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedContentConstants
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook$1
instanceKlass org/eclipse/osgi/internal/framework/XMLParsingServiceFactory
instanceKlass org/eclipse/osgi/storage/BundleLocalizationImpl
instanceKlass org/eclipse/osgi/service/localization/BundleLocalization
instanceKlass org/eclipse/osgi/storage/url/BundleURLConverter
instanceKlass org/eclipse/osgi/service/urlconversion/URLConverter
instanceKlass org/apache/felix/resolver/Logger
instanceKlass org/apache/felix/resolver/util/OpenHashMap
instanceKlass org/apache/felix/resolver/ResolutionError
instanceKlass org/apache/felix/resolver/ResolverImpl
instanceKlass org/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/internal/framework/legacy/StartLevelImpl
instanceKlass org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl
instanceKlass org/osgi/service/condition/ConditionImpl
instanceKlass org/osgi/service/condition/Condition
instanceKlass java/net/ContentHandler
instanceKlass java/net/ContentHandlerFactory
instanceKlass org/eclipse/osgi/internal/url/EquinoxFactoryManager
instanceKlass org/eclipse/osgi/internal/log/ConfigAdminListener
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 102 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x00000284810b9400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000284810b9000
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 102 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284810b8c00
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 102 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x00000284810bbb30
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1084 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284810b8800
instanceKlass org/osgi/framework/hooks/service/FindHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableCollection
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$Parser
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl
instanceKlass org/osgi/util/tracker/AbstractTracked
instanceKlass org/osgi/util/tracker/ServiceTracker
instanceKlass org/eclipse/osgi/internal/log/EventAdminAdapter
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$2
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot$SnapshotIterator
instanceKlass org/eclipse/osgi/framework/eventmgr/ListenerQueue
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventListenerHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Map;)V 78 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x00000284810bd8a0
instanceKlass org/osgi/framework/hooks/service/EventListenerHook
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Collection;)V 78 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x00000284810bd0e8
instanceKlass org/osgi/framework/hooks/service/EventHook
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot
instanceKlass org/osgi/framework/PrototypeServiceFactory
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsensitiveKey
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceReferenceImpl
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceUse
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284810b8400
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x00000284810b66d8
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1107 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000284810b8000
instanceKlass org/eclipse/osgi/internal/serviceregistry/HookContext
instanceKlass org/osgi/framework/UnfilteredServiceListener
instanceKlass org/eclipse/osgi/internal/serviceregistry/FilteredServiceListener
instanceKlass org/osgi/framework/hooks/service/ListenerHook$ListenerInfo
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Entry
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap
instanceKlass org/eclipse/osgi/internal/log/OrderedExecutor
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl$2
instanceKlass org/osgi/service/startlevel/StartLevel
instanceKlass org/osgi/service/packageadmin/PackageAdmin
instanceKlass org/eclipse/osgi/internal/framework/SystemBundleActivator
instanceKlass org/eclipse/osgi/internal/loader/classpath/TitleVersionVendor
instanceKlass org/eclipse/osgi/internal/loader/classpath/ManifestPackageAttributes
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry$PDEData
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry
instanceKlass org/eclipse/osgi/internal/loader/classpath/FragmentClasspath
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock$1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore
instanceKlass org/eclipse/osgi/internal/loader/BundleLoaderSources
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$1
instanceKlass org/eclipse/osgi/internal/loader/sources/PackageSource
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver$StorageSaverTask
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executors
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$1
instanceKlass org/osgi/framework/ServiceObjects
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl
instanceKlass org/osgi/framework/hooks/service/ListenerHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl
instanceKlass org/osgi/framework/ServiceRegistration
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager
instanceKlass org/eclipse/osgi/internal/framework/EquinoxEventPublisher
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284810adc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284810ad800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284810ad400
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleEntry
instanceKlass org/eclipse/osgi/container/ModuleDatabase$2
instanceKlass java/util/ArrayList$SubList$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284810ad000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284810acc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284810ac800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000284810ac400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000284810ac000
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass java/lang/reflect/TypeVariable
instanceKlass org/eclipse/osgi/container/ModuleWiring$LoaderInitializer
instanceKlass org/eclipse/osgi/container/ModuleWiring
instanceKlass org/eclipse/osgi/container/ModuleWire
instanceKlass org/osgi/framework/wiring/BundleWire
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/eclipse/osgi/internal/container/Capabilities$NamespaceSet
instanceKlass org/eclipse/osgi/container/ModuleRequirement
instanceKlass org/osgi/framework/wiring/BundleRequirement
instanceKlass org/eclipse/osgi/container/ModuleRevision$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$8
instanceKlass org/eclipse/osgi/container/ModuleCapability
instanceKlass org/osgi/framework/wiring/BundleCapability
instanceKlass org/osgi/resource/Capability
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$3
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$1
instanceKlass org/eclipse/osgi/internal/container/NamespaceList
instanceKlass org/eclipse/osgi/container/ModuleRevision$1
instanceKlass org/osgi/framework/wiring/BundleWiring
instanceKlass org/osgi/resource/Wiring
instanceKlass org/eclipse/osgi/container/ModuleRevision
instanceKlass org/eclipse/osgi/container/ModuleRevisions
instanceKlass org/osgi/framework/wiring/BundleRevisions
instanceKlass org/lombokweb/asm/Opcodes
instanceKlass org/lombokweb/asm/Handler
instanceKlass lombok/patcher/MethodLogistics
instanceKlass org/lombokweb/asm/Label
instanceKlass org/lombokweb/asm/Type
instanceKlass org/lombokweb/asm/Frame
instanceKlass org/lombokweb/asm/Context
instanceKlass org/lombokweb/asm/Attribute
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1
instanceKlass org/lombokweb/asm/ByteVector
instanceKlass org/lombokweb/asm/Symbol
instanceKlass org/lombokweb/asm/SymbolTable
instanceKlass org/lombokweb/asm/FieldVisitor
instanceKlass org/lombokweb/asm/MethodVisitor
instanceKlass org/lombokweb/asm/AnnotationVisitor
instanceKlass org/lombokweb/asm/ModuleVisitor
instanceKlass org/lombokweb/asm/RecordComponentVisitor
instanceKlass org/lombokweb/asm/ClassReader
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$2
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo$1
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder
instanceKlass org/osgi/resource/Wire
instanceKlass org/eclipse/osgi/container/ModuleDatabase$Persistence
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerWiring
instanceKlass org/eclipse/osgi/container/ModuleResolver
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock
instanceKlass org/eclipse/osgi/internal/container/LockSet
instanceKlass org/osgi/framework/wiring/FrameworkWiring
instanceKlass org/osgi/framework/startlevel/FrameworkStartLevel
instanceKlass org/osgi/resource/Requirement
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport
instanceKlass org/eclipse/osgi/container/ModuleContainer
instanceKlass org/eclipse/osgi/internal/container/Capabilities
instanceKlass org/eclipse/osgi/container/Module
instanceKlass org/osgi/framework/startlevel/BundleStartLevel
instanceKlass org/eclipse/osgi/container/ModuleDatabase
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1
instanceKlass java/util/concurrent/LinkedTransferQueue$DualNode
instanceKlass java/util/concurrent/TransferQueue
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/eclipse/osgi/internal/container/AtomicLazyInitializer
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$BundleCollisionHook
instanceKlass org/osgi/framework/ServiceReference
instanceKlass org/osgi/framework/hooks/resolver/ResolverHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory
instanceKlass org/osgi/framework/hooks/resolver/ResolverHookFactory
instanceKlass org/eclipse/osgi/container/ModuleCollisionHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$1
instanceKlass org/eclipse/osgi/container/ModuleLoader
instanceKlass java/util/concurrent/Callable
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass java/util/concurrent/Executor
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityRow
instanceKlass org/eclipse/osgi/internal/permadmin/PermissionAdminTable
instanceKlass org/osgi/service/permissionadmin/PermissionInfo
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionInfo
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionUpdate
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityAdmin
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionAdmin
instanceKlass org/osgi/service/permissionadmin/PermissionAdmin
instanceKlass org/eclipse/osgi/storage/PermissionData
instanceKlass  @bci org/eclipse/osgi/storage/Storage connectPersistentBundles (Ljava/util/List;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002848109d340
instanceKlass  @cpi org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry 198 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481099c00
instanceKlass org/eclipse/osgi/storage/BundleInfo$Generation
instanceKlass org/eclipse/osgi/storage/BundleInfo
instanceKlass org/eclipse/osgi/framework/util/ObjectPool
instanceKlass jdk/internal/util/ByteArray
instanceKlass org/eclipse/osgi/storagemanager/StorageManager$Entry
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile$CacheInfo
instanceKlass java/util/ComparableTimSort
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/io/DeleteOnExitHook$1
instanceKlass java/io/DeleteOnExitHook
instanceKlass  @bci org/eclipse/osgi/framework/internal/reliablefile/ReliableFile createTempFile (Ljava/lang/String;Ljava/lang/String;Ljava/io/File;)Ljava/io/File; 61 <appendix> argL0 ; # org/eclipse/osgi/framework/internal/reliablefile/ReliableFile$$Lambda+0x00000284810934f8
instanceKlass java/util/function/IntUnaryOperator
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass org/eclipse/osgi/internal/location/Locker_JavaNio
instanceKlass org/eclipse/osgi/storagemanager/StorageManager
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 92 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x0000028481032670
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 76 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x0000028481032440
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 66 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x0000028481032200
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 47 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x0000028481031fd8
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 31 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x0000028481031da8
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 21 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x0000028481031b68
instanceKlass  @cpi java/lang/SecurityManager 539 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481099800
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 59 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x0000028481031948
instanceKlass  @cpi java/lang/SecurityManager 535 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481099400
instanceKlass  @cpi org/eclipse/core/internal/events/NotificationManager$NotifyJob 101 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481099000
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 49 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x0000028481031718
instanceKlass  @cpi org/eclipse/core/internal/runtime/InternalPlatform 1102 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481098c00
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 39 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000284810314e8
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 29 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x00000284810312a0
instanceKlass  @cpi java/lang/SecurityManager 518 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481098800
instanceKlass  @cpi java/util/Comparator 259 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481098400
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 17 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x0000028481031070
# instanceKlass java/net/SetAccessible+0x0000028481098000
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 462 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000028481097c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481097800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481097400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481097000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481096c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481096800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481096400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481096000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481095c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481095800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481095400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481095000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481094c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481094800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481094400
instanceKlass jdk/internal/vm/annotation/ForceInline
instanceKlass java/lang/annotation/Documented
instanceKlass java/lang/Deprecated
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 22 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 17 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 12 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003e
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 7 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass  @bci java/lang/Class methodToString (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/String; 42 <appendix> argL0 ; # java/lang/Class$$Lambda+0x0000028481030140
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481094000
instanceKlass sun/misc/Unsafe
instanceKlass org/eclipse/osgi/internal/url/MultiplexingFactory
instanceKlass org/eclipse/osgi/storage/FrameworkExtensionInstaller
instanceKlass org/eclipse/osgi/storage/bundlefile/MRUBundleFileList
instanceKlass org/eclipse/osgi/framework/eventmgr/EventDispatcher
instanceKlass org/osgi/framework/Filter
instanceKlass org/eclipse/osgi/storage/ContentProvider
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleFile
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002848108c400
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/eclipse/osgi/storage/Storage
instanceKlass  @bci org/eclipse/osgi/internal/cds/CDSHookConfigurator addHooks (Lorg/eclipse/osgi/internal/hookregistry/HookRegistry;)V 77 <appendix> argL0 ; # org/eclipse/osgi/internal/cds/CDSHookConfigurator$$Lambda+0x000002848108d678
instanceKlass org/eclipse/osgi/signedcontent/SignedContent
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory$StorageHook
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory
instanceKlass org/osgi/framework/BundleActivator
instanceKlass org/eclipse/osgi/internal/cds/CDSHookConfigurator
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook
instanceKlass org/eclipse/osgi/signedcontent/SignedContentFactory
instanceKlass org/eclipse/osgi/internal/hookregistry/ActivatorHookFactory
instanceKlass org/osgi/framework/wiring/BundleRevision
instanceKlass org/osgi/resource/Resource
instanceKlass org/eclipse/osgi/internal/connect/ConnectHookConfigurator
instanceKlass org/eclipse/osgi/internal/hookregistry/HookConfigurator
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$ConnectModules
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 20 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x000002848108bdc8
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 5 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x000002848108bba0
instanceKlass  @cpi org/eclipse/osgi/internal/log/ExtendedLogServiceImpl 376 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002848108c000
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory$1
instanceKlass org/eclipse/osgi/framework/log/FrameworkLog
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory
instanceKlass org/osgi/service/log/FormatterLogger
instanceKlass org/eclipse/osgi/internal/log/LoggerImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerAdmin
instanceKlass org/osgi/service/log/admin/LoggerContext
instanceKlass org/eclipse/osgi/internal/log/LoggerContextTargetMap
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager$MockSystemBundle
instanceKlass org/osgi/service/log/admin/LoggerAdmin
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory
instanceKlass org/eclipse/osgi/framework/util/ArrayMap
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory$1
instanceKlass org/osgi/service/log/LogEntry
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory
instanceKlass org/osgi/framework/ServiceFactory
instanceKlass org/eclipse/equinox/log/ExtendedLogReaderService
instanceKlass org/osgi/service/log/LogReaderService
instanceKlass org/eclipse/equinox/log/ExtendedLogService
instanceKlass org/eclipse/equinox/log/Logger
instanceKlass org/osgi/service/log/Logger
instanceKlass org/osgi/service/log/LogService
instanceKlass org/osgi/service/log/LoggerFactory
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager
instanceKlass org/osgi/framework/AllServiceListener
instanceKlass org/osgi/framework/ServiceListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogWriter
instanceKlass org/eclipse/equinox/log/LogFilter
instanceKlass org/eclipse/equinox/log/SynchronousLogListener
instanceKlass org/osgi/service/log/LogListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogServices
instanceKlass org/eclipse/osgi/util/ManifestElement
instanceKlass org/eclipse/osgi/internal/debug/Debug
instanceKlass org/eclipse/osgi/service/debug/DebugOptionsListener
instanceKlass org/eclipse/osgi/service/debug/DebugTrace
instanceKlass org/eclipse/osgi/internal/debug/FrameworkDebugOptions
instanceKlass org/osgi/util/tracker/ServiceTrackerCustomizer
instanceKlass java/nio/file/FileVisitor
instanceKlass org/eclipse/osgi/storage/StorageUtil
instanceKlass org/eclipse/osgi/internal/location/BasicLocation
instanceKlass org/eclipse/osgi/internal/location/EquinoxLocations
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/util/UUID
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/eclipse/osgi/internal/container/InternalUtils
instanceKlass org/osgi/framework/Version
instanceKlass org/eclipse/osgi/internal/location/Locker
instanceKlass org/eclipse/osgi/internal/location/LocationHelper
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration$ConfigValues
instanceKlass org/eclipse/osgi/internal/util/Tokenizer
instanceKlass java/nio/charset/CoderResult
instanceKlass sun/util/logging/PlatformLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerAccessor
instanceKlass jdk/internal/logger/LazyLoggers$LoggerAccessor
instanceKlass jdk/internal/logger/AbstractLoggerWrapper
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/lang/System$LoggerFinder
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerFactories
instanceKlass jdk/internal/logger/LazyLoggers$1
instanceKlass jdk/internal/logger/LazyLoggers
instanceKlass jdk/internal/event/EventHelper$ThreadTrackHolder
instanceKlass java/net/URLClassLoader$2
instanceKlass org/eclipse/osgi/internal/framework/AliasMapper
instanceKlass org/eclipse/osgi/framework/util/KeyedElement
instanceKlass org/eclipse/osgi/internal/hookregistry/ClassLoaderHook
instanceKlass org/eclipse/osgi/internal/hookregistry/HookRegistry
instanceKlass org/eclipse/osgi/service/datalocation/Location
instanceKlass org/eclipse/osgi/service/debug/DebugOptions
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration
instanceKlass org/eclipse/osgi/service/environment/EnvironmentInfo
# instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$$InjectedInvoker+0x0000028481081400
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$InjectedInvokerHolder
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481081000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481080c00
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x0000028481028228
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x0000028481027ff8
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x00000284810278d0
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x00000284810274b0
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x0000028481026c58
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass org/eclipse/osgi/framework/util/SecureAction$1
instanceKlass org/eclipse/osgi/framework/util/SecureAction
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer
instanceKlass org/eclipse/osgi/launch/Equinox
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481080800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481080400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481080000
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$InitialBundle
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$StartupEventListener
instanceKlass org/osgi/framework/FrameworkListener
instanceKlass org/osgi/framework/SynchronousBundleListener
instanceKlass java/util/concurrent/Semaphore
instanceKlass java/util/EventObject
instanceKlass org/osgi/framework/BundleContext
instanceKlass org/osgi/framework/BundleReference
instanceKlass org/osgi/framework/BundleListener
instanceKlass java/util/EventListener
instanceKlass org/osgi/framework/launch/Framework
instanceKlass org/osgi/framework/Bundle
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x0000028481020f20
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass java/net/URLClassLoader$1
instanceKlass java/nio/file/FileStore
instanceKlass sun/nio/fs/WindowsSecurity
instanceKlass sun/nio/fs/AbstractAclFileAttributeView
instanceKlass java/nio/file/attribute/AclFileAttributeView
instanceKlass java/nio/file/attribute/FileOwnerAttributeView
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass org/eclipse/equinox/launcher/JNIBridge
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences absolutePath ()Ljava/lang/String; 66 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002848101c000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848101bc00
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences absolutePath ()Ljava/lang/String; 66 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002848101b800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848101b400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848101b000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848101ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848101a800
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences absolutePath ()Ljava/lang/String; 66 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002848101a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848101a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481019c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481019800
instanceKlass java/io/RandomAccessFile$1
instanceKlass  @bci org/eclipse/core/runtime/Path addFileExtension (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 52 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481019400
instanceKlass java/lang/invoke/LambdaFormEditor$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481019000
instanceKlass  @bci org/eclipse/core/runtime/Path addFileExtension (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 52 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481018c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481018800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481018400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481018000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481017c00
instanceKlass  @bci org/eclipse/core/runtime/Path addFileExtension (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 52 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481017800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481017400
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481017000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481016c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481016800
instanceKlass java/lang/Long$LongCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481016400
instanceKlass  @bci org/eclipse/core/internal/resources/CheckMissingNaturesListener <clinit> ()V 11 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481016000
instanceKlass sun/net/www/MimeEntry
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder$1
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder
instanceKlass sun/net/www/MimeTable$2
instanceKlass sun/net/www/MimeTable$1
instanceKlass sun/net/www/MimeTable
instanceKlass java/net/URLConnection$1
instanceKlass java/net/FileNameMap
instanceKlass java/util/Collections$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass java/net/URL$ThreadTrackHolder
instanceKlass java/lang/Thread$ThreadNumbering
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass java/security/Policy
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass org/eclipse/equinox/launcher/Main
instanceKlass sun/security/util/ManifestEntryVerifier$SunProviderHolder
instanceKlass java/util/Base64$Encoder
instanceKlass java/util/Base64$Decoder
instanceKlass java/util/Base64
instanceKlass javax/crypto/SecretKey
instanceKlass sun/security/util/Length
instanceKlass sun/security/util/KeyUtil
instanceKlass java/security/interfaces/XECKey
instanceKlass java/security/interfaces/ECKey
instanceKlass sun/security/util/JarConstraintsParameters
instanceKlass sun/security/util/ConstraintsParameters
instanceKlass java/security/CodeSigner
instanceKlass java/security/Timestamp
instanceKlass sun/security/timestamp/TimestampToken
instanceKlass java/security/cert/CertPath
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481014c00
instanceKlass java/math/MutableBigInteger
instanceKlass sun/security/rsa/RSAPadding
instanceKlass sun/security/rsa/RSACore
instanceKlass java/security/interfaces/RSAPrivateCrtKey
instanceKlass sun/security/pkcs/PKCS8Key
instanceKlass sun/security/util/InternalPrivateKey
instanceKlass java/security/interfaces/RSAPrivateKey
instanceKlass java/security/PrivateKey
instanceKlass javax/security/auth/Destroyable
instanceKlass jdk/internal/icu/util/CodePointTrie$Data
instanceKlass jdk/internal/icu/util/CodePointMap
instanceKlass jdk/internal/icu/util/VersionInfo
instanceKlass  @bci jdk/internal/module/SystemModuleFinders$SystemModuleReader open (Ljava/lang/String;)Ljava/util/Optional; 6 <appendix> member <vmtarget> ; # jdk/internal/module/SystemModuleFinders$SystemModuleReader$$Lambda+0x0000028481075230
instanceKlass jdk/internal/jimage/decompressor/ZipDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorFactory
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorRepository
instanceKlass jdk/internal/jimage/decompressor/CompressedResourceHeader
instanceKlass  @bci jdk/internal/jimage/BasicImageReader getResourceBuffer (Ljdk/internal/jimage/ImageLocation;)Ljava/nio/ByteBuffer; 168 <appendix> member <vmtarget> ; # jdk/internal/jimage/BasicImageReader$$Lambda+0x00000284810741f0
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor$StringsProvider
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass jdk/internal/jimage/ImageBufferCache$2
instanceKlass jdk/internal/jimage/ImageBufferCache
instanceKlass jdk/internal/module/Checks
instanceKlass jdk/internal/icu/impl/ICUBinary$1
instanceKlass jdk/internal/icu/impl/ICUBinary
instanceKlass jdk/internal/icu/impl/NormalizerImpl$IsAcceptable
instanceKlass jdk/internal/icu/impl/ICUBinary$Authenticate
instanceKlass jdk/internal/icu/impl/NormalizerImpl
instanceKlass jdk/internal/icu/impl/Norm2AllModes$Norm2AllModesSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes$NFKCSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes
instanceKlass jdk/internal/icu/text/Normalizer2
instanceKlass jdk/internal/icu/text/NormalizerBase$ModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$NFKDModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$1
instanceKlass jdk/internal/icu/text/NormalizerBase$Mode
instanceKlass jdk/internal/icu/text/NormalizerBase
instanceKlass java/text/Normalizer
instanceKlass sun/security/x509/AVAKeyword
instanceKlass java/util/StringJoiner
instanceKlass sun/security/jca/ServiceId
instanceKlass java/security/Signature$1
instanceKlass jdk/internal/access/JavaSecuritySignatureAccess
instanceKlass java/security/SignatureSpi
instanceKlass sun/security/util/SignatureUtil
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/pkcs/SigningCertificateInfo$ESSCertId
instanceKlass sun/security/pkcs/SigningCertificateInfo
instanceKlass sun/security/pkcs/PKCS9Attribute
instanceKlass sun/security/pkcs/PKCS9Attributes
instanceKlass java/time/Instant
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass  @bci java/time/ZoneOffset ofTotalSeconds (I)Ljava/time/ZoneOffset; 37 <appendix> argL0 ; # java/time/ZoneOffset$$Lambda+0x80000000c
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/ZoneId
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000025
instanceKlass  @bci java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass java/util/regex/CharPredicates
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints$Holder
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint
instanceKlass java/util/StringTokenizer
instanceKlass java/security/spec/ECFieldF2m
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass sun/security/util/AbstractAlgorithmConstraints$1
instanceKlass sun/security/util/AlgorithmDecomposer
instanceKlass sun/security/util/DisabledAlgorithmConstraints$JarHolder
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x0000028481066ef8
instanceKlass java/util/regex/ASCII
instanceKlass sun/security/util/AbstractAlgorithmConstraints
instanceKlass java/security/AlgorithmConstraints
instanceKlass sun/security/pkcs/SignerInfo
instanceKlass java/security/cert/PolicyQualifierInfo
instanceKlass sun/security/x509/CertificatePolicyId
instanceKlass sun/security/x509/PolicyInformation
instanceKlass sun/security/x509/DistributionPoint
instanceKlass sun/security/x509/DNSName
instanceKlass sun/security/x509/URIName
instanceKlass sun/security/x509/GeneralName
instanceKlass sun/security/x509/AccessDescription
instanceKlass sun/security/x509/GeneralNames
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/lang/System$Logger
instanceKlass jdk/internal/event/EventHelper
instanceKlass sun/security/jca/JCAUtil
instanceKlass sun/security/util/MemoryCache$CacheEntry
instanceKlass sun/security/x509/KeyIdentifier
instanceKlass java/util/TreeMap$Entry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481014800
instanceKlass sun/security/x509/OIDMap$OIDInfo
instanceKlass sun/security/x509/PKIXExtensions
instanceKlass sun/security/x509/OIDMap
instanceKlass sun/security/x509/Extension
instanceKlass java/security/cert/Extension
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass sun/security/x509/CertificateExtensions
instanceKlass sun/security/rsa/RSAUtil
instanceKlass java/security/interfaces/RSAPublicKey
instanceKlass java/security/interfaces/RSAKey
instanceKlass java/security/spec/PSSParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/spec/RSAPrivateKeySpec
instanceKlass java/security/spec/RSAPublicKeySpec
instanceKlass java/security/KeyFactorySpi
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass sun/security/jca/ProviderList$ServiceList$1
instanceKlass java/security/KeyFactory
instanceKlass  @bci java/security/spec/EncodedKeySpec <clinit> ()V 0 <appendix> argL0 ; # java/security/spec/EncodedKeySpec$$Lambda+0x000002848105d8e8
instanceKlass  @cpi org/eclipse/jdt/internal/core/search/processing/JobManager 554 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481014400
instanceKlass jdk/internal/access/JavaSecuritySpecAccess
instanceKlass java/security/spec/EncodedKeySpec
instanceKlass java/security/spec/KeySpec
instanceKlass sun/security/util/BitArray
instanceKlass sun/security/x509/X509Key
instanceKlass java/security/PublicKey
instanceKlass java/security/Key
instanceKlass sun/security/x509/CertificateX509Key
instanceKlass java/util/Date
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass sun/security/x509/CertificateValidity
instanceKlass sun/security/x509/AVA
instanceKlass sun/security/x509/RDN
instanceKlass javax/security/auth/x500/X500Principal
instanceKlass  @bci sun/security/x509/X500Name <clinit> ()V 153 <appendix> argL0 ; # sun/security/x509/X500Name$$Lambda+0x000002848105b4f0
instanceKlass sun/security/x509/X500Name
instanceKlass sun/security/x509/GeneralNameInterface
instanceKlass sun/security/x509/CertificateAlgorithmId
instanceKlass sun/security/x509/SerialNumber
instanceKlass sun/security/x509/CertificateSerialNumber
instanceKlass sun/security/x509/CertificateVersion
instanceKlass sun/security/x509/X509CertInfo
instanceKlass sun/security/util/Cache$EqualByteArray
instanceKlass java/security/cert/X509Extension
instanceKlass java/lang/Byte$ByteCache
instanceKlass  @bci sun/security/util/DerInputStream seeOptionalContextSpecific (I)Z 2 <appendix> member <vmtarget> ; # sun/security/util/DerInputStream$$Lambda+0x0000028481058f20
instanceKlass  @cpi sun/security/util/DerInputStream 295 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481014000
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/util/Cache
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/cert/CertificateFactorySpi
instanceKlass java/security/cert/CertificateFactory
instanceKlass sun/security/x509/AlgorithmId
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerValue
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/pkcs/ContentInfo
instanceKlass sun/security/util/DerEncoder
instanceKlass sun/security/util/DerInputStream
instanceKlass sun/security/pkcs/PKCS7
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass javax/security/auth/login/Configuration$Parameters
instanceKlass java/security/Policy$Parameters
instanceKlass java/security/cert/CertStoreParameters
instanceKlass java/security/SecureRandomParameters
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass  @bci sun/security/util/ManifestDigester <init> ([B)V 350 <appendix> argL0 ; # sun/security/util/ManifestDigester$$Lambda+0x000002848104ce80
instanceKlass sun/security/util/ManifestDigester$Section
instanceKlass sun/security/util/ManifestDigester$Entry
instanceKlass sun/security/util/ManifestDigester$Position
instanceKlass sun/security/util/ManifestDigester
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/util/jar/JarFile$ThreadTrackHolder
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass lombok/patcher/scripts/ScriptBuilder$SetSymbolDuringMethodCallBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder$ReplaceMethodCallBuilder
instanceKlass lombok/eclipse/agent/EclipsePatcher$4
instanceKlass lombok/eclipse/agent/EclipsePatcher$3
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapMethodCallBuilder
instanceKlass lombok/patcher/ScriptManager$WitnessAction
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapReturnValueBuilder
instanceKlass lombok/patcher/ClassRootFinder
instanceKlass lombok/patcher/scripts/ScriptBuilder$AddFieldBuilder
instanceKlass java/util/Collections$1
instanceKlass lombok/patcher/PatchScript$MethodPatcherFactory
instanceKlass org/lombokweb/asm/ClassVisitor
instanceKlass lombok/patcher/Hook
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000031
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass lombok/patcher/MethodTarget
instanceKlass lombok/patcher/scripts/ScriptBuilder$ExitEarlyBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder
instanceKlass lombok/eclipse/agent/EclipseLoaderPatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher$2
instanceKlass lombok/eclipse/agent/EclipsePatcher$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848100d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848100d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848100d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002848100cc00
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000002848100c800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002848100c400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002848100c000
instanceKlass java/lang/instrument/ClassDefinition
instanceKlass lombok/patcher/ScriptManager$OurClassFileTransformer
instanceKlass lombok/patcher/Filter$1
instanceKlass lombok/patcher/TransplantMapper$1
instanceKlass java/lang/instrument/ClassFileTransformer
instanceKlass lombok/patcher/ScriptManager
instanceKlass lombok/patcher/TransplantMapper
instanceKlass lombok/patcher/Filter
instanceKlass lombok/patcher/PatchScript
instanceKlass lombok/patcher/TargetMatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher
instanceKlass lombok/core/AgentLauncher$AgentLaunchable
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481008400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481008000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481007c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481007800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481007400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481007000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481006c00
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481006800
instanceKlass  @cpi sun/management/spi/PlatformMBeanProvider$PlatformComponent 113 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481006400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481006000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481005c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481005800
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x0000028481005400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481005000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481004c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481004800
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481004400
instanceKlass sun/invoke/util/ValueConversions$1
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000028481004000
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
instanceKlass lombok/core/AgentLauncher$AgentInfo
instanceKlass lombok/core/AgentLauncher
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass sun/net/www/MessageHeader
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLConnection
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass java/util/WeakHashMap$HashIterator
instanceKlass java/net/URLDecoder
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000029
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/net/URI$Parser
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/util/Arrays$ArrayItr
instanceKlass lombok/launch/PackageShader
instanceKlass java/io/Reader
instanceKlass java/lang/Readable
instanceKlass lombok/launch/Main
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 136 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000028481002c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481002800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481002400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481002000
instanceKlass  @bci org/eclipse/core/internal/resources/SaveManager getSaveNumber (Ljava/lang/String;)I 5 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481001c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481001800
instanceKlass sun/instrument/InstrumentationImpl$1
instanceKlass lombok/launch/Agent
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/lang/StringCoding
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/instrument/TransformerManager$TransformerInfo
instanceKlass sun/instrument/TransformerManager
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass  @bci sun/instrument/InstrumentationImpl <clinit> ()V 16 <appendix> argL0 ; # sun/instrument/InstrumentationImpl$$Lambda+0x0000028481042ca0
instanceKlass sun/instrument/InstrumentationImpl
instanceKlass java/lang/instrument/Instrumentation
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass jdk/internal/module/ModuleBootstrap$SafeModuleFinder
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x0000028481042558
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x0000028481041c40
instanceKlass  @bci jdk/internal/module/ModuleBootstrap decode (Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/Map; 193 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000028481041a10
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/util/SequencedMap
instanceKlass java/util/SequencedSet
instanceKlass java/lang/ModuleLayer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/module/ModuleFinder$1
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/module/Resolver
instanceKlass java/lang/module/Configuration
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 791 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000028481040e48
instanceKlass  @cpi org/eclipse/core/internal/preferences/EclipsePreferences 1062 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000028481000c00
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 779 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000028481040c00
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 767 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x00000284810409d0
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 757 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x00000284810407a0
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000048
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004b
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass  @bci jdk/internal/module/DefaultRoots exportsAPI (Ljava/lang/module/ModuleDescriptor;)Z 9 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000051
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000042
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000040
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000039
instanceKlass java/util/function/BiConsumer
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000045
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 42 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x80000004e
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 32 <appendix> member <vmtarget> ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000052
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 21 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x80000004f
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass  @bci org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding <clinit> ()V 46 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000028481000800
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 11 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000050
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/function/Predicate
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/invoke/LambdaMetafactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000028481000400
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/Void
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass jdk/internal/util/StrongReferenceKey
instanceKlass jdk/internal/util/ReferenceKey
instanceKlass jdk/internal/util/ReferencedKeyMap
instanceKlass java/lang/invoke/MethodType$1
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterator
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/module/DefaultRoots
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass sun/nio/cs/SingleByte
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 834 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 100 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 7 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass org/eclipse/osgi/internal/permadmin/EquinoxSecurityManager
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$ClassContext
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$Finder
instanceKlass org/eclipse/osgi/internal/url/MultiplexingFactory$InternalSecurityManager
ciInstanceKlass java/lang/SecurityManager 1 1 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 11 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 10 12 1 10 12 1 18 12 1 18 10 7 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 7 12 1 1 10 7 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 7 12 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/SecurityManager packageAccessLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager packageDefinitionLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager nonExportedPkgs Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$GenerationProtectionDomain
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 7 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 7 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 7 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 7 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 100 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 214 0 14021 0 -1
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
instanceKlass lombok/eclipse/agent/PatchDelegate$CantMakeDelegates
instanceKlass org/eclipse/jface/text/BadLocationException
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$InvalidBindingException
instanceKlass org/eclipse/jdt/core/compiler/InvalidInputException
instanceKlass org/eclipse/jdt/internal/compiler/classfmt/ClassFormatException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass org/osgi/service/application/ApplicationException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass org/eclipse/core/runtime/CoreException
instanceKlass org/osgi/service/prefs/BackingStoreException
instanceKlass org/apache/felix/scr/impl/inject/methods/SuitableMethodNotAccessibleException
instanceKlass org/xml/sax/SAXException
instanceKlass javax/xml/parsers/ParserConfigurationException
instanceKlass org/osgi/service/resolver/ResolutionException
instanceKlass java/security/GeneralSecurityException
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLockException
instanceKlass java/security/PrivilegedActionException
instanceKlass org/osgi/framework/InvalidSyntaxException
instanceKlass java/lang/InterruptedException
instanceKlass org/osgi/framework/BundleException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/instrument/UnmodifiableClassException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass lombok/eclipse/agent/PatchDelegate$DelegateRecursion
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass org/eclipse/core/internal/resources/SaveManager$MasterTable
instanceKlass org/eclipse/osgi/util/NLS$MessagesProperties
instanceKlass org/eclipse/core/internal/preferences/SortedProperties
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 100 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass org/apache/felix/scr/impl/helper/ReadOnlyDictionary
instanceKlass org/osgi/framework/FrameworkUtil$MapAsDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$UnmodifiableDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$SystemBundle$SystemBundleHeaders
instanceKlass org/eclipse/osgi/storage/BundleInfo$CachedManifest
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1443 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 100 1 100 1 10 12 100 1 10 10 100 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 1024 0 8704 0 312
ciMethod java/lang/String hashCode ()I 1024 0 14664 0 184
ciMethod java/lang/String length ()I 1024 0 141783 0 104
ciMethod java/lang/String charAt (I)C 1024 0 363286 0 192
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/InternalError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ThreadDeath
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager$LazyFileInputStream
instanceKlass java/io/ObjectInputStream
instanceKlass org/eclipse/core/internal/localstore/SafeChunkyInputStream
instanceKlass org/eclipse/core/internal/registry/BufferedRandomInputStream
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$RewindableInputStream
instanceKlass org/eclipse/osgi/storage/url/reference/ReferenceInputStream
instanceKlass java/util/jar/JarVerifier$VerifierStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 3 7 1 8 1 10 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$1
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 100 12 1 1 1 10 100 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 581 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/core/internal/jobs/InternalWorker
instanceKlass org/eclipse/core/internal/jobs/Worker
instanceKlass java/util/TimerThread
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread
instanceKlass org/eclipse/equinox/launcher/Main$SplashHandler
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 7 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 7 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 10 7 12 1 1 1 10 12 1 8 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 256 0 128 0 -1
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/StringLatin1 1 1 392 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 7 1 8 1 10 12 1 8 1 10 12 1 1 100 1 10 10 12 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 460 7 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 5 0 5 0 7 1 3 5 0 3 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 100 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 10 12 1 4 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 9 12 1 1 10 12 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/util/ArraysSupport 1 1 378 7 1 7 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 9 12 1 10 12 1 1 10 12 7 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 100 1 10 12 1 100 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 9 12 1 1 11 100 12 1 1 1 9 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 3 10 12 1 7 1 8 1 8 1 8 1 10 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield jdk/internal/util/ArraysSupport U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/util/ArraysSupport BIG_ENDIAN Z 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BOOLEAN_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BYTE_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_CHAR_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_SHORT_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_INT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_LONG_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_FLOAT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_DOUBLE_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_BYTE_BIT_SIZE I 3
staticfield jdk/internal/util/ArraysSupport JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringLatin1 equals ([B[B)Z 1024 764 7465 0 -1
ciMethod java/lang/StringLatin1 hashCode ([B)I 1024 0 5754 0 328
ciMethod java/lang/StringLatin1 indexOf ([BIII)I 522 0 6770 0 408
ciMethod java/lang/StringLatin1 indexOfChar ([BIII)I 532 6296 6734 0 0
ciMethod java/lang/StringLatin1 charAt ([BI)C 1024 0 363286 0 152
ciMethod java/lang/StringLatin1 canEncode (I)Z 1024 0 30536 0 104
ciInstanceKlass java/lang/StringUTF16 1 1 604 7 1 7 1 10 7 12 1 1 1 100 1 10 7 1 3 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 10 12 1 3 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 10 12 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 8 1 8 1 10 12 1 1 100 1 10 10 7 12 1 1 1 10 100 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 5 0 5 0 10 12 1 10 12 10 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringUTF16 checkIndex (I[B)V 0 0 9 0 -1
ciMethod java/lang/StringUTF16 hashCode ([B)I 0 0 1 0 -1
ciMethod java/lang/StringUTF16 compress ([CII)[B 1024 0 7157 0 400
ciMethod java/lang/StringUTF16 compress ([CI[BII)I 254 6526 2260 0 -1
ciMethod java/lang/StringUTF16 toBytes ([CII)[B 0 0 1 0 -1
ciMethod java/lang/StringUTF16 indexOf ([BIII)I 0 0 1 0 -1
ciMethod java/lang/StringUTF16 getChar ([BI)C 768 0 11527 0 -1
ciMethod java/lang/StringUTF16 charAt ([BI)C 0 0 9 0 0
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 100 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 100 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 907 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 18 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 10 12 1 10 12 1 10 7 12 1 1 8 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciMethod java/lang/Float intBitsToFloat (I)F 0 0 1 0 -1
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 7 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciMethod java/lang/Double longBitsToDouble (J)D 1580 0 790 0 -1
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciMethod java/lang/Byte valueOf (B)Ljava/lang/Byte; 130 0 65 0 -1
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciMethod java/lang/Short valueOf (S)Ljava/lang/Short; 0 0 1 0 -1
ciInstanceKlass java/lang/Integer$IntegerCache 1 1 95 10 7 12 1 1 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 3 10 12 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 9 12 1 100 1 10 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 1 1 1 1
staticfield java/lang/Integer$IntegerCache high I 127
staticfield java/lang/Integer$IntegerCache cache [Ljava/lang/Integer; 256 [Ljava/lang/Integer;
staticfield java/lang/Integer$IntegerCache $assertionsDisabled Z 1
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$SoftRef
instanceKlass sun/security/util/MemoryCache$SoftCacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore$LockWeakRef
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass jdk/internal/util/WeakReferenceKey
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass lombok/eclipse/agent/PatchDelegate$DelegateReceiver
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$METHOD_TYPE
instanceKlass java/math/RoundingMode
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass java/time/zone/ZoneOffsetTransitionRule$TimeDefinition
instanceKlass java/time/DayOfWeek
instanceKlass java/time/Month
instanceKlass java/time/format/TextStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$SettingsParser
instanceKlass java/util/Locale$Category
instanceKlass java/time/format/ResolverStyle
instanceKlass java/time/format/SignStyle
instanceKlass java/time/temporal/JulianFields$Field
instanceKlass java/time/temporal/IsoFields$Unit
instanceKlass java/time/temporal/IsoFields$Field
instanceKlass java/util/Comparators$NaturalOrderComparator
instanceKlass java/lang/StackWalker$Option
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils$ValueType
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegState
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager$State
instanceKlass org/osgi/util/promise/PromiseFactory$Option
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata$ReferenceScope
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata$Scope
instanceKlass org/apache/felix/scr/impl/metadata/DSVersion
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner$NameType
instanceKlass com/sun/org/apache/xerces/internal/util/Status
instanceKlass javax/xml/catalog/CatalogFeatures$Feature
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$NameMap
instanceKlass jdk/xml/internal/JdkProperty$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$Limit
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger$Level
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ContainerEvent
instanceKlass org/eclipse/osgi/container/Module$StartOptions
instanceKlass org/eclipse/osgi/container/ModuleDatabase$Sort
instanceKlass org/eclipse/osgi/container/Module$Settings
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent
instanceKlass org/eclipse/osgi/storage/ContentProvider$Type
instanceKlass org/eclipse/osgi/container/Module$State
instanceKlass org/osgi/service/log/LogLevel
instanceKlass sun/util/logging/PlatformLogger$Level
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass java/nio/file/AccessMode
instanceKlass java/nio/file/attribute/PosixFilePermission
instanceKlass jdk/internal/icu/util/CodePointTrie$ValueWidth
instanceKlass jdk/internal/icu/util/CodePointTrie$Type
instanceKlass java/text/Normalizer$Form
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/time/temporal/ChronoField
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint$Operator
instanceKlass java/lang/System$Logger$Level
instanceKlass sun/security/rsa/RSAUtil$KeyType
instanceKlass sun/security/util/KnownOIDs
instanceKlass lombok/patcher/StackRequest
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccess
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/util/stream/StreamShape
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass org/eclipse/equinox/launcher/Main$StartupClassLoader
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 117 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 3 10 100 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/ByteArrayInputStream $assertionsDisabled Z 1
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/util/Preconditions 1 1 194 10 7 12 1 1 1 11 7 12 1 1 1 11 100 12 1 1 1 7 1 100 1 10 7 12 1 1 1 10 12 1 8 1 7 1 10 7 12 1 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 7 1 10 12 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 11 12 1 8 1 8 1 11 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 7 1 10 10 12 1 1 9 12 1 1 7 1 10 9 12 1 7 1 10 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/util/Preconditions SIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions AIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$CopyOnFirstWriteList
instanceKlass org/eclipse/osgi/internal/weaving/DynamicImportList
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/Vector
instanceKlass sun/security/jca/ProviderList$ServiceList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 7 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 7 12 1 1 1 7 1 11 7 12 1 1 1 10 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciMethod java/util/List add (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/util/List iterator ()Ljava/util/Iterator; 0 0 1 0 -1
instanceKlass java/util/TreeMap$Values
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder
instanceKlass java/util/AbstractQueue
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/AbstractCollection <init> ()V 1024 0 19859 0 0
ciMethod java/util/AbstractList <init> ()V 284 0 12437 0 0
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/jdt/internal/codeassist/select/SelectionNodeFound
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$1FoundRelevantDeltaException
instanceKlass org/eclipse/jdt/internal/codeassist/complete/InvalidCursorLocation
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNodeFound
instanceKlass org/eclipse/jdt/internal/compiler/problem/AbortCompilation
instanceKlass org/eclipse/jdt/internal/compiler/lookup/SourceTypeCollisionException
instanceKlass org/eclipse/jdt/internal/core/builder/MissingSourceFileException
instanceKlass org/eclipse/jdt/internal/core/builder/ImageBuilderInternalException
instanceKlass java/lang/NegativeArraySizeException
instanceKlass org/eclipse/jdt/internal/core/ClasspathEntry$AssertionFailedException
instanceKlass org/eclipse/core/internal/events/BuildManager$JobManagerSuspendedException
instanceKlass org/eclipse/core/internal/localstore/IsSynchronizedVisitor$ResourceChangedException
instanceKlass java/lang/IllegalCallerException
instanceKlass org/eclipse/core/internal/dtree/ObjectNotFoundException
instanceKlass org/eclipse/core/internal/utils/WrappedRuntimeException
instanceKlass org/eclipse/core/runtime/OperationCanceledException
instanceKlass org/osgi/util/promise/FailedPromisesException
instanceKlass org/eclipse/core/runtime/InvalidRegistryObjectException
instanceKlass java/util/concurrent/RejectedExecutionException
instanceKlass org/eclipse/core/runtime/AssertionFailedException
instanceKlass java/util/MissingResourceException
instanceKlass org/osgi/service/component/ComponentException
instanceKlass org/osgi/framework/hooks/weaving/WeavingException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/osgi/framework/ServiceException
instanceKlass java/lang/TypeNotPresentException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/eclipse/osgi/framework/util/ThreadInfoReport
instanceKlass org/eclipse/osgi/storage/Storage$StorageException
instanceKlass java/util/NoSuchElementException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/invoke/WrongMethodTypeException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass sun/security/pkcs/SignerInfo$AlgorithmInfo
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/module/ModuleReferenceImpl$CachedHash
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 7 1 10 10 12 1 1 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljdk/internal/util/ReferencedKeySet; jdk/internal/util/ReferencedKeySet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 733 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 4
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$ServiceReferenceMap
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableValueCollectionMap
instanceKlass java/util/Collections$SingletonMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 7 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
instanceKlass java/lang/NumberFormatException
ciInstanceKlass java/lang/IllegalArgumentException 1 1 35 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/IllegalArgumentException <init> (Ljava/lang/String;)V 0 0 14 0 -1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 7 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass java/lang/Void 1 1 31 10 7 12 1 1 1 8 1 10 7 12 1 1 1 9 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Void TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; null
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 7 1 7 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 7 1 100 1 7 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/Collections 1 1 932 10 7 12 1 1 1 11 7 12 1 1 1 7 1 11 12 1 1 7 1 10 12 1 1 10 12 1 11 12 1 1 7 1 11 12 1 1 11 12 1 1 10 12 1 11 100 12 1 1 11 12 1 1 11 12 1 10 12 1 10 12 1 10 12 11 100 12 1 1 1 10 12 1 1 11 12 1 11 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 11 100 12 1 1 1 11 12 1 1 10 12 1 11 12 1 100 1 8 1 10 12 1 11 7 12 1 1 1 11 7 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 1 7 1 10 12 1 11 7 1 100 1 10 12 1 11 7 1 7 1 10 12 1 11 100 1 100 1 10 12 1 11 7 1 7 1 10 12 1 11 100 1 100 1 10 12 1 11 7 1 11 7 1 10 12 10 11 7 1 7 1 10 12 1 11 100 1 100 1 10 11 100 1 100 1 10 12 1 11 100 1 100 1 10 12 1 7 1 10 10 12 1 7 1 10 10 12 1 100 1 10 100 1 10 100 1 10 100 1 10 10 12 1 10 7 1 10 100 1 10 100 1 10 100 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 9 7 12 1 1 1 9 100 12 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 7 1 10 7 1 10 7 1 10 7 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 100 1 10 12 1 9 7 12 1 1 1 9 7 12 1 1 1 7 1 9 12 1 1 10 12 7 1 10 7 1 10 11 100 12 1 1 11 12 1 10 12 1 11 11 12 1 11 11 12 1 8 1 7 1 10 11 100 1 10 12 1 100 1 10 100 12 1 1 1 100 1 10 12 1 7 1 10 7 1 10 7 1 10 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/Collections EMPTY_SET Ljava/util/Set; java/util/Collections$EmptySet
staticfield java/util/Collections EMPTY_LIST Ljava/util/List; java/util/Collections$EmptyList
staticfield java/util/Collections EMPTY_MAP Ljava/util/Map; java/util/Collections$EmptyMap
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 1 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 1 1 271 9 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/StackWalker DEFAULT_EMPTY_OPTION Ljava/util/EnumSet; java/util/RegularEnumSet
staticfield java/lang/StackWalker DEFAULT_WALKER Ljava/lang/StackWalker; java/lang/StackWalker
ciInstanceKlass java/lang/StackWalker$StackFrame 0 0 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 375 100 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 7 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 7 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
ciMethod java/util/ArrayList add (Ljava/lang/Object;)Z 518 0 8586 0 1176
ciMethod java/util/ArrayList iterator ()Ljava/util/Iterator; 512 0 5756 0 200
ciMethod java/util/ArrayList remove (I)Ljava/lang/Object; 512 0 932 0 -1
ciMethod java/util/ArrayList <init> ()V 182 0 5403 0 112
ciMethod java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 518 0 8586 0 -1
ciInstanceKlass java/util/regex/Pattern 1 1 1581 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 11 12 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 1 7 1 8 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 10 7 1 3 10 12 1 10 12 1 8 1 10 12 1 10 100 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 7 1 9 12 1 1 10 12 1 9 12 1 9 12 1 10 7 1 100 1 8 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 100 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 10 12 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 10 11 100 1 10 12 1 1 8 1 18 12 1 1 11 12 1 1 10 10 12 1 1 8 1 9 12 1 10 12 1 8 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 7 1 8 1 10 10 10 7 12 1 1 1 10 100 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 9 12 1 9 12 1 10 12 1 10 12 1 9 12 1 7 1 9 12 1 1 9 12 1 1 10 9 12 1 1 10 12 1 1 9 7 12 1 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 8 1 7 1 10 7 12 1 1 7 1 10 7 1 7 1 9 12 1 11 12 1 1 11 7 12 1 1 11 12 1 7 1 9 12 1 7 1 10 10 12 1 1 11 7 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 3 10 12 1 1 10 12 1 7 1 10 10 7 12 1 10 12 1 10 12 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 100 1 10 10 100 1 10 12 1 7 1 10 7 1 10 12 1 1 10 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 100 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 1 100 1 9 12 1 10 10 7 12 1 1 10 12 1 1 9 12 1 1 11 7 12 1 1 100 1 10 10 12 1 11 7 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 10 12 1 8 1 10 12 1 11 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 8 1 7 1 10 11 12 1 1 8 1 8 1 11 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 7 1 10 12 1 8 1 10 12 1 8 1 11 10 12 1 1 100 1 10 100 1 10 7 1 9 7 12 1 1 1 10 12 1 11 12 1 8 1 8 1 10 12 1 11 12 1 1 9 7 12 1 1 1 7 1 10 10 12 1 1 9 12 1 8 1 10 12 1 1 100 1 9 12 1 9 12 1 10 12 1 100 1 10 100 1 10 7 1 10 11 11 12 1 8 1 10 12 1 8 1 8 1 10 12 1 9 12 1 9 12 1 9 12 1 7 1 9 7 1 7 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 9 10 12 3 11 100 1 10 7 1 10 12 1 9 9 9 12 1 8 1 10 10 9 12 1 1 10 12 1 9 12 1 10 12 1 1 7 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 3 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 11 100 1 10 12 1 100 1 10 100 1 10 7 1 10 100 1 10 10 9 12 1 9 12 1 10 12 1 18 12 1 1 18 12 1 18 18 18 12 1 18 12 1 18 12 18 12 18 18 12 18 18 18 12 18 12 18 12 18 3 3 18 18 12 18 18 18 12 1 1 18 100 1 10 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 7 12 1 1 10 9 12 7 1 10 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 16 1 15 10 12 16 16 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 16 15 10 12 16 15 10 12 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/util/regex/Pattern accept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$Node
staticfield java/util/regex/Pattern lastAccept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$LastNode
staticfield java/util/regex/Pattern $assertionsDisabled Z 1
ciMethod java/util/regex/Pattern compile ()V 32 998 14 0 -1
ciMethod java/util/regex/Pattern matcher (Ljava/lang/CharSequence;)Ljava/util/regex/Matcher; 482 0 204 0 -1
ciInstanceKlass java/util/regex/Matcher 1 1 489 10 7 12 1 1 1 7 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 12 1 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 11 12 1 10 12 1 100 1 8 1 10 12 1 9 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 8 1 10 10 7 12 1 1 1 7 1 10 10 10 12 1 1 10 12 1 1 10 10 7 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 8 1 11 7 12 1 1 8 1 10 100 12 1 1 10 12 1 10 12 1 8 1 8 1 10 12 1 1 8 1 10 12 1 8 1 11 7 12 1 1 1 7 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 11 12 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 11 100 12 1 1 100 1 10 100 1 10 12 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 8 8 8 1 8 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 8 1 10 12 9 12 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 11 8 1 10 12 1 8 1 8 1 8 1 100 1 8 1 10 10 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/regex/Matcher <init> (Ljava/util/regex/Pattern;Ljava/lang/CharSequence;)V 484 0 204 0 -1
ciMethod java/util/regex/Matcher group (I)Ljava/lang/String; 516 0 241 0 -1
ciMethod java/util/regex/Matcher reset ()Ljava/util/regex/Matcher; 258 5826 205 0 -1
ciMethod java/util/regex/Matcher matches ()Z 292 0 123 0 -1
ciMethod java/util/regex/Matcher find ()Z 1024 0 1256 0 -1
ciMethod java/util/regex/Matcher getTextLength ()I 258 0 205 0 -1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/function/BiFunction 1 1 65 10 100 12 1 1 1 18 12 1 1 11 7 12 1 1 11 100 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 100 1 100 1 1
instanceKlass java/util/ArrayList$ListItr
ciInstanceKlass java/util/ArrayList$Itr 1 1 104 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 7 12 1 1 9 12 1 9 12 1 9 12 1 10 12 1 100 1 10 9 12 1 1 100 1 10 100 1 10 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/ArrayList$Itr remove ()V 8 0 4 0 0
ciMethod java/util/ArrayList$Itr hasNext ()Z 514 0 6218 0 120
ciMethod java/util/ArrayList$Itr next ()Ljava/lang/Object; 512 0 5642 0 256
ciMethod java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 510 0 5757 0 0
ciMethod java/util/ArrayList$Itr checkForComodification ()V 510 0 5646 0 0
ciMethod java/util/Iterator remove ()V 0 0 1 0 -1
ciInstanceKlass jdk/internal/util/Preconditions$4 1 1 61 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 100 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 1 1
ciInstanceKlass java/util/Arrays$ArrayList 1 1 149 10 7 12 1 1 1 10 7 12 1 1 1 7 1 9 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 10 12 1 1 7 1 10 12 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Collections$UnmodifiableRandomAccessList
ciInstanceKlass java/util/Collections$UnmodifiableList 1 1 127 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 100 1 10 12 1 11 12 1 1 11 12 1 10 12 1 1 100 1 10 12 1 11 12 1 1 10 12 1 100 1 100 1 10 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
ciMethod java/util/Collections$UnmodifiableList <init> (Ljava/util/List;)V 510 0 2102 0 -1
ciInstanceKlass java/util/Collections$UnmodifiableRandomAccessList 1 1 52 10 7 12 1 1 1 7 1 9 12 1 1 11 7 12 1 1 1 10 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciMethod java/util/Collections$UnmodifiableRandomAccessList <init> (Ljava/util/List;)V 510 0 2102 0 -1
ciInstanceKlass java/util/Collections$UnmodifiableCollection$1 1 1 71 9 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 11 12 1 1 100 1 10 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/regex/IntHashSet 1 1 54 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciMethod java/util/regex/IntHashSet clear ()V 0 0 1 0 -1
ciMethod java/util/Collections unmodifiableList (Ljava/util/List;)Ljava/util/List; 512 0 1127 0 0
ciMethod jdk/internal/util/Preconditions outOfBoundsCheckFromIndexSize (Ljava/util/function/BiFunction;III)Ljava/lang/RuntimeException; 0 0 1 0 -1
ciMethod jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 514 0 49121 0 168
ciMethod jdk/internal/util/Preconditions checkIndex (IILjava/util/function/BiFunction;)I 1024 0 5673 0 -1
ciMethod java/lang/Integer valueOf (I)Ljava/lang/Integer; 152 0 1445 0 0
ciMethod java/lang/Integer <init> (I)V 528 0 568 0 0
ciMethod java/lang/Number <init> ()V 608 0 2204 0 0
ciMethod java/lang/Character valueOf (C)Ljava/lang/Character; 80 0 40 0 -1
ciMethod jdk/internal/util/ArraysSupport vectorizedHashCode (Ljava/lang/Object;IIII)I 1024 0 11239 0 -1
ciMethod java/lang/Math max (II)I 1024 0 32803 0 -1
ciMethod java/lang/Math min (II)I 1024 0 46246 0 -1
ciMethod java/util/Iterator next ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Iterator hasNext ()Z 0 0 1 0 -1
ciMethod java/lang/String replace (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String; 512 0 6301 0 -1
ciMethod java/lang/String isLatin1 ()Z 1024 0 404472 0 112
ciMethod java/lang/String checkBoundsOffCount (III)I 520 0 22997 0 160
ciMethod java/lang/String rangeCheck ([CII)Ljava/lang/Void; 500 0 5245 0 0
ciMethod java/lang/String coder ()B 1024 0 197768 0 88
ciMethod java/lang/String indexOf (II)I 522 0 6770 0 448
ciMethod java/lang/String checkIndex (II)V 1024 0 363592 0 120
ciMethod java/lang/String <init> ([CII)V 242 0 5245 0 872
ciMethod java/lang/String <init> ([CIILjava/lang/Void;)V 512 0 6001 0 0
ciMethod java/lang/CharSequence length ()I 0 0 1 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 326 0 43669 0 -1
ciMethod java/lang/StringBuilder <init> ()V 146 0 10026 0 -1
ciMethod java/lang/StringBuilder <init> (Ljava/lang/String;)V 512 0 3766 0 -1
ciMethod java/lang/Object getClass ()Ljava/lang/Class; 256 0 128 0 -1
ciMethod java/lang/Object <init> ()V 10024 0 235751 0 80
ciInstanceKlass lombok/patcher/TargetMatcher 1 0 15 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/MethodTarget 1 1 305 7 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 1 1 1 1 1 1 1 10 7 1 12 1 1 9 12 8 1 9 12 8 1 9 12 1 1 1 1 9 12 10 7 1 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 10 12 1 1 1 1 1 1 1 1 1 1 1 9 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 1 1 1 1 8 1 10 12 1 1 10 100 1 12 1 10 12 100 1 8 10 8 8 8 8 1 10 12 1 1 8 1 100 1 8 1 10 10 7 1 12 1 1 10 7 1 12 1 1 1 1 1 10 12 1 1 10 7 1 12 1 8 1 7 1 10 10 12 1 11 7 1 12 1 10 12 1 1 1 1 1 1 1 1 1 10 12 1 1 1 1 1 10 12 1 1 1 10 12 10 12 1 1 10 12 11 12 1 1 11 7 1 12 1 1 10 12 1 11 12 1 1 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 1 1 1 1 1 8 1 10 12 1 1 1 10 12 11 1 10 12 1 1 11 1 1 1 8 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 1 1
staticfield lombok/patcher/MethodTarget PARAM_SPEC Ljava/util/regex/Pattern; java/util/regex/Pattern
staticfield lombok/patcher/MethodTarget COMPLETE_SPEC Ljava/util/regex/Pattern; java/util/regex/Pattern
staticfield lombok/patcher/MethodTarget BRACE_PAIRS Ljava/util/regex/Pattern; java/util/regex/Pattern
ciInstanceKlass lombok/patcher/Hook 1 1 233 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 10 12 1 8 1 8 1 11 7 1 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 7 1 12 1 1 9 12 1 1 1 1 1 10 100 1 8 1 10 12 1 8 8 8 9 12 9 12 9 12 7 1 10 11 7 1 12 1 1 10 12 1 1 9 12 1 1 1 1 1 1 1 8 10 7 1 12 1 1 1 1 1 1 1 1 1 10 12 1 1 1 7 1 10 8 1 10 12 1 1 11 12 1 1 11 7 1 12 1 1 10 12 1 11 12 1 8 1 10 12 1 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 11 12 1 1 8 1 10 12 1 1 8 1 10 12 1 1 10 1 1 10 12 11 1 10 12 1 1 11 1 1 1 8 1 10 8 1 8 1 8 1 10 12 1 8 1 1 1
staticfield lombok/patcher/Hook PRIMITIVES Ljava/util/Map; java/util/Collections$UnmodifiableMap
instanceKlass lombok/patcher/scripts/AddFieldScript$1
instanceKlass lombok/patcher/PatchScript$MethodPatcher
instanceKlass org/lombokweb/asm/ClassWriter
instanceKlass lombok/patcher/PatchScript$NoopClassVisitor
ciInstanceKlass org/lombokweb/asm/ClassVisitor 1 1 175 1 7 1 7 1 1 100 1 100 1 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 3 1 100 1 8 10 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 8 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 1 1 1 12 10 1 1 1 1 8 12 10 1 1 1 8 12 10 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1
instanceKlass lombok/patcher/PatchScript$FixedClassWriter
ciInstanceKlass org/lombokweb/asm/ClassWriter 1 1 578 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 3 12 10 12 9 1 7 1 12 10 1 12 10 12 9 12 9 1 1 1 1 1 1 12 9 12 9 3 1 1 12 10 12 9 1 1 12 10 12 9 1 1 12 10 1 7 1 12 9 12 9 12 9 12 9 1 1 1 1 1 1 1 1 1 12 9 1 7 1 12 10 3 1 1 12 10 12 9 1 1 1 1 1 100 1 12 10 1 12 10 12 9 1 1 12 9 1 1 1 12 9 1 1 12 10 12 9 1 1 1 1 12 9 1 7 1 1 12 10 12 9 1 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 9 1 12 9 1 1 12 9 12 9 1 1 12 10 1 1 12 9 12 9 1 1 1 12 9 1 12 9 12 9 1 1 1 1 1 1 1 100 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 7 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 1 1 7 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 1 1 1 12 10 1 12 10 1 12 9 1 8 1 8 1 8 1 8 1 8 1 8 3 1 8 1 8 1 12 10 1 8 1 8 1 8 1 12 10 1 12 10 1 12 10 1 8 1 8 1 8 3 1 12 10 1 8 10 1 12 10 1 12 10 1 12 10 1 100 1 1 12 10 1 12 10 10 3 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 1 12 10 1 1 12 10 12 10 1 1 1 10 1 12 10 1 1 12 10 10 10 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 12 10 1 1 12 10 12 10 1 8 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 100 1 12 10 10 1 1 1 1 1
ciInstanceKlass lombok/patcher/PatchScript$FixedClassWriter 1 1 35 100 1 7 1 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 10 12 8 1 100 1 1 1 1 1 1 1 100 1 1
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$2
ciInstanceKlass lombok/patcher/PatchScript$MethodPatcher 1 1 184 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 10 12 1 7 1 10 12 1 9 12 9 12 9 12 9 12 1 1 1 1 1 1 1 1 9 12 1 1 11 7 1 12 1 1 1 1 1 1 9 12 10 12 1 1 1 1 1 1 1 1 1 100 1 8 1 10 12 1 1 1 11 12 1 1 11 7 1 12 1 1 7 1 7 1 8 1 10 10 12 1 10 7 1 12 1 1 8 1 10 12 1 1 10 12 1 11 7 1 12 1 1 9 12 10 7 1 12 1 1 11 12 1 1 1 1 1 10 12 10 12 1 10 12 1 10 12 1 11 12 1 7 1 11 12 1 1 7 1 10 12 1 11 7 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/PatchScript$MethodPatcherFactory 1 0 13 100 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass lombok/patcher/scripts/AddFieldScript$1 1 1 80 7 1 7 1 1 1 1 1 1 1 1 9 12 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 1 10 12 1 1 1 1 1 1 1 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 1 12 10 1 1 1 1 1 12 1 1 1
ciInstanceKlass org/lombokweb/asm/ClassReader 1 1 1065 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 12 9 12 9 1 1 12 10 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 12 10 1 12 10 1 1 12 10 12 9 1 7 12 9 10 12 9 12 9 1 100 12 9 1 1 12 10 12 9 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 12 10 1 1 1 1 12 10 1 1 1 8 12 10 1 100 1 1 12 10 1 1 1 100 1 8 10 1 1 12 10 1 100 10 1 100 1 1 12 10 1 12 10 1 12 10 1 12 10 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 1 1 7 10 1 1 12 9 12 9 12 9 1 12 10 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 3 1 8 1 8 1 1 12 10 1 8 1 8 1 8 3 1 8 1 8 1 8 1 8 1 1 12 10 1 1 12 9 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 100 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 10 10 10 10 1 1 1 1 1 1 8 1 1 12 10 1 1 12 10 1 7 10 10 10 10 1 1 1 1 1 1 1 12 9 1 12 9 1 12 9 1 8 1 8 1 8 1 8 1 8 1 8 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 100 10 10 10 1 1 12 10 10 1 12 10 1 1 12 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 9 1 1 12 10 1 1 12 10 1 8 1 1 12 10 1 8 1 8 1 1 12 10 1 1 12 10 1 8 1 8 1 12 9 1 12 9 1 12 9 1 12 9 1 1 12 9 1 12 9 1 12 9 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 100 10 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 12 9 1 12 9 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 10 1 1 12 9 1 1 100 1 12 10 1 12 10 1 1 1 1 1 1 1 1 3 3 3 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 1 12 10 1 100 1 12 10 1 100 1 12 10 1 100 1 1 12 9 1 12 9 1 12 10 1 7 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 7 1 1 12 9 1 1 12 10 1 12 9 1 12 9 1 12 9 1 12 9 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 12 9 1 1 1 1 1 1 12 9 1 12 10 10 1 1 1 1 1 1 5 0 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 7 1 12 10 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 12 10 12 10 1 1
instanceKlass org/lombokweb/asm/AnnotationWriter
ciInstanceKlass org/lombokweb/asm/AnnotationVisitor 1 1 98 1 100 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/AnnotationWriter 1 1 254 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 10 12 9 12 9 12 9 1 100 1 12 9 12 9 12 9 12 9 1 1 1 1 12 10 1 100 1 1 12 10 1 1 12 10 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 1 12 9 1 100 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 100 1 12 9 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 100 1 100 1 100 1 100 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 8 1 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 1 1 1 8 12 10 1 8 1 8 1 8 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$WrapWithSymbol
instanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$ReplaceMethodCall
instanceKlass lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly
instanceKlass org/lombokweb/asm/MethodWriter
ciInstanceKlass org/lombokweb/asm/MethodVisitor 1 1 262 1 7 1 7 1 1 100 1 100 1 1 1 1 8 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 1 100 10 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 8 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 100 1 100 1 1 12 10 1 100 1 8 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/MethodWriter 1 1 809 1 7 1 7 1 1 100 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 10 1 7 1 12 10 12 9 12 9 8 1 7 1 1 12 10 3 12 9 1 7 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 12 10 1 7 1 12 9 12 9 1 7 1 12 10 12 9 12 9 1 7 10 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 12 9 1 1 12 9 12 9 1 1 12 10 1 1 12 9 1 7 1 12 10 1 1 12 9 1 1 12 10 12 9 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 9 12 9 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 10 12 9 1 12 9 12 9 1 1 1 1 12 9 1 1 12 9 1 100 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 12 9 10 1 12 9 1 1 12 10 12 9 1 1 12 10 1 12 10 1 1 12 10 1 100 1 8 1 12 10 12 9 12 9 1 100 10 1 12 10 1 1 12 10 10 12 9 1 7 1 1 12 9 1 12 9 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 10 12 9 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 12 9 1 12 9 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 12 10 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 12 9 1 1 1 1 1 1 12 9 1 1 12 10 12 9 12 9 12 9 1 12 9 1 1 1 12 10 1 12 9 1 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 3 12 9 12 9 1 1 1 7 1 12 10 12 9 1 12 9 1 1 1 1 1 1 1 12 9 12 9 12 9 12 9 1 1 1 100 1 12 10 1 1 12 9 12 9 1 1 1 12 10 1 12 10 1 12 9 1 8 1 1 12 10 1 12 9 1 12 9 1 12 9 1 100 1 1 12 9 1 12 10 1 12 9 1 12 9 1 12 10 1 12 9 1 12 9 1 1 12 10 1 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 12 10 1 12 9 1 12 10 1 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 12 10 1 7 1 12 10 1 12 10 1 1 1 1 12 10 3 1 7 1 1 12 10 1 1 1 1 1 1 1 1 12 9 12 9 1 1 1 3 1 100 1 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 12 10 1 8 1 1 12 10 1 8 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 12 10 1 1 1 12 9 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1
staticfield org/lombokweb/asm/MethodWriter STACK_SIZE_DELTA [I 202
ciInstanceKlass org/lombokweb/asm/SymbolTable 1 1 632 1 7 1 7 1 1 7 1 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 7 10 12 9 1 1 1 1 7 1 1 12 9 1 1 12 10 1 12 9 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 100 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 8 1 7 1 1 12 10 12 9 12 9 1 1 12 10 1 12 10 3 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 9 1 1 1 1 12 10 1 7 1 12 9 1 1 1 12 9 1 1 1 1 12 10 1 12 9 1 1 1 12 10 1 1 12 10 1 1 1 1 12 9 12 9 1 1 12 9 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 12 10 1 7 10 1 7 1 1 12 10 1 7 10 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 100 10 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 12 10 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1 12 10 12 10 12 9 12 9 12 9 12 9 1 12 10 1 1 12 10 1 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 9 12 10 12 10 1 1 12 10 1 12 10 1 12 10 12 10 1 12 10 12 10 1 1 1 1 12 10 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 12 10 12 10 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 12 9 1 1 12 9 1 12 9 1 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 1 12 10 9 1 1 1 12 9 1 100 1 1 12 10 12 10 1 1 1 1 1 1 100 1 1 12 10 1 12 9 1 1 12 10 1 12 9 12 9 1 12 10 1 1 1 10 1 1 1 1 1 1 1 1 1
instanceKlass org/lombokweb/asm/SymbolTable$Entry
ciInstanceKlass org/lombokweb/asm/Symbol 1 1 93 1 7 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 1 1 1 1 12 9 1 7 1 12 10 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/SymbolTable$Entry 1 1 38 1 7 1 7 1 1 100 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/ByteVector 1 1 107 1 7 1 7 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 12 9 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 10 3 1 100 1 8 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 10 1 1 1 100 1 8 1 12 10 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1 1 1 90 7 1 7 1 100 1 1 1 1 1 1 1 1 9 12 9 12 10 12 1 1 1 1 1 1 1 10 7 1 12 1 1 10 12 1 1 10 7 1 12 1 1 100 1 100 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 7 1 10 12 1 1 1 1 1 1 1 1 1 1 12 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1 1 160 7 1 7 1 1 1 1 1 1 1 1 1 1 9 12 3 10 12 1 9 12 9 12 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 1 9 12 10 12 1 1 10 12 1 10 12 1 1 9 7 1 12 1 1 11 7 1 12 1 1 10 12 1 1 9 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 7 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Attribute 1 1 137 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 1 1 100 1 1 12 10 12 9 1 100 1 12 9 1 100 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 1 1 1 1 1 1 12 9 1 1 1 1 12 10 1 1 1 7 12 9 1 1 12 10 12 10 12 9 1 1 1 12 10 1 8 1 8 3 1 8 1 1 1 1 1 12 10 1 1 1 1 12 10 1 12 10 1 12 9 1 1 12 10 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Context 1 1 43 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1
ciInstanceKlass java/util/NoSuchElementException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/lombokweb/asm/MethodTooLargeException
instanceKlass org/lombokweb/asm/ClassTooLargeException
instanceKlass java/lang/ArrayIndexOutOfBoundsException
ciInstanceKlass java/lang/IndexOutOfBoundsException 0 0 49 10 100 12 1 1 1 10 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionError 0 0 79 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/channels/OverlappingFileLockException
ciInstanceKlass java/lang/IllegalStateException 1 0 35 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/lombokweb/asm/CurrentFrame
ciInstanceKlass org/lombokweb/asm/Frame 0 0 504 1 100 1 100 1 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 1 1 100 1 1 12 10 1 100 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 100 1 12 9 1 8 1 12 9 1 100 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 100 1 12 10 1 1 1 100 10 1 8 1 1 12 10 1 12 10 12 10 1 12 10 1 1 8 12 10 1 1 1 1 1 1 3 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 1 100 1 12 9 12 9 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 9 1 1 1 1 1 12 10 12 10 1 1 1 1 12 10 12 10 1 1 1 1 1 1 1 3 1 1 12 10 1 100 1 12 9 1 1 1 1 1 1 12 9 1 8 8 1 8 1 8 12 10 1 100 10 12 10 12 10 12 10 1 8 12 10 1 12 9 12 10 3 3 3 3 3 3 3 3 10 1 1 8 12 10 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 10 1 12 10 1 1 1 1 1 3 1 12 10 8 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 1 12 10 1 12 9 1 12 10 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Type 1 1 379 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 1 1 1 7 1 1 12 10 1 1 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 10 1 1 12 10 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 7 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 12 10 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 12 10 1 12 10 1 1 1 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 12 10 12 10 10 1 8 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 1 8 1 8 12 10 1 1 12 10 1 1 12 10 1 100 10 1 8 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 100 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield org/lombokweb/asm/Type VOID_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type BOOLEAN_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type CHAR_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type BYTE_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type SHORT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type INT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type FLOAT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type LONG_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type DOUBLE_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
ciInstanceKlass org/lombokweb/asm/Label 1 1 231 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 9 1 100 1 8 1 12 10 12 9 1 1 12 9 1 100 1 12 9 1 1 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 7 1 1 12 10 3 1 1 12 10 1 1 1 1 1 1 1 1 7 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 12 9 1 1 1 1 1 1 1 1 12 9 1 1 1 1 1 1 1 12 9 12 9 12 9 1 1 12 10 1 1 1 1 100 12 9 12 9 1 12 9 1 12 10 1 1 1 1 12 9 1 1 1 1 1 1 1 1 12 10 1 1 1 100 10 1 8 1 1 12 10 1 12 10 12 10 12 10 1 10 1 1 1 1 1 1
staticfield org/lombokweb/asm/Label EMPTY_LIST Lorg/lombokweb/asm/Label; org/lombokweb/asm/Label
ciInstanceKlass lombok/patcher/MethodLogistics 1 1 169 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 9 12 10 7 1 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 7 1 10 12 1 1 9 12 10 12 1 9 12 7 1 10 10 7 1 12 1 1 11 12 1 1 10 12 1 11 12 1 1 10 7 1 12 1 1 9 12 9 12 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 11 12 1 1 1 11 12 1 1 10 12 1 10 7 1 12 1 1 1 1 1 1 10 12 10 12 1 1 1 1 1 1 1 1 10 12 1 1 100 1 8 1 10 12 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 1 1
ciInstanceKlass lombok/patcher/scripts/WrapReturnValuesScript$1 1 1 54 7 1 7 1 100 1 1 1 1 1 1 1 1 9 12 9 12 10 12 1 1 1 1 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 100 1 12 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1 1 155 7 1 7 1 1 1 1 1 1 1 1 1 1 1 9 12 3 10 12 1 9 12 9 12 10 7 1 12 1 1 9 12 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 12 10 12 1 1 9 7 1 12 1 1 11 7 1 12 1 1 10 12 1 1 9 12 10 12 1 1 10 12 1 9 12 1 10 12 1 9 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 10 12 1 10 12 1 11 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Handle 1 1 93 1 7 1 7 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 1 1 1 1 1 1 1 1 1 1 7 12 10 1 1 1 1 12 10 1 1 8 1 8 1 1 1 100 10 1 1 12 10 1 8 1 8 1 12 10 1 8 12 10 12 10 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$ReplaceMethodCall 1 1 134 7 1 7 1 1 1 1 1 1 1 1 1 1 9 12 3 10 12 1 9 12 9 12 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 9 7 1 12 1 1 11 7 1 12 1 9 12 10 7 1 12 1 1 9 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/ConcurrentModificationException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 230739 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String hashCode ()I 2 14152 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x60007 0x31d3 0x108 0x576 0xd0007 0x3 0xe8 0x573 0x110005 0x573 0x0 0x0 0x0 0x0 0x0 0x140007 0x0 0x48 0x573 0x1b0002 0x573 0x1e0003 0x573 0x28 0x250002 0x0 0x2a0007 0x572 0x38 0x1 0x320003 0x1 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xe oops 0 methods 0
ciMethodData java/lang/String isLatin1 ()Z 2 403962 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0x629fb 0x80000006000a0007 0x5 0x38 0x629f7 0xe0003 0x629f7 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 hashCode ([B)I 2 5242 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x20008 0x6 0x1476 0x70 0x1 0x40 0x4 0x58 0x1d0003 0x1 0x40 0x270003 0x4 0x28 0x300002 0x1476 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData java/lang/String equals (Ljava/lang/Object;)Z 2 8192 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x20007 0x1b8d 0x20 0x474 0x80104 0x0 0x0 0x284e4ad9278 0x1b81 0x0 0x0 0xb0007 0xc 0xe0 0x1b81 0xf0004 0x0 0x0 0x284e4ad9278 0x1b81 0x0 0x0 0x160007 0x0 0x40 0x1b81 0x210007 0x0 0x68 0x1b81 0x2c0002 0x1b81 0x2f0007 0x19e1 0x38 0x1a0 0x330003 0x1a0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/String 18 java/lang/String methods 0
ciMethodData java/lang/String coder ()B 2 197256 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x30007 0x0 0x38 0x30289 0xa0003 0x30289 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String charAt (I)C 2 362774 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x10005 0x58917 0x0 0x0 0x0 0x0 0x0 0x40007 0x1 0x30 0x58917 0xc0002 0x58917 0x150002 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 charAt ([BI)C 2 362774 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x30002 0x58917 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String checkIndex (II)V 2 363080 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x50002 0x58a49 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String length ()I 2 141271 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x60005 0x227d8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData java/lang/StringLatin1 canEncode (I)Z 2 30024 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x40007 0x0 0x38 0x7549 0x80003 0x7549 0x18 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/AbstractCollection <init> ()V 2 19347 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x4b94 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 charAt ([BI)C 1 9 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x20002 0x9 0x70002 0x9 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String indexOf (II)I 2 6509 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 43 0x10005 0x196d 0x0 0x0 0x0 0x0 0x0 0x40007 0x0 0x80 0x196d 0xe0005 0x196d 0x0 0x0 0x0 0x0 0x0 0x110002 0x196d 0x140003 0x196d 0x60 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x210002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 indexOf ([BIII)I 2 6509 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x10002 0x196d 0x40007 0x196d 0x20 0x0 0xb0002 0x196d 0x120002 0x196d 0x180007 0x194a 0x20 0x23 0x210002 0x194a 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 indexOfChar ([BIII)I 2 6468 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0xa0007 0x163e 0x58 0xbdf8 0x130007 0xbaf2 0x20 0x306 0x1c0003 0xbaf2 0xffffffffffffffc0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 2 48864 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 22 0x50007 0x0 0x40 0xbee0 0xc0007 0xbee0 0x30 0x0 0x130002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String checkBoundsOffCount (III)I 2 22737 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x60002 0x58d1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String <init> ([CII)V 2 5124 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x70002 0x1404 0xa0002 0x1404 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String rangeCheck ([CII)Ljava/lang/Void; 2 4995 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40002 0x1383 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String <init> ([CIILjava/lang/Void;)V 2 5745 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x10002 0x1671 0x50007 0x1661 0x20 0x10 0x1e0007 0x0 0x50 0x1661 0x240002 0x1661 0x2b0007 0x1 0x20 0x1660 0x430002 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0xc 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 compress ([CII)[B 2 6645 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x90002 0x19f6 0xd0007 0x1 0x20 0x19f5 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethod java/lang/IllegalStateException <init> ()V 0 0 1 0 -1
ciMethodData java/lang/Integer valueOf (I)Ljava/lang/Integer; 2 1369 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x30007 0x7 0x40 0x552 0xa0007 0x123 0x20 0x42f 0x1c0002 0x12a 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Integer <init> (I)V 1 304 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x10002 0x130 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData java/lang/Number <init> ()V 2 1900 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x76c 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr hasNext ()Z 2 5961 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xb0007 0x515 0x38 0x1234 0xf0003 0x1234 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;)Z 2 8327 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x140005 0x2087 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList <init> ()V 2 5312 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x14c0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/AbstractList <init> ()V 2 12295 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x3007 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList iterator ()Ljava/util/Iterator; 2 5500 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x50002 0x157c 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 2 5502 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x60002 0x157e 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 5386 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x10005 0x150a 0x0 0x0 0x0 0x0 0x0 0x110007 0x150a 0x30 0x0 0x180002 0x0 0x270007 0x150a 0x30 0x0 0x2e0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr checkForComodification ()V 2 5391 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0xb0007 0x150f 0x30 0x0 0x120002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/Collections unmodifiableList (Ljava/util/List;)Ljava/util/List; 1 871 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x10005 0x1d 0x0 0x284e4adccd8 0x343 0x284eb20e2f0 0x7 0x80007 0x0 0x78 0x367 0xc0005 0x1d 0x0 0x284e4adccd8 0x343 0x284eb20e2f0 0x7 0x130007 0x34a 0x20 0x1d 0x190004 0x0 0x0 0x284e4adccd8 0x343 0x284eb20e2f0 0x7 0x1c0007 0x0 0x48 0x34a 0x240002 0x34a 0x270003 0x34a 0x28 0x2f0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 6 3 java/util/ArrayList 5 java/util/Arrays$ArrayList 14 java/util/ArrayList 16 java/util/Arrays$ArrayList 25 java/util/ArrayList 27 java/util/Arrays$ArrayList methods 0
ciMethodData java/lang/String replace (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String; 2 6045 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 178 0x10005 0x0 0x0 0x284e4ad9278 0x179d 0x0 0x0 0x80005 0x0 0x0 0x284e4ad9278 0x179d 0x0 0x0 0x100005 0x179d 0x0 0x0 0x0 0x0 0x0 0x160005 0x179d 0x0 0x0 0x0 0x0 0x0 0x1d0005 0x179d 0x0 0x0 0x0 0x0 0x0 0x240007 0x0 0x268 0x179d 0x2a0007 0x0 0xe8 0x179d 0x300007 0x0 0xc8 0x179d 0x360005 0x179d 0x0 0x0 0x0 0x0 0x0 0x3c0005 0x179d 0x0 0x0 0x0 0x0 0x0 0x3f0005 0x179d 0x0 0x0 0x0 0x0 0x0 0x440005 0x0 0x0 0x0 0x0 0x0 0x0 0x4a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x510005 0x0 0x0 0x0 0x0 0x0 0x0 0x580007 0x0 0x88 0x0 0x5d0007 0x0 0x68 0x0 0x620007 0x0 0x48 0x0 0x780002 0x0 0x7b0003 0x0 0x28 0x970002 0x0 0x9e0007 0x0 0x20 0x0 0xab0002 0x0 0xb00002 0x0 0xb30002 0x0 0xb80003 0x0 0x28 0xc40002 0x0 0xce0002 0x0 0xd70005 0x0 0x0 0x0 0x0 0x0 0x0 0xe20007 0x0 0xe0 0x0 0xea0005 0x0 0x0 0x0 0x0 0x0 0x0 0xed0005 0x0 0x0 0x0 0x0 0x0 0x0 0xf20005 0x0 0x0 0x0 0x0 0x0 0x0 0xf90003 0x0 0xffffffffffffff38 0xfe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 3 java/lang/String 10 java/lang/String methods 0
ciMethod java/util/ConcurrentModificationException <init> ()V 0 0 1 0 -1
ciMethod lombok/patcher/TargetMatcher matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 0 0 1 0 -1
ciMethod lombok/patcher/MethodTarget decomposeFullDesc (Ljava/lang/String;)Ljava/util/List; 150 248 62 0 0
ciMethod lombok/patcher/MethodTarget classMatches (Ljava/lang/String;)Z 26 0 45 0 0
ciMethod lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 1024 0 6705 0 0
ciMethod lombok/patcher/MethodTarget descriptorMatch (Ljava/lang/String;)Z 22 16 43 0 0
ciMethod lombok/patcher/MethodTarget typeSpecMatch (Ljava/lang/String;Ljava/lang/String;)Z 184 130 82 0 -1
ciMethod lombok/patcher/MethodTarget typeMatches (Ljava/lang/String;Ljava/lang/String;)Z 552 0 30198 0 -1
ciMethod lombok/patcher/Hook getMethodName ()Ljava/lang/String; 1328 0 664 0 0
ciMethod lombok/patcher/Hook getMethodDescriptor ()Ljava/lang/String; 270 438 171 0 0
ciMethod lombok/patcher/Hook toSpec (Ljava/lang/String;)Ljava/lang/String; 512 26 440 0 -1
ciMethod org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 1024 0 7133 0 0
ciMethod org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 924 0 7140 0 0
ciMethod lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 994 2048 6619 0 0
ciMethod lombok/patcher/PatchScript$MethodPatcherFactory createMethodVisitor (Ljava/lang/String;Ljava/lang/String;Lorg/lombokweb/asm/MethodVisitor;Llombok/patcher/MethodLogistics;)Lorg/lombokweb/asm/MethodVisitor; 0 0 1 0 -1
ciMethodData lombok/patcher/MethodTarget typeMatches (Ljava/lang/String;Ljava/lang/String;)Z 2 29922 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x60005 0x74e2 0x0 0x0 0x0 0x0 0x0 0xa0005 0x74e2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethod org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I 620 1020 7268 0 -1
ciMethod org/lombokweb/asm/ClassReader readCode (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;I)V 12 5296 27 0 0
ciMethod org/lombokweb/asm/ClassReader readBytecodeInstructionOffset (I)V 512 0 1298 0 -1
ciMethod org/lombokweb/asm/ClassReader createLabel (I[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Label; 224 0 671 0 -1
ciMethod org/lombokweb/asm/ClassReader createDebugLabel (I[Lorg/lombokweb/asm/Label;)V 786 0 1212 0 -1
ciMethod org/lombokweb/asm/ClassReader readTypeAnnotations (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;IZ)[I 0 0 1 0 -1
ciMethod org/lombokweb/asm/ClassReader getTypeAnnotationBytecodeOffset ([II)I 132 0 54 0 -1
ciMethod org/lombokweb/asm/ClassReader readTypeAnnotationTarget (Lorg/lombokweb/asm/Context;I)I 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readParameterAnnotations (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;IZ)V 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readElementValues (Lorg/lombokweb/asm/AnnotationVisitor;IZ[C)I 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readElementValue (Lorg/lombokweb/asm/AnnotationVisitor;ILjava/lang/String;[C)I 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader computeImplicitFrame (Lorg/lombokweb/asm/Context;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/ClassReader readStackMapFrame (IZZLorg/lombokweb/asm/Context;)I 574 2086 233 0 -1
ciMethod org/lombokweb/asm/ClassReader readAttribute ([Lorg/lombokweb/asm/Attribute;Ljava/lang/String;II[CI[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Attribute; 0 0 1 0 0
ciMethod org/lombokweb/asm/ClassReader readByte (I)I 52 0 1052 0 0
ciMethod org/lombokweb/asm/ClassReader readUnsignedShort (I)I 512 0 1425 0 184
ciMethod org/lombokweb/asm/ClassReader readShort (I)S 514 0 665 0 -1
ciMethod org/lombokweb/asm/ClassReader readInt (I)I 512 0 2320 0 216
ciMethod org/lombokweb/asm/ClassReader readLong (I)J 46 0 311 0 -1
ciMethod org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 752 0 68601 0 2192
ciMethod org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 540 0 6572 0 2112
ciMethod org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 242 6144 1257 0 1584
ciMethod org/lombokweb/asm/ClassReader readStringish (I[C)Ljava/lang/String; 510 0 12276 0 -1
ciMethod org/lombokweb/asm/ClassReader readClass (I[C)Ljava/lang/String; 512 0 12276 0 2336
ciMethod org/lombokweb/asm/ClassReader readConst (I[C)Ljava/lang/Object; 586 0 2666 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visit (Ljava/lang/String;Ljava/lang/Object;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitEnum (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitAnnotation (Ljava/lang/String;Ljava/lang/String;)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitArray (Ljava/lang/String;)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationVisitor visitEnd ()V 0 0 1 0 -1
ciMethod org/lombokweb/asm/AnnotationWriter visitEnd ()V 0 0 1 0 0
ciMethod org/lombokweb/asm/MethodVisitor <init> (I)V 924 0 7140 0 0
ciMethod org/lombokweb/asm/MethodVisitor <init> (ILorg/lombokweb/asm/MethodVisitor;)V 930 0 7160 0 0
ciMethod org/lombokweb/asm/MethodVisitor stringConcat$0 (I)Ljava/lang/String; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitParameter (Ljava/lang/String;I)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAnnotationDefault ()Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAnnotation (Ljava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTypeAnnotation (ILorg/lombokweb/asm/TypePath;Ljava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAnnotableParameterCount (IZ)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitParameterAnnotation (ILjava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitAttribute (Lorg/lombokweb/asm/Attribute;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitCode ()V 52 0 20 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitFrame (II[Ljava/lang/Object;I[Ljava/lang/Object;)V 512 0 187 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitInsn (I)V 836 0 483 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitIntInsn (II)V 144 0 35 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitVarInsn (II)V 522 0 1000 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTypeInsn (ILjava/lang/String;)V 242 0 92 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitFieldInsn (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V 512 0 456 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitMethodInsn (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V 768 0 315 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitInvokeDynamicInsn (Ljava/lang/String;Ljava/lang/String;Lorg/lombokweb/asm/Handle;[Ljava/lang/Object;)V 4 0 2 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitJumpInsn (ILorg/lombokweb/asm/Label;)V 528 0 269 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLabel (Lorg/lombokweb/asm/Label;)V 580 0 796 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLdcInsn (Ljava/lang/Object;)V 232 0 31 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitIincInsn (II)V 60 0 4 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTableSwitchInsn (IILorg/lombokweb/asm/Label;[Lorg/lombokweb/asm/Label;)V 4 0 2 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLookupSwitchInsn (Lorg/lombokweb/asm/Label;[I[Lorg/lombokweb/asm/Label;)V 10 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitMultiANewArrayInsn (Ljava/lang/String;I)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitInsnAnnotation (ILorg/lombokweb/asm/TypePath;Ljava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitTryCatchBlock (Lorg/lombokweb/asm/Label;Lorg/lombokweb/asm/Label;Lorg/lombokweb/asm/Label;Ljava/lang/String;)V 22 0 11 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLocalVariable (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/lombokweb/asm/Label;Lorg/lombokweb/asm/Label;I)V 512 0 144 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitLocalVariableAnnotation (ILorg/lombokweb/asm/TypePath;[Lorg/lombokweb/asm/Label;[Lorg/lombokweb/asm/Label;[ILjava/lang/String;Z)Lorg/lombokweb/asm/AnnotationVisitor; 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitMaxs (II)V 52 0 20 0 -1
ciMethod org/lombokweb/asm/MethodVisitor visitEnd ()V 52 0 20 0 -1
ciMethod org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 924 300 7140 0 0
ciMethod org/lombokweb/asm/MethodWriter visitLabel (Lorg/lombokweb/asm/Label;)V 486 0 7536 0 -1
ciMethod org/lombokweb/asm/MethodWriter addSuccessorToCurrentBasicBlock (ILorg/lombokweb/asm/Label;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/MethodWriter canCopyMethodAttributes (Lorg/lombokweb/asm/ClassReader;ZZIII)Z 680 184 5482 0 688
ciMethod org/lombokweb/asm/MethodWriter setMethodAttributesSource (II)V 674 0 7113 0 0
ciMethod org/lombokweb/asm/SymbolTable getSource ()Lorg/lombokweb/asm/ClassReader; 280 0 140 0 0
ciMethod org/lombokweb/asm/SymbolTable getMajorVersion ()I 302 0 151 0 0
ciMethod org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 512 0 9911 0 176
ciMethod org/lombokweb/asm/SymbolTable put (Lorg/lombokweb/asm/SymbolTable$Entry;)Lorg/lombokweb/asm/SymbolTable$Entry; 278 0 381 0 0
ciMethod org/lombokweb/asm/SymbolTable addConstantClass (Ljava/lang/String;)Lorg/lombokweb/asm/Symbol; 512 0 1377 0 0
ciMethod org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 364 116 7812 0 920
ciMethod org/lombokweb/asm/SymbolTable addConstantUtf8Reference (ILjava/lang/String;)Lorg/lombokweb/asm/Symbol; 512 438 1472 0 0
ciMethod org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 768 0 20024 0 0
ciMethod org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 844 0 6887 0 136
ciMethod org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 768 0 7149 0 128
ciMethod org/lombokweb/asm/ByteVector <init> ()V 538 0 7263 0 0
ciMethod org/lombokweb/asm/ByteVector putByte (I)Lorg/lombokweb/asm/ByteVector; 512 0 3176 0 0
ciMethod org/lombokweb/asm/ByteVector put12 (II)Lorg/lombokweb/asm/ByteVector; 274 0 1153 0 0
ciMethod org/lombokweb/asm/ByteVector putUTF8 (Ljava/lang/String;)Lorg/lombokweb/asm/ByteVector; 214 4096 200 0 0
ciMethod org/lombokweb/asm/ByteVector encodeUtf8 (Ljava/lang/String;II)Lorg/lombokweb/asm/ByteVector; 0 0 1 0 -1
ciMethod org/lombokweb/asm/ByteVector enlarge (I)V 18 0 121 0 -1
ciMethodData org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 1169 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 2 6302 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x80007 0x78e 0x20 0x1110 0x220005 0x0 0x0 0x284e5b8ed60 0x78e 0x0 0x0 0x260005 0x78e 0x0 0x0 0x0 0x0 0x0 0x2a0004 0x0 0x0 0x284e4ad9278 0x78e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 7 org/lombokweb/asm/ClassReader 21 java/lang/String methods 0
ciMethodData org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 2 1136 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x160007 0x470 0xa8 0x67d8 0x290007 0x0 0x38 0x67d8 0x390003 0x67d8 0x50 0x450007 0x0 0x38 0x0 0x640003 0x0 0x18 0x920003 0x67d8 0xffffffffffffff70 0x9d0002 0x470 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 68225 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x20005 0x0 0x0 0x284e5b8ed60 0x10a81 0x0 0x0 0x70007 0x21 0x40 0x10a60 0x80000006000b0007 0x10a55 0x20 0xc 0x130005 0x10a55 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 1 3 org/lombokweb/asm/ClassReader methods 0
ciMethodData org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 2 6465 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10002 0x1941 0x0 0x0 0x0 0x0 0x9 0x8 0x3e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethod org/lombokweb/asm/Attribute <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod org/lombokweb/asm/Attribute read (Lorg/lombokweb/asm/ClassReader;II[CI[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Attribute; 0 0 1 0 -1
ciMethod org/lombokweb/asm/Type getType (Ljava/lang/String;)Lorg/lombokweb/asm/Type; 0 0 1 0 -1
ciMethod org/lombokweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 316 332 6886 0 1832
ciMethod org/lombokweb/asm/Label <init> ()V 308 0 7572 0 0
ciMethod org/lombokweb/asm/Label addLineNumber (I)V 512 0 826 0 -1
ciMethod org/lombokweb/asm/Label accept (Lorg/lombokweb/asm/MethodVisitor;Z)V 792 0 908 0 -1
ciMethod org/lombokweb/asm/Label resolve ([BLorg/lombokweb/asm/ByteVector;I)Z 1024 74 7536 0 -1
ciMethod lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 8 10 20 0 0
ciMethod lombok/patcher/MethodLogistics loadOpcodeFor (Ljava/lang/String;)I 72 0 26 0 0
ciMethod lombok/patcher/MethodLogistics returnOpcodeFor (Ljava/lang/String;)I 56 0 20 0 0
ciMethod lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 128 0 46 0 0
ciMethodData org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 2 19640 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x40005 0x4cb8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xe oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 2 9655 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 2 6765 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x70002 0x1a6d 0x0 0x0 0x0 0x0 0x9 0x5 0x7e 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ByteVector putByte (I)Lorg/lombokweb/asm/ByteVector; 2 2920 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0xd0007 0xb43 0x58 0x25 0x120005 0x25 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ByteVector enlarge (I)V 1 112 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x90007 0x70 0x30 0x0 0x120002 0x0 0x270007 0x0 0x38 0x70 0x2b0003 0x70 0x18 0x3f0002 0x70 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x4 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readClass (I[C)Ljava/lang/String; 2 12020 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30005 0x2ef4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readStringish (I[C)Ljava/lang/String; 2 12021 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x70005 0x0 0x0 0x284e5b8ed60 0x2ef5 0x0 0x0 0xc0005 0x0 0x0 0x284e5b8ed60 0x2ef5 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 3 org/lombokweb/asm/ClassReader 10 org/lombokweb/asm/ClassReader methods 0
ciMethodData org/lombokweb/asm/SymbolTable addConstantUtf8Reference (ILjava/lang/String;)Lorg/lombokweb/asm/Symbol; 2 1216 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 69 0x20002 0x4c0 0x80005 0x4c0 0x0 0x0 0x0 0x0 0x0 0xf0007 0x21 0xd0 0x706 0x180007 0x1e1 0x98 0x525 0x210007 0x86 0x78 0x49f 0x2a0005 0x49f 0x0 0x0 0x0 0x0 0x0 0x2d0007 0x0 0x20 0x49f 0x3a0003 0x267 0xffffffffffffff48 0x440005 0x21 0x0 0x0 0x0 0x0 0x0 0x470005 0x21 0x0 0x0 0x0 0x0 0x0 0x5e0002 0x21 0x610005 0x21 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 2 7630 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 68 0x20002 0x1dce 0x80005 0x1dce 0x0 0x0 0x0 0x0 0x0 0xd0007 0x5c 0xd0 0x2bf9 0x150007 0x9f0 0x98 0x2209 0x1d0007 0x497 0x78 0x1d72 0x250005 0x1d72 0x0 0x0 0x0 0x0 0x0 0x280007 0x0 0x20 0x1d72 0x350003 0xe87 0xffffffffffffff48 0x3d0005 0x0 0x0 0x284ea6a28a0 0x5c 0x0 0x0 0x410005 0x0 0x0 0x284ea6a28a0 0x5c 0x0 0x0 0x580002 0x5c 0x5b0005 0x5c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x4c 0xffffffffffffffff oops 2 38 org/lombokweb/asm/ByteVector 45 org/lombokweb/asm/ByteVector methods 0
ciMethodData org/lombokweb/asm/ByteVector put12 (II)Lorg/lombokweb/asm/ByteVector; 2 1016 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 22 0xd0007 0x3e5 0x58 0x13 0x120005 0x13 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/SymbolTable put (Lorg/lombokweb/asm/SymbolTable$Entry;)Lorg/lombokweb/asm/SymbolTable$Entry; 1 242 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 46 0xd0007 0xf1 0xc8 0x1 0x290007 0x1 0xa8 0x34e 0x370007 0x34e 0x70 0x27b 0x5a0004 0x0 0x0 0x284ea37ccc0 0x27b 0x0 0x0 0x5f0003 0x27b 0xffffffffffffffa8 0x650003 0x34e 0xffffffffffffff70 0x940004 0x0 0x0 0x284ea37ccc0 0xf2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x44 0x40 oops 2 15 org/lombokweb/asm/SymbolTable$Entry 28 org/lombokweb/asm/SymbolTable$Entry methods 0
ciMethodData org/lombokweb/asm/SymbolTable addConstantClass (Ljava/lang/String;)Lorg/lombokweb/asm/Symbol; 2 1121 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x40005 0x461 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/Label <init> ()V 2 7418 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x1cfa 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ByteVector putUTF8 (Ljava/lang/String;)Lorg/lombokweb/asm/ByteVector; 1 93 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x10005 0x5d 0x0 0x0 0x0 0x0 0x0 0x80007 0x5d 0x30 0x0 0x110002 0x0 0x240007 0x5d 0x58 0x0 0x2b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4f0007 0x5d 0x100 0x93b 0x550005 0x93b 0x0 0x0 0x0 0x0 0x0 0x5d0007 0x0 0x58 0x93b 0x640007 0x0 0x38 0x93b 0x710003 0x93b 0x50 0x7f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x860003 0x93b 0xffffffffffffff18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readInt (I)I 2 2064 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ByteVector <init> ()V 2 6994 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x1b52 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I 2 6958 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 683 0xd0005 0x0 0x0 0x284e5b8ed60 0x1b2e 0x0 0x0 0x1b0005 0x0 0x0 0x284e5b8ed60 0x1b2e 0x0 0x0 0x290005 0x0 0x0 0x284e5b8ed60 0x1b2e 0x0 0x0 0x5f0005 0x0 0x0 0x284e5b8ed60 0x1b2e 0x0 0x0 0x6c0007 0x1b2e 0x7c8 0x1bf6 0x740005 0x0 0x0 0x284e5b8ed60 0x1bf6 0x0 0x0 0x7e0005 0x0 0x0 0x284e5b8ed60 0x1bf6 0x0 0x0 0x8b0005 0x1bf6 0x0 0x0 0x0 0x0 0x0 0x8e0007 0xdf 0x58 0x1b17 0x970007 0x0 0x6c8 0x1b17 0x9e0003 0x1b17 0x6a8 0xa60005 0xdf 0x0 0x0 0x0 0x0 0x0 0xa90007 0x35 0x118 0xaa 0xb30005 0x0 0x0 0x284e5b8ed60 0xaa 0x0 0x0 0xc90007 0xaa 0xa8 0xaa 0xd50005 0x0 0x0 0x284e5b8ed60 0xaa 0x0 0x0 0xd80004 0x0 0x0 0x284e4ad9278 0xaa 0x0 0x0 0xdf0003 0xaa 0xffffffffffffff70 0xe20003 0xaa 0x558 0xea0005 0x35 0x0 0x0 0x0 0x0 0x0 0xed0007 0x14 0x70 0x21 0xf30005 0x0 0x0 0x284e5b8ed60 0x21 0x0 0x0 0xf80003 0x21 0x4b0 0x1000005 0x14 0x0 0x0 0x0 0x0 0x0 0x1030007 0x0 0x38 0x14 0x1120003 0x14 0x440 0x11a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11d0007 0x0 0x38 0x0 0x1240003 0x0 0x3d0 0x12c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x12f0007 0x0 0x38 0x0 0x1360003 0x0 0x360 0x13e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1410007 0x0 0x38 0x0 0x1480003 0x0 0x2f0 0x1500005 0x0 0x0 0x0 0x0 0x0 0x0 0x1530007 0x0 0x38 0x0 0x1650003 0x0 0x280 0x16d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1700007 0x0 0x38 0x0 0x1770003 0x0 0x210 0x17f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1820007 0x0 0x38 0x0 0x1890003 0x0 0x1a0 0x1910005 0x0 0x0 0x0 0x0 0x0 0x0 0x1940007 0x0 0x38 0x0 0x19b0003 0x0 0x130 0x1a30005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a60007 0x0 0x38 0x0 0x1ad0003 0x0 0xc0 0x1b50005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b80007 0x0 0x38 0x0 0x1bf0003 0x0 0x50 0x1d10005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e80003 0x1bf6 0xfffffffffffff850 0x1fa0007 0x21 0x38 0x1b0d 0x1fe0003 0x1b0d 0x50 0x2060005 0x21 0x0 0x0 0x0 0x0 0x0 0x20b0005 0x7b 0x0 0x284e5b90710 0x149 0x284e5b907c0 0x196a 0x2120007 0x1ab7 0x20 0x77 0x21a0004 0xffffffffffffffef 0x0 0x284e5b90870 0x1aa6 0x0 0x0 0x21d0007 0x11 0x158 0x1aa6 0x2220004 0x0 0x0 0x284e5b90870 0x1aa6 0x0 0x0 0x2340007 0x1a92 0x38 0x14 0x2380003 0x14 0x18 0x2400005 0x0 0x0 0x284e5b8ed60 0x1aa6 0x0 0x0 0x2470005 0x1aa6 0x0 0x0 0x0 0x0 0x0 0x24a0007 0x4 0x58 0x1aa2 0x2540005 0x1aa2 0x0 0x0 0x0 0x0 0x0 0x25c0007 0x15 0x158 0x0 0x2650007 0x0 0x138 0x0 0x26b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x27b0007 0x0 0xe0 0x0 0x2850005 0x0 0x0 0x0 0x0 0x0 0x0 0x28d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2900005 0x0 0x0 0x0 0x0 0x0 0x0 0x2960003 0x0 0xffffffffffffff38 0x29b0007 0x15 0xe8 0x0 0x2a00005 0x0 0x0 0x0 0x0 0x0 0x0 0x2ad0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2b30007 0x0 0x58 0x0 0x2b80005 0x0 0x0 0x0 0x0 0x0 0x0 0x2bd0007 0x15 0x138 0x0 0x2c30005 0x0 0x0 0x0 0x0 0x0 0x0 0x2d30007 0x0 0xe0 0x0 0x2db0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2e90005 0x0 0x0 0x0 0x0 0x0 0x0 0x2f10005 0x0 0x0 0x0 0x0 0x0 0x0 0x2f60003 0x0 0xffffffffffffff38 0x2fb0007 0x15 0x138 0x0 0x3010005 0x0 0x0 0x0 0x0 0x0 0x0 0x3110007 0x0 0xe0 0x0 0x3190005 0x0 0x0 0x0 0x0 0x0 0x0 0x3270005 0x0 0x0 0x0 0x0 0x0 0x0 0x32f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3340003 0x0 0xffffffffffffff38 0x3390007 0x15 0x170 0x0 0x33f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x34f0007 0x0 0x118 0x0 0x3560005 0x0 0x0 0x0 0x0 0x0 0x0 0x3600005 0x0 0x0 0x0 0x0 0x0 0x0 0x3760005 0x0 0x0 0x0 0x0 0x0 0x0 0x37e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3830003 0x0 0xffffffffffffff00 0x3880007 0x15 0x170 0x0 0x38e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x39e0007 0x0 0x118 0x0 0x3a50005 0x0 0x0 0x0 0x0 0x0 0x0 0x3af0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c50005 0x0 0x0 0x0 0x0 0x0 0x0 0x3cd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3d20003 0x0 0xffffffffffffff00 0x3d70007 0x15 0x58 0x0 0x3e10005 0x0 0x0 0x0 0x0 0x0 0x0 0x3e60007 0x15 0x58 0x0 0x3f00005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f50007 0x15 0x70 0x0 0x4090005 0x0 0x0 0x0 0x0 0x0 0x0 0x4100003 0x0 0xffffffffffffffa8 0x4150007 0x0 0x90 0x15 0x41a0005 0x8 0x0 0x284e5b90920 0x9 0x284e5b90870 0x4 0x4230005 0x15 0x0 0x0 0x0 0x0 0x0 0x4280005 0x8 0x0 0x284e5b90920 0x9 0x284e5b90870 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 19 3 org/lombokweb/asm/ClassReader 10 org/lombokweb/asm/ClassReader 17 org/lombokweb/asm/ClassReader 24 org/lombokweb/asm/ClassReader 35 org/lombokweb/asm/ClassReader 42 org/lombokweb/asm/ClassReader 78 org/lombokweb/asm/ClassReader 89 org/lombokweb/asm/ClassReader 96 java/lang/String 120 org/lombokweb/asm/ClassReader 294 lombok/patcher/scripts/AddFieldScript$1 296 lombok/patcher/PatchScript$MethodPatcher 305 org/lombokweb/asm/MethodWriter 316 org/lombokweb/asm/MethodWriter 330 org/lombokweb/asm/ClassReader 637 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 639 org/lombokweb/asm/MethodWriter 651 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 653 org/lombokweb/asm/MethodWriter methods 0
ciMethodData org/lombokweb/asm/ClassReader readAttribute ([Lorg/lombokweb/asm/Attribute;Ljava/lang/String;II[CI[Lorg/lombokweb/asm/Label;)Lorg/lombokweb/asm/Attribute; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 50 0xf0007 0x0 0xc8 0x0 0x1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x220007 0x0 0x58 0x0 0x310005 0x0 0x0 0x0 0x0 0x0 0x0 0x380003 0x0 0xffffffffffffff50 0x400002 0x0 0x4a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodWriter setMethodAttributesSource (II)V 2 6776 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Matcher reset ()Ljava/util/regex/Matcher; 1 76 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 53 0x170007 0x4c 0x38 0x5f0 0x240003 0x5f0 0xffffffffffffffe0 0x2f0007 0x4c 0x38 0xd3 0x3c0003 0xd3 0xffffffffffffffe0 0x470007 0x4c 0x90 0x1b 0x500007 0x1b 0x58 0x0 0x590005 0x0 0x0 0x0 0x0 0x0 0x0 0x5f0003 0x1b 0xffffffffffffff88 0x6e0005 0x4c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x7e oops 0 methods 0
ciMethodData java/util/regex/Matcher getTextLength ()I 1 76 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x0 0x0 0x284e4ad9278 0x4c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/lang/String methods 0
ciMethodData org/lombokweb/asm/MethodWriter canCopyMethodAttributes (Lorg/lombokweb/asm/ClassReader;ZZIII)Z 2 5142 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 106 0x50005 0x1416 0x0 0x0 0x0 0x0 0x0 0x80007 0x2 0xb8 0x1414 0x110007 0x0 0x98 0x1414 0x1a0007 0x0 0x78 0x1414 0x260007 0x1402 0x38 0x12 0x2a0003 0x12 0x18 0x2e0007 0x1414 0x20 0x0 0x370005 0x1414 0x0 0x0 0x0 0x0 0x0 0x3c0007 0x1414 0x58 0x0 0x470007 0x0 0x38 0x0 0x4b0003 0x0 0x18 0x540007 0x1414 0x20 0x0 0x5b0007 0x73 0x40 0x13a1 0x620007 0x13a1 0x108 0x0 0x6a0005 0x0 0x0 0x284e5b8ed60 0x73 0x0 0x0 0x710007 0x0 0xb0 0x73 0x830007 0x73 0x90 0x73 0x890005 0x0 0x0 0x284e5b8ed60 0x73 0x0 0x0 0x930007 0x73 0x20 0x0 0x9e0003 0x73 0xffffffffffffff88 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x7 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 2 63 org/lombokweb/asm/ClassReader 78 org/lombokweb/asm/ClassReader methods 0
ciMethodData org/lombokweb/asm/ClassReader readByte (I)I 2 1026 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readElementValue (Lorg/lombokweb/asm/AnnotationVisitor;ILjava/lang/String;[C)I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 749 0x40007 0x0 0xe0 0x0 0x120008 0x8 0x0 0xc0 0x0 0x50 0x0 0x88 0x0 0x50 0x420005 0x0 0x0 0x0 0x0 0x0 0x0 0x4f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x660008 0x6a 0x0 0x1540 0x0 0x968 0x0 0x1540 0x0 0x360 0x0 0x430 0x0 0x500 0x0 0x1540 0x0 0x500 0x0 0x1540 0x0 0x1540 0x0 0x500 0x0 0x500 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x5c0 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x690 0x0 0xa28 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x8d0 0x0 0x1540 0x0 0x810 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x1540 0x0 0x788 0x14e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1520005 0x0 0x0 0x0 0x0 0x0 0x0 0x1560002 0x0 0x1590005 0x0 0x0 0x0 0x0 0x0 0x0 0x15f0003 0x0 0x1138 0x16c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1700005 0x0 0x0 0x0 0x0 0x0 0x0 0x1740002 0x0 0x1770005 0x0 0x0 0x0 0x0 0x0 0x0 0x17d0003 0x0 0x1068 0x1860005 0x0 0x0 0x0 0x0 0x0 0x0 0x18b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x18e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1940003 0x0 0xfa8 0x1a10005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a50005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a90002 0x0 0x1ac0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b20003 0x0 0xed8 0x1bf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c30005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c60007 0x0 0x38 0x0 0x1cc0003 0x0 0x18 0x1d20005 0x0 0x0 0x0 0x0 0x0 0x0 0x1d80003 0x0 0xde0 0x1e20005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e50005 0x0 0x0 0x0 0x0 0x0 0x0 0x1eb0003 0x0 0xd58 0x1f50005 0x0 0x0 0x0 0x0 0x0 0x0 0x1ff0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2020005 0x0 0x0 0x0 0x0 0x0 0x0 0x2080003 0x0 0xc98 0x2120005 0x0 0x0 0x0 0x0 0x0 0x0 0x2150002 0x0 0x2180005 0x0 0x0 0x0 0x0 0x0 0x0 0x21e0003 0x0 0xc00 0x2290005 0x0 0x0 0x0 0x0 0x0 0x0 0x22c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2360005 0x0 0x0 0x0 0x0 0x0 0x0 0x23b0003 0x0 0xb40 0x2410005 0x0 0x0 0x0 0x0 0x0 0x0 0x24b0007 0x0 0x90 0x0 0x2510005 0x0 0x0 0x0 0x0 0x0 0x0 0x25b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x26a0008 0x34 0x0 0x9c8 0x0 0x1b0 0x0 0x4d0 0x0 0x8c0 0x0 0x9c8 0x0 0x7b8 0x0 0x9c8 0x0 0x9c8 0x0 0x5c8 0x0 0x6c0 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x3d8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x9c8 0x0 0x2a8 0x2e90007 0x0 0xa8 0x0 0x2fa0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2fe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3090003 0x0 0xffffffffffffff70 0x3100005 0x0 0x0 0x0 0x0 0x0 0x0 0x3130003 0x0 0x7d0 0x3230007 0x0 0xe0 0x0 0x3340005 0x0 0x0 0x0 0x0 0x0 0x0 0x3380005 0x0 0x0 0x0 0x0 0x0 0x0 0x33b0007 0x0 0x38 0x0 0x33f0003 0x0 0x18 0x34a0003 0x0 0xffffffffffffff38 0x3510005 0x0 0x0 0x0 0x0 0x0 0x0 0x3540003 0x0 0x6a0 0x3640007 0x0 0xa8 0x0 0x3750005 0x0 0x0 0x0 0x0 0x0 0x0 0x3790005 0x0 0x0 0x0 0x0 0x0 0x0 0x3840003 0x0 0xffffffffffffff70 0x38b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x38e0003 0x0 0x5a8 0x39e0007 0x0 0xa8 0x0 0x3af0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3b30005 0x0 0x0 0x0 0x0 0x0 0x0 0x3be0003 0x0 0xffffffffffffff70 0x3c50005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c80003 0x0 0x4b0 0x3d80007 0x0 0xa8 0x0 0x3e90005 0x0 0x0 0x0 0x0 0x0 0x0 0x3ed0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f70003 0x0 0xffffffffffffff70 0x3fe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4010003 0x0 0x3b8 0x4110007 0x0 0xa8 0x0 0x4220005 0x0 0x0 0x0 0x0 0x0 0x0 0x4260005 0x0 0x0 0x0 0x0 0x0 0x0 0x4300003 0x0 0xffffffffffffff70 0x4370005 0x0 0x0 0x0 0x0 0x0 0x0 0x43a0003 0x0 0x2c0 0x44a0007 0x0 0xb8 0x0 0x45b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x45f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4620002 0x0 0x46c0003 0x0 0xffffffffffffff60 0x4730005 0x0 0x0 0x0 0x0 0x0 0x0 0x4760003 0x0 0x1b8 0x4860007 0x0 0xb8 0x0 0x4970005 0x0 0x0 0x0 0x0 0x0 0x0 0x49b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x49e0002 0x0 0x4a80003 0x0 0xffffffffffffff60 0x4af0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4b20003 0x0 0xb0 0x4b80005 0x0 0x0 0x0 0x0 0x0 0x0 0x4c20005 0x0 0x0 0x0 0x0 0x0 0x0 0x4c70003 0x0 0x28 0x4ce0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/AnnotationWriter visitEnd ()V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x50007 0x0 0x20 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readElementValues (Lorg/lombokweb/asm/AnnotationVisitor;IZ[C)I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 70 0x60005 0x0 0x0 0x0 0x0 0x0 0x0 0xf0007 0x0 0xc8 0x0 0x170007 0x0 0x118 0x0 0x1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x330003 0x0 0xffffffffffffff70 0x3b0007 0x0 0x70 0x0 0x450005 0x0 0x0 0x0 0x0 0x0 0x0 0x4a0003 0x0 0xffffffffffffffa8 0x4e0007 0x0 0x58 0x0 0x520005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readTypeAnnotationTarget (Lorg/lombokweb/asm/Context;I)I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 276 0x40005 0x0 0x0 0x0 0x0 0x0 0x0 0xe0008 0x9a 0x0 0x768 0x0 0x4e0 0x0 0x4e0 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x738 0x0 0x738 0x0 0x738 0x0 0x4f8 0x0 0x4f8 0x0 0x4f8 0x0 0x4e0 0x0 0x738 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x768 0x0 0x510 0x0 0x510 0x0 0x738 0x0 0x750 0x0 0x750 0x0 0x750 0x0 0x750 0x0 0x720 0x0 0x720 0x0 0x720 0x0 0x720 0x0 0x720 0x1570003 0x0 0x298 0x1650003 0x0 0x280 0x1740005 0x0 0x0 0x0 0x0 0x0 0x0 0x19d0007 0x0 0x1c0 0x0 0x1a20005 0x0 0x0 0x0 0x0 0x0 0x0 0x1ab0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b40005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c90005 0x0 0x0 0x0 0x0 0x0 0x0 0x1cc0004 0x0 0x0 0x0 0x0 0x0 0x0 0x1dd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e00004 0x0 0x0 0x0 0x0 0x0 0x0 0x1ed0003 0x0 0xfffffffffffffe58 0x1f00003 0x0 0x70 0x1fe0003 0x0 0x58 0x20c0003 0x0 0x40 0x21a0003 0x0 0x28 0x2210002 0x0 0x22d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2350007 0x0 0x38 0x0 0x2390003 0x0 0x28 0x2450002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readParameterAnnotations (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;IZ)V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 62 0x180005 0x0 0x0 0x0 0x0 0x0 0x0 0x280007 0x0 0x150 0x0 0x2e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3b0007 0x0 0xe0 0x0 0x430005 0x0 0x0 0x0 0x0 0x0 0x0 0x530005 0x0 0x0 0x0 0x0 0x0 0x0 0x5b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x600003 0x0 0xffffffffffffff38 0x660003 0x0 0xfffffffffffffec8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 2 6678 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 116 0x30002 0x1a16 0xb0002 0x1a16 0x1a0005 0x1a16 0x0 0x0 0x0 0x0 0x0 0x1d0007 0x19fa 0x38 0x1c 0x240003 0x1c 0x18 0x2e0005 0x1a16 0x0 0x0 0x0 0x0 0x0 0x3d0005 0x1a16 0x0 0x0 0x0 0x0 0x0 0x4c0007 0x12 0x38 0x1a04 0x500003 0x1a04 0x50 0x560005 0x12 0x0 0x0 0x0 0x0 0x0 0x5e0007 0x19d9 0xc8 0x3d 0x640007 0x0 0xa8 0x3d 0x810007 0x3d 0x70 0x3d 0x900005 0x3d 0x0 0x0 0x0 0x0 0x0 0x9a0003 0x3d 0xffffffffffffffa8 0x9d0003 0x3d 0x18 0xb20007 0xa8 0x98 0x196e 0xb70002 0x196e 0xc20007 0x17db 0x20 0x193 0xd90002 0x196e 0xe40005 0x196e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodVisitor <init> (I)V 2 6678 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x30002 0x1a16 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/MethodVisitor <init> (ILorg/lombokweb/asm/MethodVisitor;)V 2 6695 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 51 0x10002 0x1a27 0x70007 0x1a27 0x100 0x0 0xd0007 0x0 0xe0 0x0 0x130007 0x0 0xc0 0x0 0x190007 0x0 0xa0 0x0 0x1f0007 0x0 0x80 0x0 0x250007 0x0 0x60 0x0 0x2b0007 0x0 0x40 0x0 0x330002 0x0 0x360002 0x0 0x3d0007 0x1a27 0x30 0x0 0x410002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 2 6728 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 97 0x60005 0x1a48 0x0 0x0 0x0 0x0 0x0 0xd0007 0x1a48 0x1d8 0x17f7 0x130007 0x20 0x40 0x17d7 0x190007 0x17d7 0x38 0x0 0x220003 0x20 0x128 0x270005 0x1a90 0x0 0x0 0x0 0x0 0x0 0x2c0007 0x17d7 0x38 0x2b9 0x320003 0x2b9 0xffffffffffffffa8 0x3a0005 0x17d7 0x0 0x0 0x0 0x0 0x0 0x3f0007 0x93b 0x68 0xe9c 0x460005 0xe9c 0x0 0x0 0x0 0x0 0x0 0x500002 0xe9c 0x590005 0x17f7 0x0 0x0 0x0 0x0 0x0 0x5d0003 0x17f7 0xfffffffffffffe40 0x640005 0x1a48 0x0 0x0 0x0 0x0 0x0 0x6b0007 0x422 0x20 0x1626 0x750007 0x0 0x40 0x422 0x7b0007 0x422 0x38 0x0 0x7f0003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassReader readCode (Lorg/lombokweb/asm/MethodVisitor;Lorg/lombokweb/asm/Context;I)V 2 21 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 2691 0x120005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x1c0005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x260005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x380007 0x15 0x30 0x0 0x3f0002 0x0 0x600007 0x15 0x1440 0xa84 0x770008 0x1bc 0x0 0x13f8 0x0 0xdf0 0x3b 0xdf0 0x8 0xdf0 0x35 0xdf0 0x45 0xdf0 0x6 0xdf0 0x2 0xdf0 0x1 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1b 0x1398 0x8 0x13b0 0x6 0x1398 0x18 0x13b0 0x5 0x13b0 0x50 0x1398 0x1 0x1398 0x0 0x1398 0x0 0x1398 0xae 0x1398 0x0 0xdf0 0x10 0xdf0 0x21 0xdf0 0x9 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x138 0xdf0 0x96 0xdf0 0xe 0xdf0 0x23 0xdf0 0x14 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x12 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x2d 0x1398 0x1 0x1398 0x0 0x1398 0x0 0x1398 0x59 0x1398 0x0 0xdf0 0x3 0xdf0 0x4 0xdf0 0xb 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1 0xdf0 0x4 0xdf0 0x4 0xdf0 0x13 0xdf0 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xd 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x8 0xdf0 0x0 0xdf0 0x40 0xdf0 0xe 0xdf0 0x0 0xdf0 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xd 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1b 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xf 0xdf0 0x2 0xdf0 0xa 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x4 0x13b0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x3 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x5d 0xe08 0x2e 0xe08 0x1 0xe08 0x3 0xe08 0x0 0xe08 0x3 0xe08 0x1 0xe08 0xa 0xe08 0x4 0xe08 0x1 0xe08 0x0 0xe08 0x3 0xe08 0x1 0xe08 0x3 0xe08 0x49 0xe08 0x0 0xe08 0x0 0x1398 0x2 0x10c0 0x0 0x1248 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xb 0xdf0 0x1b 0xdf0 0x1d 0x13b0 0x1f 0x13b0 0x13e 0x13b0 0x4f 0x13b0 0xb6 0x13b0 0x15 0x13b0 0x3a 0x13b0 0x1e 0x13c8 0x2 0x13c8 0x15 0x13b0 0x0 0x1398 0xd 0x13b0 0xa 0xdf0 0x7 0xdf0 0x18 0x13b0 0x1d 0x13b0 0x0 0xdf0 0x0 0xdf0 0x0 0xfa0 0x0 0x13e0 0x19 0xe08 0x8 0xe08 0x0 0xf18 0x0 0xf18 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xf18 0x3fb0003 0x45a 0x618 0x4060005 0x0 0x0 0x284e5b8ed60 0x113 0x0 0x0 0x40c0005 0x113 0x0 0x0 0x0 0x0 0x0 0x4130003 0x113 0x590 0x41e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4240005 0x0 0x0 0x0 0x0 0x0 0x0 0x42b0003 0x0 0x508 0x4360005 0x0 0x0 0x0 0x0 0x0 0x0 0x43c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4430003 0x0 0x480 0x4510008 0x1a 0x0 0x110 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xe0 0x0 0xf8 0x0 0xe0 0x4bf0003 0x0 0x388 0x4c50003 0x0 0x370 0x4cc0002 0x0 0x4e10005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x4e70005 0x2 0x0 0x0 0x0 0x0 0x0 0x4f10005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x4f90005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x5090007 0x2 0x268 0x11 0x5120005 0x0 0x0 0x284e5b8ed60 0x11 0x0 0x0 0x5180005 0x11 0x0 0x0 0x0 0x0 0x0 0x51f0003 0x11 0xffffffffffffff70 0x5330005 0x0 0x0 0x0 0x0 0x0 0x0 0x5390005 0x0 0x0 0x0 0x0 0x0 0x0 0x5420005 0x0 0x0 0x0 0x0 0x0 0x0 0x54f0007 0x0 0x118 0x0 0x55a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5600005 0x0 0x0 0x0 0x0 0x0 0x0 0x5670003 0x0 0xffffffffffffff70 0x56d0003 0x1a7 0x70 0x5730003 0x34e 0x58 0x5790003 0x20 0x40 0x57f0003 0x0 0x28 0x5860002 0x0 0x58a0003 0xa84 0xffffffffffffebd8 0x5900005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x59d0007 0x15 0x230 0xe 0x5a40005 0x0 0x0 0x284e5b8ed60 0xe 0x0 0x0 0x5a90005 0xe 0x0 0x0 0x0 0x0 0x0 0x5b40005 0x0 0x0 0x284e5b8ed60 0xe 0x0 0x0 0x5b90005 0xe 0x0 0x0 0x0 0x0 0x0 0x5c40005 0x0 0x0 0x284e5b8ed60 0xe 0x0 0x0 0x5c90005 0xe 0x0 0x0 0x0 0x0 0x0 0x5d90005 0x0 0x0 0x284e5b8ed60 0xe 0x0 0x0 0x5df0005 0x0 0x0 0x284e5b8ed60 0xe 0x0 0x0 0x5f00005 0x3 0x0 0x284e5b90920 0x6 0x284eb1f58a0 0x5 0x5f30003 0xe 0xfffffffffffffde8 0x6110005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x61e0007 0x15 0x780 0x3c 0x6260005 0x0 0x0 0x284e5b8ed60 0x3c 0x0 0x0 0x6300005 0x0 0x0 0x284e5b8ed60 0x3c 0x0 0x0 0x63d0005 0x3c 0x0 0x0 0x0 0x0 0x0 0x6400007 0x27 0x1a8 0x15 0x6490007 0x0 0x680 0x15 0x6570005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x6640007 0x15 0x118 0x91 0x66a0005 0x0 0x0 0x284e5b8ed60 0x91 0x0 0x0 0x6740005 0x91 0x0 0x0 0x0 0x0 0x0 0x67c0005 0x0 0x0 0x284e5b8ed60 0x91 0x0 0x0 0x6890005 0x91 0x0 0x0 0x0 0x0 0x0 0x68f0003 0x91 0xffffffffffffff00 0x6920003 0x15 0x510 0x69a0005 0x27 0x0 0x0 0x0 0x0 0x0 0x69d0007 0x27 0x38 0x0 0x6a40003 0x0 0x4a0 0x6ac0005 0x27 0x0 0x0 0x0 0x0 0x0 0x6af0007 0x12 0x1a8 0x15 0x6b80007 0x0 0x430 0x15 0x6c20005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x6cf0007 0x15 0x118 0x2d9 0x6d50005 0x0 0x0 0x284e5b8ed60 0x2d9 0x0 0x0 0x6df0005 0x0 0x0 0x284e5b8ed60 0x2d9 0x0 0x0 0x6ec0005 0x2d9 0x0 0x0 0x0 0x0 0x0 0x6f60005 0x2d9 0x0 0x0 0x0 0x0 0x0 0x6f90003 0x2d9 0xffffffffffffff00 0x6fc0003 0x15 0x2c0 0x7040005 0x12 0x0 0x0 0x0 0x0 0x0 0x7070007 0x12 0x70 0x0 0x7100005 0x0 0x0 0x0 0x0 0x0 0x0 0x7150003 0x0 0x218 0x71d0005 0x12 0x0 0x0 0x0 0x0 0x0 0x7200007 0x12 0x70 0x0 0x7290005 0x0 0x0 0x0 0x0 0x0 0x0 0x72e0003 0x0 0x170 0x7360005 0x12 0x0 0x0 0x0 0x0 0x0 0x7390007 0x0 0x58 0x12 0x7420007 0x0 0x100 0x12 0x7520003 0x12 0xe0 0x75a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x75d0007 0x0 0x58 0x0 0x7660007 0x0 0x70 0x0 0x7790003 0x0 0x50 0x78c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7a30003 0x3c 0xfffffffffffff898 0x7ad0007 0x15 0x38 0x0 0x7b10003 0x0 0x18 0x7b90007 0x3 0x1a0 0x12 0x7e90007 0x12 0x58 0x0 0x7ee0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7fb0007 0x12 0x128 0x636 0x8050007 0x62d 0xf0 0x9 0x80d0005 0x0 0x0 0x284e5b8ed60 0x9 0x0 0x0 0x8140007 0x0 0x98 0x9 0x81b0007 0x5 0x78 0x4 0x82d0007 0x0 0x58 0x4 0x8350005 0x4 0x0 0x0 0x0 0x0 0x0 0x83c0003 0x636 0xfffffffffffffef0 0x8410007 0x15 0x78 0x0 0x84c0007 0x0 0x58 0x0 0x8560005 0x0 0x0 0x0 0x0 0x0 0x0 0x8600005 0x15 0x0 0x0 0x0 0x0 0x0 0x86c0005 0x15 0x0 0x0 0x0 0x0 0x0 0x87c0007 0x0 0x38 0x15 0x8810003 0x15 0x18 0x88f0007 0x15 0x2710 0xa84 0x89c0005 0x0 0x0 0x284e5b8ed60 0xa84 0x0 0x0 0x8a80007 0x76f 0x90 0x315 0x8b40007 0x0 0x38 0x315 0x8b80003 0x315 0x18 0x8bc0005 0x315 0x0 0x0 0x0 0x0 0x0 0x8c10007 0x86 0x1d0 0xad2 0x8ca0007 0xc2 0x40 0xa10 0x8d20007 0x9fe 0x190 0x12 0x8da0007 0x12 0xe8 0xc2 0x8df0007 0x0 0x40 0xc2 0x8e40007 0xc2 0x70 0x0 0x8f90005 0x0 0x0 0x0 0x0 0x0 0x0 0x8fc0003 0x0 0x50 0x9140005 0x74 0x0 0x284e5b90920 0x46 0x284e5b90870 0x8 0x91e0007 0x12 0x70 0xc2 0x9290005 0xc2 0x0 0x0 0x0 0x0 0x0 0x92e0003 0xc2 0xfffffffffffffe60 0x9340003 0x12 0xfffffffffffffe48 0x9390007 0xa84 0x78 0x0 0x9430007 0x0 0x58 0x0 0x94e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x9610008 0x1bc 0x0 0x2058 0x0 0xdf0 0x3b 0xdf0 0x8 0xdf0 0x35 0xdf0 0x45 0xdf0 0x6 0xdf0 0x2 0xdf0 0x1 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1b 0x1768 0x8 0x17b8 0x6 0x1840 0x18 0x18c8 0x5 0x18c8 0x50 0x1718 0x1 0x1718 0x0 0x1718 0x0 0x1718 0xae 0x1718 0x0 0xe40 0x10 0xe40 0x21 0xe40 0x9 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x0 0xe40 0x138 0xe40 0x96 0xe40 0xe 0xe40 0x23 0xe40 0x14 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x12 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x2d 0x1718 0x1 0x1718 0x0 0x1718 0x0 0x1718 0x59 0x1718 0x0 0xe90 0x3 0xe90 0x4 0xe90 0xb 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x0 0xe90 0x1 0xe90 0x4 0xe90 0x4 0xe90 0x13 0xe90 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xd 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x8 0xdf0 0x0 0xdf0 0x40 0xdf0 0xe 0xdf0 0x0 0xdf0 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xd 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1b 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xf 0xdf0 0x2 0xdf0 0xa 0xdf0 0x1 0xdf0 0x0 0xdf0 0x0 0xdf0 0x4 0x1f80 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x3 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0x5d 0xee0 0x2e 0xee0 0x1 0xee0 0x3 0xee0 0x0 0xee0 0x3 0xee0 0x1 0xee0 0xa 0xee0 0x4 0xee0 0x1 0xee0 0x0 0xee0 0x3 0xee0 0x1 0xee0 0x3 0xee0 0x49 0xee0 0x0 0xee0 0x0 0x1718 0x2 0x13d8 0x0 0x1578 0xc 0xdf0 0x0 0xdf0 0x0 0xdf0 0x0 0xdf0 0xb 0xdf0 0x1b 0xdf0 0x1d 0x1988 0x1f 0x1988 0x13e 0x1988 0x4f 0x1988 0xb6 0x1988 0x15 0x1988 0x3a 0x1988 0x1e 0x1988 0x2 0x1bd0 0x15 0x1ef8 0x0 0x1768 0xd 0x1ef8 0xa 0xdf0 0x7 0xdf0 0x18 0x1ef8 0x1d 0x1ef8 0x0 0xdf0 0x0 0xdf0 0x0 0x1270 0x0 0x1fd0 0x19 0xee0 0x8 0xee0 0x0 0xf68 0x0 0xf68 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0xff0 0x0 0x11e8 0xce70005 0xe6 0x0 0x284e5b90920 0xf9 0x284e5b90870 0x14 0xced0003 0x1f3 0x1240 0xcff0005 0x12a 0x0 0x284e5b90920 0x103 0x284e5b90870 0xc 0xd050003 0x239 0x11f0 0xd170005 0x1b 0x0 0x284e5b90920 0x10 0x284e5b90870 0x3 0xd1d0003 0x2e 0x11a0 0xd2c0005 0x0 0x0 0x284e5b8ed60 0x113 0x0 0x0 0xd310005 0xa7 0x0 0x284e5b90920 0x65 0x284e5b90870 0x7 0xd370003 0x113 0x1118 0xd490005 0x0 0x0 0x0 0x0 0x0 0x0 0xd4e0005 0x0 0x0 0x0 0x0 0x0 0x0 0xd540003 0x0 0x1090 0xd5c0007 0x0 0x38 0x0 0xd640003 0x0 0x18 0xd770005 0x0 0x0 0x0 0x0 0x0 0x0 0xd830007 0x0 0x40 0x0 0xd8b0007 0x0 0x70 0x0 0xd960005 0x0 0x0 0x0 0x0 0x0 0x0 0xd990003 0x0 0xf8 0xda10007 0x0 0x38 0x0 0xdac0003 0x0 0x18 0xdbc0005 0x0 0x0 0x0 0x0 0x0 0x0 0xdc60005 0x0 0x0 0x0 0x0 0x0 0x0 0xdcf0005 0x0 0x0 0x0 0x0 0x0 0x0 0xdd80003 0x0 0xe98 0xde80005 0x0 0x0 0x0 0x0 0x0 0x0 0xded0005 0x0 0x0 0x0 0x0 0x0 0x0 0xdf60003 0x0 0xe10 0xe0b0007 0x0 0xe0 0x0 0xe140005 0x0 0x0 0x0 0x0 0x0 0x0 0xe1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe250003 0x0 0xd30 0xe300005 0x0 0x0 0x0 0x0 0x0 0x0 0xe330005 0x0 0x0 0x0 0x0 0x0 0x0 0xe390003 0x0 0xca8 0xe4e0005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0xe5a0005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0xe650005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0xe810007 0x2 0xa8 0x11 0xe8f0005 0x0 0x0 0x284e5b8ed60 0x11 0x0 0x0 0xe940004 0x0 0x0 0x284ea3ff590 0x11 0x0 0x0 0xe9b0003 0x11 0xffffffffffffff70 0xea70005 0x0 0x0 0x284eb1f58a0 0x2 0x0 0x0 0xeaa0003 0x2 0xb08 0xebf0005 0x0 0x0 0x0 0x0 0x0 0x0 0xecb0005 0x0 0x0 0x0 0x0 0x0 0x0 0xee70007 0x0 0xe0 0x0 0xef10005 0x0 0x0 0x0 0x0 0x0 0x0 0xf020005 0x0 0x0 0x0 0x0 0x0 0x0 0xf070004 0x0 0x0 0x0 0x0 0x0 0x0 0xf0e0003 0x0 0xffffffffffffff38 0xf180005 0x0 0x0 0x0 0x0 0x0 0x0 0xf1b0003 0x0 0x968 0xf2c0005 0x2b 0x0 0x284e5b90920 0x6e 0x284eb1f58a0 0xed 0xf320003 0x186 0x918 0xf3f0005 0x3 0x0 0x284e5b90920 0x5 0x284eb1f58a0 0x13 0xf450003 0x1b 0x8c8 0xf500005 0x0 0x0 0x284e5b8ed60 0x8 0x0 0x0 0xf530005 0x0 0x0 0x284e5b90920 0x6 0x284eb1f5950 0x2 0xf590003 0x8 0x840 0xf6b0005 0x0 0x0 0x284e5b8ed60 0x6 0x0 0x0 0xf6e0005 0x0 0x0 0x284e5b90870 0x3 0x284eb1f58a0 0x3 0xf740003 0x6 0x7b8 0xf7e0005 0x0 0x0 0x284e5b8ed60 0x1d 0x0 0x0 0xf830005 0x0 0x0 0x284e5b8ed60 0x1d 0x0 0x0 0xf860005 0xa 0x0 0x284e5b90920 0x12 0x284e5b90870 0x1 0xf8c0003 0x1d 0x6f8 0xf980005 0x0 0x0 0x284e5b8ed60 0x2ec 0x0 0x0 0xfa70005 0x0 0x0 0x284e5b8ed60 0x2ec 0x0 0x0 0xfb20005 0x0 0x0 0x284e5b8ed60 0x2ec 0x0 0x0 0xfbc0005 0x0 0x0 0x284e5b8ed60 0x2ec 0x0 0x0 0xfc80005 0x0 0x0 0x284e5b8ed60 0x2ec 0x0 0x0 0xfd20007 0x123 0x70 0x1c9 0xfde0005 0x69 0x0 0x284e5b90920 0xd9 0x284eb1f58a0 0x87 0xfe10003 0x1c9 0x88 0xfed0007 0x105 0x38 0x1e 0xff10003 0x1e 0x18 0x10020005 0xd3 0x0 0x284e5b90920 0x46 0x284e5b90870 0xa 0x100a0007 0x2ce 0x38 0x1e 0x10100003 0x1e 0x4c8 0x10160003 0x2ce 0x4b0 0x10220005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10310005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x103c0005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10480005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10540005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x105e0005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10630005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10660004 0x0 0x0 0x284eb1eab20 0x2 0x0 0x0 0x10700005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10830007 0x2 0xe0 0x2 0x108e0005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10930005 0x0 0x0 0x284e5b8ed60 0x2 0x0 0x0 0x10960004 0x0 0x0 0x284e4ad9278 0x2 0x0 0x0 0x109d0003 0x2 0xffffffffffffff38 0x10a90005 0x0 0x0 0x284e5b90920 0x2 0x0 0x0 0x10af0003 0x2 0x188 0x10bc0005 0x0 0x0 0x284e5b8ed60 0x57 0x0 0x0 0x10bf0005 0x29 0x0 0x284e5b90920 0x2d 0x284e5b90870 0x1 0x10c50003 0x57 0x100 0x10db0005 0x0 0x0 0x284eb1f58a0 0x3 0x284e5b90920 0x1 0x10e10003 0x4 0xb0 0x10ec0005 0x0 0x0 0x0 0x0 0x0 0x0 0x10fa0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11000003 0x0 0x28 0x11070002 0x0 0x110d0007 0xa84 0x1b0 0x0 0x11150007 0x0 0x190 0x0 0x111c0007 0x0 0x170 0x0 0x11230007 0x0 0x100 0x0 0x112d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11370005 0x0 0x0 0x0 0x0 0x0 0x0 0x114c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11540005 0x0 0x0 0x0 0x0 0x0 0x0 0x11600005 0x0 0x0 0x0 0x0 0x0 0x0 0x11650003 0x0 0xfffffffffffffe68 0x116a0007 0xa84 0x1b0 0x0 0x11720007 0x0 0x190 0x0 0x11790007 0x0 0x170 0x0 0x11800007 0x0 0x100 0x0 0x118a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11940005 0x0 0x0 0x0 0x0 0x0 0x0 0x11a90005 0x0 0x0 0x0 0x0 0x0 0x0 0x11b10005 0x0 0x0 0x0 0x0 0x0 0x0 0x11bd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11c20003 0x0 0xfffffffffffffe68 0x11c50003 0xa84 0xffffffffffffd908 0x11cd0007 0x1 0x58 0x14 0x11d60005 0x7 0x0 0x284e5b90920 0x9 0x284e5b90870 0x4 0x11db0007 0x0 0x3e8 0x15 0x11e40007 0x0 0x3c8 0x15 0x11ec0007 0x15 0x100 0x0 0x11f20005 0x0 0x0 0x0 0x0 0x0 0x0 0x12080007 0x0 0xa8 0x0 0x12250005 0x0 0x0 0x0 0x0 0x0 0x0 0x12330005 0x0 0x0 0x0 0x0 0x0 0x0 0x123a0003 0x0 0xffffffffffffff70 0x12400005 0x0 0x0 0x284e5b8ed60 0x15 0x0 0x0 0x12500007 0x15 0x270 0x91 0x12560005 0x0 0x0 0x284e5b8ed60 0x91 0x0 0x0 0x12600005 0x0 0x0 0x284e5b8ed60 0x91 0x0 0x0 0x126c0005 0x0 0x0 0x284e5b8ed60 0x91 0x0 0x0 0x12790005 0x0 0x0 0x284e5b8ed60 0x91 0x0 0x0 0x12840005 0x0 0x0 0x284e5b8ed60 0x91 0x0 0x0 0x12910007 0x91 0xe8 0x0 0x129c0007 0x0 0xc8 0x0 0x12a60007 0x0 0x90 0x0 0x12b20007 0x0 0x70 0x0 0x12bf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x12c40003 0x0 0x30 0x12ca0003 0x0 0xffffffffffffff50 0x12e30005 0x55 0x0 0x284e5b90920 0x34 0x284e5b90870 0x8 0x12e60003 0x91 0xfffffffffffffda8 0x12eb0007 0x15 0x1b0 0x0 0x12fe0007 0x0 0x190 0x0 0x130b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13140007 0x0 0x40 0x0 0x131b0007 0x0 0x100 0x0 0x13220005 0x0 0x0 0x0 0x0 0x0 0x0 0x132c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x134d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13550005 0x0 0x0 0x0 0x0 0x0 0x0 0x135c0003 0x0 0xfffffffffffffe88 0x13610007 0x15 0x1b0 0x0 0x13740007 0x0 0x190 0x0 0x13810005 0x0 0x0 0x0 0x0 0x0 0x0 0x138a0007 0x0 0x40 0x0 0x13910007 0x0 0x100 0x0 0x13980005 0x0 0x0 0x0 0x0 0x0 0x0 0x13a20005 0x0 0x0 0x0 0x0 0x0 0x0 0x13c30005 0x0 0x0 0x0 0x0 0x0 0x0 0x13cb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13d20003 0x0 0xfffffffffffffe88 0x13d70007 0x15 0x70 0x0 0x13ea0005 0x0 0x0 0x0 0x0 0x0 0x0 0x13f10003 0x0 0xffffffffffffffa8 0x13f90005 0x8 0x0 0x284e5b90920 0x9 0x284e5b90870 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 97 3 org/lombokweb/asm/ClassReader 10 org/lombokweb/asm/ClassReader 17 org/lombokweb/asm/ClassReader 483 org/lombokweb/asm/ClassReader 570 org/lombokweb/asm/ClassReader 584 org/lombokweb/asm/ClassReader 591 org/lombokweb/asm/ClassReader 602 org/lombokweb/asm/ClassReader 678 org/lombokweb/asm/ClassReader 689 org/lombokweb/asm/ClassReader 703 org/lombokweb/asm/ClassReader 717 org/lombokweb/asm/ClassReader 731 org/lombokweb/asm/ClassReader 738 org/lombokweb/asm/ClassReader 745 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 747 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 755 org/lombokweb/asm/ClassReader 766 org/lombokweb/asm/ClassReader 773 org/lombokweb/asm/ClassReader 795 org/lombokweb/asm/ClassReader 806 org/lombokweb/asm/ClassReader 820 org/lombokweb/asm/ClassReader 869 org/lombokweb/asm/ClassReader 880 org/lombokweb/asm/ClassReader 887 org/lombokweb/asm/ClassReader 1032 org/lombokweb/asm/ClassReader 1101 org/lombokweb/asm/ClassReader 1160 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1162 org/lombokweb/asm/MethodWriter 1645 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1647 org/lombokweb/asm/MethodWriter 1655 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1657 org/lombokweb/asm/MethodWriter 1665 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1667 org/lombokweb/asm/MethodWriter 1675 org/lombokweb/asm/ClassReader 1682 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1684 org/lombokweb/asm/MethodWriter 1834 org/lombokweb/asm/ClassReader 1841 org/lombokweb/asm/ClassReader 1848 org/lombokweb/asm/ClassReader 1859 org/lombokweb/asm/ClassReader 1866 org/lombokweb/asm/Label 1876 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1938 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1940 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1948 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1950 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1958 org/lombokweb/asm/ClassReader 1965 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 1967 lombok/patcher/scripts/ReplaceMethodCallScript$ReplaceMethodCall 1975 org/lombokweb/asm/ClassReader 1982 org/lombokweb/asm/MethodWriter 1984 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 1992 org/lombokweb/asm/ClassReader 1999 org/lombokweb/asm/ClassReader 2006 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2008 org/lombokweb/asm/MethodWriter 2016 org/lombokweb/asm/ClassReader 2023 org/lombokweb/asm/ClassReader 2030 org/lombokweb/asm/ClassReader 2037 org/lombokweb/asm/ClassReader 2044 org/lombokweb/asm/ClassReader 2055 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2057 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 2072 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2074 org/lombokweb/asm/MethodWriter 2089 org/lombokweb/asm/ClassReader 2096 org/lombokweb/asm/ClassReader 2103 org/lombokweb/asm/ClassReader 2110 org/lombokweb/asm/ClassReader 2117 org/lombokweb/asm/ClassReader 2124 org/lombokweb/asm/ClassReader 2131 org/lombokweb/asm/ClassReader 2138 org/lombokweb/asm/Handle 2145 org/lombokweb/asm/ClassReader 2156 org/lombokweb/asm/ClassReader 2163 org/lombokweb/asm/ClassReader 2170 java/lang/String 2180 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2190 org/lombokweb/asm/ClassReader 2197 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2199 org/lombokweb/asm/MethodWriter 2207 lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues 2209 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2351 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2353 org/lombokweb/asm/MethodWriter 2398 org/lombokweb/asm/ClassReader 2409 org/lombokweb/asm/ClassReader 2416 org/lombokweb/asm/ClassReader 2423 org/lombokweb/asm/ClassReader 2430 org/lombokweb/asm/ClassReader 2437 org/lombokweb/asm/ClassReader 2473 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2475 org/lombokweb/asm/MethodWriter 2605 lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly 2607 org/lombokweb/asm/MethodWriter methods 0
ciMethodData org/lombokweb/asm/MethodWriter visitLabel (Lorg/lombokweb/asm/Label;)V 2 7293 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 121 0x180005 0x1c7d 0x0 0x0 0x0 0x0 0x0 0x250007 0x1a37 0x20 0x247 0x2e0007 0x1a37 0x100 0x0 0x350007 0x0 0x78 0x0 0x430007 0x0 0x20 0x0 0x680005 0x0 0x0 0x0 0x0 0x0 0x0 0x6f0007 0x0 0x40 0x0 0x7d0007 0x0 0x20 0x0 0xbf0002 0x0 0xc50003 0x0 0x178 0xcd0007 0x1a37 0x70 0x0 0xd40007 0x0 0x38 0x0 0xdc0003 0x0 0x120 0xea0003 0x0 0x108 0xf20007 0x1a37 0xb0 0x0 0xf90007 0x0 0x58 0x0 0x10e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1240007 0x0 0x20 0x0 0x1340003 0x0 0x58 0x13c0007 0x0 0x40 0x1a37 0x1430007 0xdb 0x20 0x195c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 6678 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x130002 0x1a16 0x1c0007 0x1a02 0x38 0x14 0x250003 0x14 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 6621 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x40007 0x0 0x58 0x19de 0x120005 0x0 0x0 0x284ea69b780 0x19de 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 7 lombok/patcher/PatchScript$FixedClassWriter methods 0
ciMethodData lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 2 6193 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x50005 0x1832 0x0 0x0 0x0 0x0 0x0 0x80007 0x27 0x20 0x180b 0xf0005 0x27 0x0 0x0 0x0 0x0 0x0 0x120007 0x27 0x20 0x0 0x190002 0x27 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 6122 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 149 0x80002 0x17ea 0x110005 0x0 0x0 0x284e4adccd8 0x17ea 0x0 0x0 0x180003 0x17ea 0x1e0 0x1d0005 0x0 0x0 0x284ea36c788 0xedd 0x0 0x0 0x220004 0x0 0x0 0x284ea36b928 0xedd 0x0 0x0 0x290005 0x0 0x0 0x284ea36b928 0xedd 0x0 0x0 0x2d0005 0xedd 0x0 0x0 0x0 0x0 0x0 0x300007 0xeda 0xe8 0x3 0x350005 0x0 0x0 0x284ea36b928 0x3 0x0 0x0 0x390005 0x3 0x0 0x0 0x0 0x0 0x0 0x3c0007 0x0 0x58 0x3 0x410005 0x0 0x0 0x284ea36c788 0x3 0x0 0x0 0x480005 0x0 0x0 0x284ea36c788 0x26c7 0x0 0x0 0x4d0007 0xedd 0xfffffffffffffe00 0x17ea 0x540005 0x0 0x0 0x284e4adccd8 0x17ea 0x0 0x0 0x5b0003 0x17ea 0x128 0x600005 0x0 0x0 0x284ea36c788 0x17fd 0x0 0x0 0x650004 0x0 0x0 0x284ea36c838 0x17fd 0x0 0x0 0x720005 0x0 0x0 0x284ea36c838 0x17fd 0x0 0x0 0x770007 0x17ec 0x68 0x11 0x880002 0x11 0x8b0005 0x2 0x0 0x284ea36c8e8 0x9 0x284ea36c998 0x6 0x930005 0x0 0x0 0x284ea36c788 0x2fd6 0x0 0x0 0x980007 0x17fc 0xfffffffffffffeb8 0x17da 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 14 5 java/util/ArrayList 15 java/util/ArrayList$Itr 22 lombok/patcher/Hook 29 lombok/patcher/Hook 47 lombok/patcher/Hook 65 java/util/ArrayList$Itr 72 java/util/ArrayList$Itr 83 java/util/ArrayList 93 java/util/ArrayList$Itr 100 lombok/patcher/MethodTarget 107 lombok/patcher/MethodTarget 120 lombok/patcher/scripts/ExitFromMethodEarlyScript$1 122 lombok/patcher/scripts/WrapReturnValuesScript$1 127 java/util/ArrayList$Itr methods 0
ciMethodData lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 1 16 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 118 0x10002 0x10 0x90007 0xe 0x38 0x2 0xd0003 0x2 0x18 0x150002 0x10 0x1a0005 0x0 0x0 0x284e4adccd8 0x10 0x0 0x0 0x230005 0x0 0x0 0x284ea36c788 0x10 0x0 0x0 0x280004 0x0 0x0 0x284e4ad9278 0x10 0x0 0x0 0x300002 0x10 0x390002 0x10 0x490002 0x10 0x520002 0x10 0x5b0002 0x10 0x600003 0x10 0x180 0x650005 0x0 0x0 0x284ea36c788 0x15 0x0 0x0 0x6a0004 0x0 0x0 0x284e4ad9278 0x15 0x0 0x0 0x710002 0x15 0x7a0002 0x15 0x7d0005 0x0 0x0 0x284e4adccd8 0x15 0x0 0x0 0x870002 0x15 0x8a0005 0x0 0x0 0x284e4adccd8 0x15 0x0 0x0 0x940002 0x15 0x970002 0x15 0x9a0005 0x0 0x0 0x284e4adccd8 0x15 0x0 0x0 0xa90005 0x0 0x0 0x284ea36c788 0x25 0x0 0x0 0xae0007 0x15 0xfffffffffffffe60 0x10 0xb40002 0x10 0xbd0002 0x10 0xc60002 0x10 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 9 14 java/util/ArrayList 21 java/util/ArrayList$Itr 28 java/lang/String 48 java/util/ArrayList$Itr 55 java/lang/String 66 java/util/ArrayList 75 java/util/ArrayList 86 java/util/ArrayList 93 java/util/ArrayList$Itr methods 0
ciMethodData lombok/patcher/Hook getMethodDescriptor ()Ljava/lang/String; 1 36 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 85 0x40002 0x24 0xb0005 0x24 0x0 0x0 0x0 0x0 0x0 0x130005 0x0 0x0 0x284eafce5b0 0x24 0x0 0x0 0x190003 0x24 0xd0 0x1d0005 0x0 0x0 0x284eafce660 0x32 0x0 0x0 0x220004 0x0 0x0 0x284e4ad9278 0x32 0x0 0x0 0x280002 0x32 0x2b0005 0x32 0x0 0x0 0x0 0x0 0x0 0x300005 0x0 0x0 0x284eafce660 0x56 0x0 0x0 0x350007 0x32 0xffffffffffffff10 0x24 0x3b0005 0x24 0x0 0x0 0x0 0x0 0x0 0x440002 0x24 0x470005 0x24 0x0 0x0 0x0 0x0 0x0 0x4c0005 0x24 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 4 12 java/util/Collections$UnmodifiableRandomAccessList 22 java/util/Collections$UnmodifiableCollection$1 29 java/lang/String 45 java/util/Collections$UnmodifiableCollection$1 methods 0
ciMethodData lombok/patcher/MethodTarget classMatches (Ljava/lang/String;)Z 1 32 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x50002 0x20 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData lombok/patcher/MethodTarget descriptorMatch (Ljava/lang/String;)Z 1 32 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 132 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 131 0x40007 0x1f 0x20 0x1 0xa0002 0x1f 0xd0005 0x0 0x0 0x284e4adccd8 0x1f 0x0 0x0 0x140005 0x0 0x0 0x284ea36c788 0x1f 0x0 0x0 0x190004 0x0 0x0 0x284e4ad9278 0x1f 0x0 0x0 0x200002 0x1f 0x230007 0x1b 0x20 0x4 0x2c0005 0x0 0x0 0x284eafce5b0 0x1b 0x0 0x0 0x320003 0x1b 0x128 0x360005 0x0 0x0 0x284ea36c788 0x20 0x0 0x0 0x3b0004 0x0 0x0 0x284e4ad9278 0x20 0x0 0x0 0x3f0005 0x0 0x0 0x284eafce660 0x20 0x0 0x0 0x440004 0x0 0x0 0x284e4ad9278 0x20 0x0 0x0 0x470002 0x20 0x4a0007 0x19 0x20 0x7 0x500005 0x0 0x0 0x284ea36c788 0x34 0x0 0x0 0x550007 0x12 0x78 0x22 0x590005 0x0 0x0 0x284eafce660 0x22 0x0 0x0 0x5e0007 0x20 0xfffffffffffffe60 0x2 0x620005 0x0 0x0 0x284ea36c788 0x14 0x0 0x0 0x670007 0x2 0x78 0x12 0x6b0005 0x0 0x0 0x284eafce660 0x12 0x0 0x0 0x700007 0x3 0x20 0xf 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 12 9 java/util/ArrayList 16 java/util/ArrayList$Itr 23 java/lang/String 36 java/util/Collections$UnmodifiableRandomAccessList 46 java/util/ArrayList$Itr 53 java/lang/String 60 java/util/Collections$UnmodifiableCollection$1 67 java/lang/String 80 java/util/ArrayList$Itr 91 java/util/Collections$UnmodifiableCollection$1 102 java/util/ArrayList$Itr 113 java/util/Collections$UnmodifiableCollection$1 methods 0
compile org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I -1 4 inline 157 0 -1 0 org/lombokweb/asm/ClassReader readMethod (Lorg/lombokweb/asm/ClassVisitor;Lorg/lombokweb/asm/Context;I)I 1 13 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 27 0 org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 2 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 19 0 org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 3 34 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 3 38 0 org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 4 157 0 java/lang/String <init> ([CII)V 5 7 0 java/lang/String rangeCheck ([CII)Ljava/lang/Void; 6 4 0 java/lang/String checkBoundsOffCount (III)I 7 6 0 jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 5 10 0 java/lang/String <init> ([CIILjava/lang/Void;)V 6 1 0 java/lang/Object <init> ()V 6 36 0 java/lang/StringUTF16 compress ([CII)[B 1 41 0 org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 2 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 19 0 org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 3 34 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 3 38 0 org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 4 157 0 java/lang/String <init> ([CII)V 5 7 0 java/lang/String rangeCheck ([CII)Ljava/lang/Void; 6 4 0 java/lang/String checkBoundsOffCount (III)I 7 6 0 jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 5 10 0 java/lang/String <init> ([CIILjava/lang/Void;)V 6 1 0 java/lang/Object <init> ()V 6 36 0 java/lang/StringUTF16 compress ([CII)[B 1 95 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 116 0 org/lombokweb/asm/ClassReader readUTF8 (I[C)Ljava/lang/String; 2 2 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 19 0 org/lombokweb/asm/ClassReader readUtf (I[C)Ljava/lang/String; 3 34 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 3 38 0 org/lombokweb/asm/ClassReader readUtf (II[C)Ljava/lang/String; 4 157 0 java/lang/String <init> ([CII)V 5 7 0 java/lang/String rangeCheck ([CII)Ljava/lang/Void; 6 4 0 java/lang/String checkBoundsOffCount (III)I 7 6 0 jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 5 10 0 java/lang/String <init> ([CIILjava/lang/Void;)V 6 1 0 java/lang/Object <init> ()V 6 36 0 java/lang/StringUTF16 compress ([CII)[B 1 126 0 org/lombokweb/asm/ClassReader readInt (I)I 1 139 0 java/lang/String equals (Ljava/lang/Object;)Z 1 179 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 523 0 lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 8 0 org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 3 18 0 org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 4 19 0 org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 5 3 0 org/lombokweb/asm/MethodVisitor <init> (I)V 6 3 0 org/lombokweb/asm/MethodVisitor <init> (ILorg/lombokweb/asm/MethodVisitor;)V 7 1 0 java/lang/Object <init> ()V 5 11 0 org/lombokweb/asm/ByteVector <init> ()V 6 1 0 java/lang/Object <init> ()V 5 26 0 java/lang/String equals (Ljava/lang/Object;)Z 5 46 0 org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 6 2 0 org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 7 4 0 java/lang/String hashCode ()I 8 17 0 java/lang/String isLatin1 ()Z 6 8 0 org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 6 37 0 java/lang/String equals (Ljava/lang/Object;)Z 6 88 0 org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 7 7 0 org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 8 1 0 java/lang/Object <init> ()V 5 61 0 org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 6 2 0 org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 7 4 0 java/lang/String hashCode ()I 8 17 0 java/lang/String isLatin1 ()Z 6 8 0 org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 6 37 0 java/lang/String equals (Ljava/lang/Object;)Z 6 88 0 org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 7 7 0 org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 8 1 0 java/lang/Object <init> ()V 5 144 0 org/lombokweb/asm/SymbolTable addConstantClass (Ljava/lang/String;)Lorg/lombokweb/asm/Symbol; 6 4 0 org/lombokweb/asm/SymbolTable addConstantUtf8Reference (ILjava/lang/String;)Lorg/lombokweb/asm/Symbol; 7 2 0 org/lombokweb/asm/SymbolTable hash (ILjava/lang/String;)I 8 4 0 java/lang/String hashCode ()I 9 17 0 java/lang/String isLatin1 ()Z 7 8 0 org/lombokweb/asm/SymbolTable get (I)Lorg/lombokweb/asm/SymbolTable$Entry; 7 42 0 java/lang/String equals (Ljava/lang/Object;)Z 7 94 0 org/lombokweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 8 7 0 org/lombokweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 9 1 0 java/lang/Object <init> ()V 5 183 0 org/lombokweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 6 6 0 java/lang/String charAt (I)C 7 1 0 java/lang/String isLatin1 ()Z 7 12 0 java/lang/StringLatin1 charAt ([BI)C 8 3 0 java/lang/String checkIndex (II)V 6 39 0 java/lang/String charAt (I)C 7 1 0 java/lang/String isLatin1 ()Z 7 12 0 java/lang/StringLatin1 charAt ([BI)C 8 3 0 java/lang/String checkIndex (II)V 6 58 0 java/lang/String charAt (I)C 7 1 0 java/lang/String isLatin1 ()Z 7 12 0 java/lang/StringLatin1 charAt ([BI)C 8 3 0 java/lang/String checkIndex (II)V 6 70 0 java/lang/String indexOf (II)I 7 1 0 java/lang/String isLatin1 ()Z 7 14 0 java/lang/String length ()I 8 6 0 java/lang/String coder ()B 7 17 0 java/lang/StringLatin1 indexOf ([BIII)I 8 1 0 java/lang/StringLatin1 canEncode (I)Z 8 33 0 java/lang/StringLatin1 indexOfChar ([BIII)I 6 89 0 java/lang/String charAt (I)C 7 1 0 java/lang/String isLatin1 ()Z 7 12 0 java/lang/StringLatin1 charAt ([BI)C 8 3 0 java/lang/String checkIndex (II)V 6 100 0 java/lang/String charAt (I)C 7 1 0 java/lang/String isLatin1 ()Z 7 12 0 java/lang/StringLatin1 charAt ([BI)C 8 3 0 java/lang/String checkIndex (II)V 5 217 0 org/lombokweb/asm/Label <init> ()V 6 1 0 java/lang/Object <init> ()V 2 17 0 java/util/ArrayList iterator ()Ljava/util/Iterator; 3 5 0 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 4 6 0 java/lang/Object <init> ()V 2 72 0 java/util/ArrayList$Itr hasNext ()Z 2 29 0 java/util/ArrayList$Itr next ()Ljava/lang/Object; 3 1 0 java/util/ArrayList$Itr checkForComodification ()V 2 41 0 lombok/patcher/Hook getMethodName ()Ljava/lang/String; 2 45 0 java/lang/String equals (Ljava/lang/Object;)Z 2 84 0 java/util/ArrayList iterator ()Ljava/util/Iterator; 3 5 0 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 4 6 0 java/lang/Object <init> ()V 2 147 0 java/util/ArrayList$Itr hasNext ()Z 2 96 0 java/util/ArrayList$Itr next ()Ljava/lang/Object; 3 1 0 java/util/ArrayList$Itr checkForComodification ()V 2 114 0 lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 3 5 0 java/lang/String equals (Ljava/lang/Object;)Z 2 136 0 lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 3 1 0 java/lang/Object <init> ()V 3 48 0 lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 3 57 0 lombok/patcher/MethodLogistics returnOpcodeFor (Ljava/lang/String;)I 3 73 0 java/util/ArrayList <init> ()V 4 1 0 java/util/AbstractList <init> ()V 5 1 0 java/util/AbstractCollection <init> ()V 6 1 0 java/lang/Object <init> ()V 3 82 0 java/util/ArrayList <init> ()V 4 1 0 java/util/AbstractList <init> ()V 5 1 0 java/util/AbstractCollection <init> ()V 6 1 0 java/lang/Object <init> ()V 3 91 0 java/util/ArrayList <init> ()V 4 1 0 java/util/AbstractList <init> ()V 5 1 0 java/util/AbstractCollection <init> ()V 6 1 0 java/lang/Object <init> ()V 3 113 0 lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 3 122 0 java/lang/Integer valueOf (I)Ljava/lang/Integer; 3 135 0 java/lang/Integer valueOf (I)Ljava/lang/Integer; 4 28 0 java/lang/Integer <init> (I)V 5 1 0 java/lang/Number <init> ()V 6 1 0 java/lang/Object <init> ()V 3 148 0 lombok/patcher/MethodLogistics loadOpcodeFor (Ljava/lang/String;)I 3 151 0 java/lang/Integer valueOf (I)Ljava/lang/Integer; 1 576 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 583 0 org/lombokweb/asm/MethodWriter canCopyMethodAttributes (Lorg/lombokweb/asm/ClassReader;ZZIII)Z 2 5 0 org/lombokweb/asm/SymbolTable getSource ()Lorg/lombokweb/asm/ClassReader; 2 55 0 org/lombokweb/asm/SymbolTable getMajorVersion ()I 2 106 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 2 137 0 org/lombokweb/asm/ClassReader readUnsignedShort (I)I 1 596 0 org/lombokweb/asm/MethodWriter setMethodAttributesSource (II)V
