# 🚀 دليل البدء السريع - VPN Gaming

## 📋 المتطلبات الأساسية

### 🛠 أدوات التطوير
- **Android Studio** (Arctic Fox أو أحدث)
- **Java JDK** 8 أو أحدث
- **Android SDK** (API Level 24 أو أحدث)
- **Git** (اختياري)

### 📱 للاختبار
- جهاز Android (API 24+) أو محاكي
- تفعيل "خيارات المطور" و "USB Debugging"

## ⚡ خطوات البدء السريع

### 1️⃣ فتح المشروع
```bash
# إذا كان لديك Android Studio
1. افتح Android Studio
2. اختر "Open an existing project"
3. حدد مجلد المشروع
4. انتظر حتى ينتهي Gradle من التحميل
```

### 2️⃣ بناء المشروع
```bash
# في Terminal داخل Android Studio أو Command Prompt
./gradlew build

# أو في Windows
gradlew.bat build
```

### 3️⃣ تشغيل التطبيق
```bash
# الطريقة الأولى: من Android Studio
1. اربط جهاز Android أو شغل المحاكي
2. اضغط على زر "Run" (▶️)

# الطريقة الثانية: من Terminal
./gradlew installDebug
```

## 🔧 إعدادات سريعة

### تغيير اسم التطبيق
```xml
<!-- في app/src/main/res/values/strings.xml -->
<string name="app_name">اسم التطبيق الجديد</string>
```

### تغيير معرف التطبيق
```kotlin
// في app/build.gradle.kts
android {
    defaultConfig {
        applicationId = "com.yourcompany.yourapp"
    }
}
```

### إضافة خوادم جديدة
```kotlin
// في VpnRepository.kt - دالة getDefaultServersList()
VpnServer(
    id = "new-server-1",
    name = "خادم جديد",
    country = "البلد",
    serverAddress = "server.example.com",
    port = 1194,
    protocol = VpnProtocol.OPENVPN_UDP
)
```

## 🎨 تخصيص الألوان

```xml
<!-- في app/src/main/res/values/colors.xml -->
<color name="primary_blue">#FF1976D2</color>
<color name="secondary_green">#FF4CAF50</color>
<color name="accent_orange">#FFFF9800</color>
```

## 🧪 اختبار سريع

### اختبار البناء
```bash
./gradlew assembleDebug
```

### اختبار الوحدة
```bash
./gradlew test
```

### اختبار على الجهاز
```bash
./gradlew connectedAndroidTest
```

## 🐛 حل المشاكل الشائعة

### مشكلة: Gradle Build Failed
```bash
# تنظيف المشروع
./gradlew clean

# إعادة البناء
./gradlew build
```

### مشكلة: SDK غير موجود
```bash
# في Android Studio
1. File → Project Structure
2. SDK Location
3. تأكد من مسار Android SDK
```

### مشكلة: Java Version
```bash
# تأكد من إصدار Java
java -version

# يجب أن يكون Java 8 أو أحدث
```

### مشكلة: VPN Permission
```kotlin
// التأكد من الأذونات في AndroidManifest.xml
<uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
<uses-permission android:name="android.permission.INTERNET" />
```

## 📱 اختبار الميزات

### 1. اختبار الاتصال
- افتح التطبيق
- اضغط على "اتصال"
- امنح إذن VPN عند الطلب
- تحقق من تغيير حالة الاتصال

### 2. اختبار قائمة الخوادم
- اضغط على أيقونة القائمة
- تصفح الخوادم المتاحة
- اختر خادم واختبر الاتصال

### 3. اختبار الإشعارات
- اتصل بـ VPN
- تحقق من ظهور إشعار في شريط الحالة
- اضغط على الإشعار للعودة للتطبيق

## 🔄 تحديث المشروع

### تحديث التبعيات
```kotlin
// في app/build.gradle.kts
// تحديث إصدارات المكتبات حسب الحاجة
implementation("androidx.compose:compose-bom:2023.10.01")
```

### تحديث Gradle
```bash
# في gradle/wrapper/gradle-wrapper.properties
distributionUrl=https\://services.gradle.org/distributions/gradle-8.2-bin.zip
```

## 📚 موارد إضافية

### الوثائق
- [Android VPN Service](https://developer.android.com/develop/connectivity/vpn)
- [Jetpack Compose](https://developer.android.com/jetpack/compose)
- [Hilt Dependency Injection](https://developer.android.com/training/dependency-injection/hilt-android)

### أدوات مفيدة
- [Android Studio](https://developer.android.com/studio)
- [Gradle](https://gradle.org/)
- [Kotlin](https://kotlinlang.org/)

## 💡 نصائح للتطوير

### 1. استخدام Logcat
```kotlin
// إضافة logs للتتبع
Log.d("VPN", "Connection state: $connectionState")
```

### 2. تصحيح الأخطاء
```kotlin
// استخدام breakpoints في Android Studio
// تفعيل USB Debugging على الجهاز
```

### 3. مراقبة الأداء
```bash
# استخدام Android Profiler في Android Studio
# مراقبة استهلاك الذاكرة والمعالج
```

## 🆘 الحصول على المساعدة

### المشاكل الشائعة
1. تحقق من ملف README.md
2. راجع ملفات الـ logs
3. تأكد من الأذونات المطلوبة

### التواصل
- افتح Issue في GitHub
- راجع الوثائق الرسمية لـ Android
- تحقق من Stack Overflow

---

**ملاحظة**: هذا التطبيق مخصص للأغراض التعليمية. تأكد من الامتثال للقوانين المحلية عند استخدام تقنيات VPN.
