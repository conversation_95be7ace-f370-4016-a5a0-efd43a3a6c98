package com.vpngaming.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.vpngaming.app.data.model.VpnConnectionState
import com.vpngaming.app.data.model.VpnServer
import com.vpngaming.app.data.repository.VpnRepository
import com.vpngaming.app.vpn.VpnManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel الرئيسي لإدارة حالة VPN
 */
@HiltViewModel
class VpnViewModel @Inject constructor(
    private val vpnManager: VpnManager,
    private val vpnRepository: VpnRepository
) : ViewModel() {

    // حالة الاتصال
    val connectionState: StateFlow<VpnConnectionState> = vpnManager.connectionState

    // الخادم الحالي
    val currentServer: StateFlow<VpnServer?> = vpnManager.currentServer

    // قائمة الخوادم
    private val _servers = MutableStateFlow<List<VpnServer>>(emptyList())
    val servers: StateFlow<List<VpnServer>> = _servers.asStateFlow()

    // حالة التحميل
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // رسائل الخطأ
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // البحث
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // الخوادم المفلترة
    val filteredServers: StateFlow<List<VpnServer>> = combine(
        servers,
        searchQuery
    ) { serverList, query ->
        if (query.isBlank()) {
            serverList
        } else {
            serverList.filter { server ->
                server.name.contains(query, ignoreCase = true) ||
                server.country.contains(query, ignoreCase = true) ||
                server.city.contains(query, ignoreCase = true)
            }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )

    init {
        loadServers()
        loadDefaultServers()
    }

    /**
     * تحميل الخوادم
     */
    private fun loadServers() {
        viewModelScope.launch {
            vpnRepository.getOnlineServers().collect { serverList ->
                _servers.value = serverList
            }
        }
    }

    /**
     * تحميل الخوادم الافتراضية
     */
    private fun loadDefaultServers() {
        viewModelScope.launch {
            try {
                vpnRepository.loadDefaultServers()
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في تحميل الخوادم: ${e.message}"
            }
        }
    }

    /**
     * الاتصال بخادم VPN
     */
    fun connect(server: VpnServer) {
        viewModelScope.launch {
            try {
                _errorMessage.value = null
                vpnManager.connect(server)
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في الاتصال: ${e.message}"
            }
        }
    }

    /**
     * الاتصال بأفضل خادم متاح
     */
    fun connectToBestServer() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val bestServer = vpnRepository.getBestAvailableServer()
                if (bestServer != null) {
                    connect(bestServer)
                } else {
                    _errorMessage.value = "لا توجد خوادم متاحة"
                }
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في العثور على أفضل خادم: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * قطع اتصال VPN
     */
    fun disconnect() {
        viewModelScope.launch {
            try {
                _errorMessage.value = null
                vpnManager.disconnect()
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في قطع الاتصال: ${e.message}"
            }
        }
    }

    /**
     * إعادة الاتصال
     */
    fun reconnect() {
        viewModelScope.launch {
            try {
                _errorMessage.value = null
                vpnManager.reconnect()
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في إعادة الاتصال: ${e.message}"
            }
        }
    }

    /**
     * تحديث استعلام البحث
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    /**
     * مسح رسالة الخطأ
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * تحديث ping الخادم
     */
    fun updateServerPing(serverId: String, ping: Int) {
        viewModelScope.launch {
            try {
                vpnRepository.updateServerPing(serverId, ping)
            } catch (e: Exception) {
                // تجاهل الأخطاء في تحديث ping
            }
        }
    }

    /**
     * تحديث حالة الخادم
     */
    fun updateServerStatus(serverId: String, isOnline: Boolean) {
        viewModelScope.launch {
            try {
                vpnRepository.updateServerStatus(serverId, isOnline)
            } catch (e: Exception) {
                // تجاهل الأخطاء في تحديث الحالة
            }
        }
    }

    /**
     * التحقق من حالة الاتصال
     */
    fun isConnected(): Boolean = vpnManager.isConnected()

    /**
     * التحقق من حالة الاتصال
     */
    fun isConnecting(): Boolean = vpnManager.isConnecting()

    override fun onCleared() {
        super.onCleared()
        vpnManager.cleanup()
    }
}
