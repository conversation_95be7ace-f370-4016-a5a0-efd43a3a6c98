package com.vpngaming.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * نموذج بيانات خادم VPN
 */
@Entity(tableName = "vpn_servers")
@Parcelize
data class VpnServer(
    @PrimaryKey
    val id: String,
    val name: String,
    val country: String,
    val countryCode: String,
    val city: String,
    val serverAddress: String,
    val port: Int,
    val protocol: VpnProtocol,
    val isOnline: Boolean = true,
    val isPremium: Boolean = false,
    val ping: Int = 0,
    val load: Int = 0, // نسبة الحمولة من 0-100
    val flagUrl: String? = null,
    val username: String? = null,
    val password: String? = null,
    val ovpnConfig: String? = null, // إعدادات OpenVPN
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable

/**
 * أنواع بروتوكولات VPN المدعومة
 */
enum class VpnProtocol {
    OPENVPN_UDP,
    OPENVPN_TCP,
    IKEV2,
    WIREGUARD,
    SSTP,
    L2TP_IPSEC
}

/**
 * حالات اتصال VPN
 */
enum class VpnConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    RECONNECTING,
    ERROR
}

/**
 * إحصائيات الاتصال
 */
@Parcelize
data class ConnectionStats(
    val bytesIn: Long = 0,
    val bytesOut: Long = 0,
    val packetsIn: Long = 0,
    val packetsOut: Long = 0,
    val connectionTime: Long = 0,
    val lastPing: Int = 0
) : Parcelable

/**
 * معلومات الاتصال الحالي
 */
@Parcelize
data class VpnConnection(
    val server: VpnServer?,
    val state: VpnConnectionState,
    val stats: ConnectionStats,
    val connectedAt: Long = 0,
    val lastError: String? = null
) : Parcelable
