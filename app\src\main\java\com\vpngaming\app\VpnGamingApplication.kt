package com.vpngaming.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import dagger.hilt.android.HiltAndroidApp

/**
 * فئة التطبيق الرئيسية
 * تحتوي على إعدادات Hilt وإنشاء قنوات الإشعارات
 */
@HiltAndroidApp
class VpnGamingApplication : Application() {

    companion object {
        const val VPN_NOTIFICATION_CHANNEL_ID = "vpn_service_channel"
        const val VPN_NOTIFICATION_ID = 1001
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannels()
    }

    /**
     * إنشاء قنوات الإشعارات المطلوبة للتطبيق
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)
            
            // قناة إشعارات خدمة VPN
            val vpnChannel = NotificationChannel(
                VPN_NOTIFICATION_CHANNEL_ID,
                getString(R.string.vpn_notification_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.vpn_notification_channel_description)
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }
            
            notificationManager.createNotificationChannel(vpnChannel)
        }
    }
}
