package com.vpngaming.app.data.repository

import com.vpngaming.app.data.database.VpnDao
import com.vpngaming.app.data.model.VpnProtocol
import com.vpngaming.app.data.model.VpnServer
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مستودع بيانات VPN
 */
@Singleton
class VpnRepository @Inject constructor(
    private val vpnDao: VpnDao
) {

    /**
     * الحصول على جميع الخوادم
     */
    fun getAllServers(): Flow<List<VpnServer>> = vpnDao.getAllServers()

    /**
     * الحصول على الخوادم المتاحة
     */
    fun getOnlineServers(): Flow<List<VpnServer>> = vpnDao.getOnlineServers()

    /**
     * الحصول على الخوادم المجانية
     */
    fun getFreeServers(): Flow<List<VpnServer>> = vpnDao.getFreeServers()

    /**
     * الحصول على أسرع الخوادم
     */
    fun getFastestServers(): Flow<List<VpnServer>> = vpnDao.getFastestServers()

    /**
     * البحث في الخوادم
     */
    fun searchServers(query: String): Flow<List<VpnServer>> = vpnDao.searchServers(query)

    /**
     * الحصول على خادم بالمعرف
     */
    suspend fun getServerById(serverId: String): VpnServer? = vpnDao.getServerById(serverId)

    /**
     * إضافة خادم جديد
     */
    suspend fun insertServer(server: VpnServer) = vpnDao.insertServer(server)

    /**
     * تحديث خادم
     */
    suspend fun updateServer(server: VpnServer) = vpnDao.updateServer(server)

    /**
     * حذف خادم
     */
    suspend fun deleteServer(server: VpnServer) = vpnDao.deleteServer(server)

    /**
     * تحديث حالة الخادم
     */
    suspend fun updateServerStatus(serverId: String, isOnline: Boolean) {
        vpnDao.updateServerStatus(serverId, isOnline)
    }

    /**
     * تحديث ping الخادم
     */
    suspend fun updateServerPing(serverId: String, ping: Int) {
        vpnDao.updateServerPing(serverId, ping)
    }

    /**
     * الحصول على عدد الخوادم المتاحة
     */
    suspend fun getOnlineServersCount(): Int = vpnDao.getOnlineServersCount()

    /**
     * تحميل الخوادم الافتراضية
     */
    suspend fun loadDefaultServers() {
        val currentCount = vpnDao.getServersCount()
        if (currentCount == 0) {
            val defaultServers = getDefaultServersList()
            vpnDao.insertServers(defaultServers)
        }
    }

    /**
     * الحصول على أفضل خادم متاح
     */
    suspend fun getBestAvailableServer(): VpnServer? {
        val fastestServers = getFastestServers().first()
        return fastestServers.firstOrNull()
    }

    /**
     * قائمة الخوادم الافتراضية
     */
    private fun getDefaultServersList(): List<VpnServer> {
        return listOf(
            VpnServer(
                id = "us-east-1",
                name = "الولايات المتحدة - نيويورك",
                country = "الولايات المتحدة",
                countryCode = "US",
                city = "نيويورك",
                serverAddress = "us-east.vpngaming.com",
                port = 1194,
                protocol = VpnProtocol.OPENVPN_UDP,
                isOnline = true,
                isPremium = false,
                ping = 45,
                load = 25
            ),
            VpnServer(
                id = "us-west-1",
                name = "الولايات المتحدة - لوس أنجلوس",
                country = "الولايات المتحدة",
                countryCode = "US",
                city = "لوس أنجلوس",
                serverAddress = "us-west.vpngaming.com",
                port = 1194,
                protocol = VpnProtocol.OPENVPN_UDP,
                isOnline = true,
                isPremium = false,
                ping = 38,
                load = 15
            ),
            VpnServer(
                id = "uk-london-1",
                name = "المملكة المتحدة - لندن",
                country = "المملكة المتحدة",
                countryCode = "GB",
                city = "لندن",
                serverAddress = "uk-london.vpngaming.com",
                port = 1194,
                protocol = VpnProtocol.OPENVPN_UDP,
                isOnline = true,
                isPremium = false,
                ping = 28,
                load = 35
            ),
            VpnServer(
                id = "de-frankfurt-1",
                name = "ألمانيا - فرانكفورت",
                country = "ألمانيا",
                countryCode = "DE",
                city = "فرانكفورت",
                serverAddress = "de-frankfurt.vpngaming.com",
                port = 1194,
                protocol = VpnProtocol.OPENVPN_UDP,
                isOnline = true,
                isPremium = false,
                ping = 32,
                load = 20
            ),
            VpnServer(
                id = "jp-tokyo-1",
                name = "اليابان - طوكيو",
                country = "اليابان",
                countryCode = "JP",
                city = "طوكيو",
                serverAddress = "jp-tokyo.vpngaming.com",
                port = 1194,
                protocol = VpnProtocol.OPENVPN_UDP,
                isOnline = true,
                isPremium = true,
                ping = 85,
                load = 40
            ),
            VpnServer(
                id = "sg-singapore-1",
                name = "سنغافورة",
                country = "سنغافورة",
                countryCode = "SG",
                city = "سنغافورة",
                serverAddress = "sg-singapore.vpngaming.com",
                port = 1194,
                protocol = VpnProtocol.OPENVPN_UDP,
                isOnline = true,
                isPremium = true,
                ping = 95,
                load = 30
            )
        )
    }
}
