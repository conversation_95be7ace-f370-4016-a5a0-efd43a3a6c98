package com.vpngaming.app.di

import android.content.Context
import androidx.room.Room
import com.vpngaming.app.data.database.VpnDao
import com.vpngaming.app.data.database.VpnDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * وحدة Hilt لقاعدة البيانات
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideVpnDatabase(@ApplicationContext context: Context): VpnDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            VpnDatabase::class.java,
            "vpn_gaming_database"
        )
            .fallbackToDestructiveMigration()
            .build()
    }

    @Provides
    fun provideVpnDao(database: VpnDatabase): VpnDao {
        return database.vpnDao()
    }
}
