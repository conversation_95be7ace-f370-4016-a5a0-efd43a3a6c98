package com.vpngaming.app.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vpngaming.app.R
import com.vpngaming.app.data.model.VpnConnectionState
import com.vpngaming.app.data.model.VpnServer
import com.vpngaming.app.ui.viewmodel.VpnViewModel

/**
 * الشاشة الرئيسية للتطبيق
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    vpnViewModel: VpnViewModel,
    onConnectRequest: (VpnServer?) -> Unit,
    onDisconnectRequest: () -> Unit
) {
    val connectionState by vpnViewModel.connectionState.collectAsState()
    val currentServer by vpnViewModel.currentServer.collectAsState()
    val servers by vpnViewModel.filteredServers.collectAsState()
    val isLoading by vpnViewModel.isLoading.collectAsState()
    val errorMessage by vpnViewModel.errorMessage.collectAsState()

    var showServerList by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // شريط التطبيق العلوي
        TopAppBar(
            title = {
                Text(
                    text = stringResource(R.string.app_name),
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                IconButton(onClick = { showServerList = !showServerList }) {
                    Icon(
                        imageVector = if (showServerList) Icons.Default.Home else Icons.Default.List,
                        contentDescription = if (showServerList) "الرئيسية" else "قائمة الخوادم"
                    )
                }
            }
        )

        Spacer(modifier = Modifier.height(16.dp))

        if (showServerList) {
            // عرض قائمة الخوادم
            ServerListContent(
                servers = servers,
                currentServer = currentServer,
                onServerSelected = { server ->
                    onConnectRequest(server)
                    showServerList = false
                }
            )
        } else {
            // عرض شاشة الاتصال الرئيسية
            ConnectionContent(
                connectionState = connectionState,
                currentServer = currentServer,
                isLoading = isLoading,
                onConnectClick = { onConnectRequest(null) },
                onDisconnectClick = onDisconnectRequest,
                onSelectServerClick = { showServerList = true }
            )
        }

        // عرض رسائل الخطأ
        errorMessage?.let { message ->
            LaunchedEffect(message) {
                // يمكن عرض Snackbar هنا
            }
        }
    }
}

/**
 * محتوى شاشة الاتصال الرئيسية
 */
@Composable
private fun ConnectionContent(
    connectionState: VpnConnectionState,
    currentServer: VpnServer?,
    isLoading: Boolean,
    onConnectClick: () -> Unit,
    onDisconnectClick: () -> Unit,
    onSelectServerClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // حالة الاتصال
        ConnectionStatusCard(
            connectionState = connectionState,
            currentServer = currentServer
        )

        Spacer(modifier = Modifier.height(32.dp))

        // زر الاتصال الرئيسي
        ConnectionButton(
            connectionState = connectionState,
            isLoading = isLoading,
            onConnectClick = onConnectClick,
            onDisconnectClick = onDisconnectClick
        )

        Spacer(modifier = Modifier.height(24.dp))

        // زر اختيار الخادم
        OutlinedButton(
            onClick = onSelectServerClick,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.LocationOn,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(stringResource(R.string.select_server))
        }
    }
}

/**
 * بطاقة حالة الاتصال
 */
@Composable
private fun ConnectionStatusCard(
    connectionState: VpnConnectionState,
    currentServer: VpnServer?
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // أيقونة الحالة
            Icon(
                imageVector = when (connectionState) {
                    VpnConnectionState.CONNECTED -> Icons.Default.Shield
                    VpnConnectionState.CONNECTING -> Icons.Default.Sync
                    VpnConnectionState.DISCONNECTING -> Icons.Default.SyncDisabled
                    VpnConnectionState.ERROR -> Icons.Default.Error
                    else -> Icons.Default.ShieldOutlined
                },
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = when (connectionState) {
                    VpnConnectionState.CONNECTED -> Color(0xFF4CAF50)
                    VpnConnectionState.CONNECTING -> Color(0xFFFF9800)
                    VpnConnectionState.ERROR -> Color(0xFFF44336)
                    else -> Color.Gray
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // نص الحالة
            Text(
                text = when (connectionState) {
                    VpnConnectionState.CONNECTED -> stringResource(R.string.connected)
                    VpnConnectionState.CONNECTING -> stringResource(R.string.connecting)
                    VpnConnectionState.DISCONNECTING -> "جاري قطع الاتصال..."
                    VpnConnectionState.ERROR -> stringResource(R.string.connection_failed)
                    else -> stringResource(R.string.disconnected)
                },
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )

            // معلومات الخادم
            currentServer?.let { server ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = server.name,
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * زر الاتصال الرئيسي
 */
@Composable
private fun ConnectionButton(
    connectionState: VpnConnectionState,
    isLoading: Boolean,
    onConnectClick: () -> Unit,
    onDisconnectClick: () -> Unit
) {
    val isConnected = connectionState == VpnConnectionState.CONNECTED
    val isConnecting = connectionState == VpnConnectionState.CONNECTING

    Button(
        onClick = {
            if (isConnected) {
                onDisconnectClick()
            } else {
                onConnectClick()
            }
        },
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp),
        enabled = !isLoading && !isConnecting,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isConnected) {
                Color(0xFFF44336)
            } else {
                Color(0xFF4CAF50)
            }
        )
    ) {
        if (isLoading || isConnecting) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = Color.White,
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(8.dp))
        }

        Text(
            text = if (isConnected) {
                stringResource(R.string.disconnect)
            } else {
                stringResource(R.string.connect)
            },
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * محتوى قائمة الخوادم
 */
@Composable
private fun ServerListContent(
    servers: List<VpnServer>,
    currentServer: VpnServer?,
    onServerSelected: (VpnServer) -> Unit
) {
    LazyColumn {
        items(servers) { server ->
            ServerItem(
                server = server,
                isSelected = server.id == currentServer?.id,
                onServerClick = { onServerSelected(server) }
            )
        }
    }
}

/**
 * عنصر خادم في القائمة
 */
@Composable
private fun ServerItem(
    server: VpnServer,
    isSelected: Boolean,
    onServerClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        onClick = onServerClick,
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // أيقونة البلد (يمكن استبدالها بعلم البلد)
            Icon(
                imageVector = Icons.Default.LocationOn,
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // معلومات الخادم
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = server.name,
                    fontWeight = FontWeight.Medium,
                    fontSize = 16.sp
                )
                Text(
                    text = "${server.country} • ${server.city}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // معلومات إضافية
            Column(horizontalAlignment = Alignment.End) {
                Text(
                    text = "${server.ping}ms",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                if (server.isPremium) {
                    Text(
                        text = "Premium",
                        fontSize = 10.sp,
                        color = Color(0xFFFF9800)
                    )
                }
            }
        }
    }
}
