#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=2204, tid=7612
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6a2084654daf15e3f33836b0104d5d77-sock

Host: Intel(R) Core(TM)2 Quad  CPU   Q9300  @ 2.50GHz, 4 cores, 3G,  Windows 10 , 64 bit Build 16299 (10.0.16299.15)
Time: Sun May 25 14:14:02 2025 Central Europe Daylight Time elapsed time: 8.869634 seconds (0d 0h 0m 8s)

---------------  T H R E A D  ---------------

Current thread (0x0000018279a15490):  JavaThread "Worker-0: Updating Maven Dependencies"        [_thread_in_vm, id=7612, stack(0x0000004377f00000,0x0000004378000000) (1024K)]

Stack: [0x0000004377f00000,0x0000004378000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0x8a411e]
V  [jvm.dll+0x670385]
V  [jvm.dll+0x1e472c]
V  [jvm.dll+0x1e44fe]
V  [jvm.dll+0x672c82]
V  [jvm.dll+0x672aa2]
V  [jvm.dll+0x670d5e]
V  [jvm.dll+0x26a6e6]
V  [jvm.dll+0x216107]
V  [jvm.dll+0x20bb8e]
V  [jvm.dll+0x5ae3ec]
V  [jvm.dll+0x821736]
V  [jvm.dll+0x471736]
V  [jvm.dll+0x477608]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 1060  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.6 (0 bytes) @ 0x0000018228cf0811 [0x0000018228cf0740+0x00000000000000d1]
J 2348 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@21.0.6 (43 bytes) @ 0x0000018221600dfc [0x0000018221600aa0+0x000000000000035c]
J 3119 c1 org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult; (67 bytes) @ 0x00000182217e5d3c [0x00000182217e5a20+0x000000000000031c]
J 2452 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;Lorg/eclipse/osgi/storage/bundlefile/BundleEntry;Ljava/util/List;)Ljava/lang/Class; (636 bytes) @ 0x000001822163d0f4 [0x000001822163b640+0x0000000000001ab4]
J 2924 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(Ljava/lang/String;Lorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;Ljava/util/List;)Ljava/lang/Class; (343 bytes) @ 0x0000018221768094 [0x0000018221764d80+0x0000000000003314]
J 2923 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(Ljava/lang/String;[Lorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;Ljava/util/List;)Ljava/lang/Class; (55 bytes) @ 0x00000182217635e4 [0x0000018221763460+0x0000000000000184]
J 2429 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(Ljava/lang/String;Ljava/util/List;)Ljava/lang/Class; (132 bytes) @ 0x0000018221633914 [0x00000182216333e0+0x0000000000000534]
J 2809 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(Ljava/lang/String;)Ljava/lang/Class; (210 bytes) @ 0x000001822172f9a4 [0x000001822172ece0+0x0000000000000cc4]
J 2424 c1 org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(Ljava/lang/String;)Ljava/lang/Class; (166 bytes) @ 0x000001822162feec [0x000001822162f340+0x0000000000000bac]
J 2921 c1 org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(Ljava/lang/String;)Ljava/lang/Class; (9 bytes) @ 0x000001822176084c [0x0000018221760740+0x000000000000010c]
j  org.eclipse.osgi.internal.loader.BundleLoader.findClass0(Ljava/lang/String;ZZ)Ljava/lang/Class;+320
J 2382 c1 org.eclipse.osgi.internal.loader.BundleLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; (8 bytes) @ 0x0000018221614dcc [0x0000018221614d40+0x000000000000008c]
J 2386 c1 org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; (36 bytes) @ 0x000001822161e34c [0x000001822161e120+0x000000000000022c]
J 1113 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.6 (7 bytes) @ 0x00000182213735d4 [0x00000182213734c0+0x0000000000000114]
v  ~StubRoutines::call_stub 0x00000182286e1015
j  org.eclipse.m2e.core.internal.embedder.MavenExecutionContext.createExecutionRequest(Lorg/eclipse/m2e/core/embedder/IMavenConfiguration;Lorg/eclipse/m2e/core/embedder/IComponentLookup;Lorg/eclipse/m2e/core/embedder/MavenSettingsLocations;Ljava/io/File;)Lorg/apache/maven/execution/MavenExecutionRequest;+0
j  org.eclipse.m2e.core.internal.embedder.MavenExecutionContext.newExecutionRequest()Lorg/apache/maven/execution/MavenExecutionRequest;+153
j  org.eclipse.m2e.core.internal.embedder.MavenExecutionContext.execute(Lorg/apache/maven/project/MavenProject;Lorg/eclipse/m2e/core/embedder/ICallable;Lorg/eclipse/core/runtime/IProgressMonitor;)Ljava/lang/Object;+131
j  org.eclipse.m2e.core.internal.embedder.MavenExecutionContext.execute(Lorg/eclipse/m2e/core/embedder/ICallable;Lorg/eclipse/core/runtime/IProgressMonitor;)Ljava/lang/Object;+4
j  org.eclipse.m2e.core.internal.project.registry.ProjectRegistryRefreshJob.run(Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/runtime/IStatus;+80
j  org.eclipse.core.internal.jobs.Worker.run()V+32
v  ~StubRoutines::call_stub 0x00000182286e1015

Compiled method (n/a) 9180 1060     n 0       java.lang.ClassLoader::defineClass1 (native)
 total in heap  [0x0000018228cf0590,0x0000018228cf0a08] = 1144
 relocation     [0x0000018228cf06f0,0x0000018228cf0728] = 56
 main code      [0x0000018228cf0740,0x0000018228cf0a07] = 711
 stub code      [0x0000018228cf0a07,0x0000018228cf0a08] = 1

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000018233430ff0} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # parm0:    rdx:rdx   = 'java/lang/ClassLoader'
  # parm1:    r8:r8     = 'java/lang/String'
  # parm2:    r9:r9     = '[B'
  # parm3:    rdi       = int
  # parm4:    rsi       = int
  # parm5:    rcx:rcx   = 'java/security/ProtectionDomain'
  # parm6:    [sp+0xa0]   = 'java/lang/String'  (sp of caller)
  0x0000018228cf0740: 448b 5208 | 49bb 0000 | 0033 8201 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x0000018228cf0758: ;   {runtime_call ic_miss_stub}
  0x0000018228cf0758: 0000 e921 | dda3 ff90 
[Verified Entry Point]
  0x0000018228cf0760: 8984 2400 | 80ff ff55 | 488b ec48 | 81ec 9000 | 0000 6690 | 4181 7f20 | 0200 0000 

  0x0000018228cf077c: ;   {runtime_call StubRoutines (final stubs)}
  0x0000018228cf077c: 7405 e83d | 82a2 ff48 | 837d 1000 | 488d 4510 | 480f 4445 | 1048 8944 | 2440 4889 | 4c24 7048 
  0x0000018228cf079c: 83f9 0048 | 8d44 2470 | 480f 4444 | 2470 4889 | 4424 3848 | 8974 2430 | 4889 7c24 | 284c 894c 
  0x0000018228cf07bc: 2458 4983 | f900 488d | 4424 5848 | 0f44 4424 | 5848 8944 | 2420 4c89 | 4424 5049 | 83f8 004c 
  0x0000018228cf07dc: 8d4c 2450 | 4c0f 444c | 2450 4889 | 5424 4848 | 83fa 004c | 8d44 2448 | 4c0f 4444 

  0x0000018228cf07f8: ;   {oop(a 'java/lang/Class'{0x00000000c0000140} = 'java/lang/ClassLoader')}
  0x0000018228cf07f8: 2448 49be | 4001 00c0 | 0000 0000 | 4c89 7424 | 784c 8d74 | 2478 498b 

  0x0000018228cf0810: ; ImmutableOopMap {[72]=Oop [80]=Oop [88]=Oop [112]=Oop [120]=Oop [160]=Oop }
                      ;   {section_word}
  0x0000018228cf0810: d649 ba11 | 08cf 2882 | 0100 004d | 8997 a003 | 0000 4989 | a798 0300 

  0x0000018228cf0828: ;   {external_word}
  0x0000018228cf0828: 0049 ba35 | 5b45 80fb | 7f00 0041 | 803a 000f | 844e 0000 | 0052 4150 

  0x0000018228cf0840: ;   {metadata({method} {0x0000018233430ff0} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018228cf0840: 4151 48ba | e80f 4333 | 8201 0000 | 498b cf48 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec 
  0x0000018228cf0860: ;   {runtime_call}
  0x0000018228cf0860: 0848 b8c0 | 4bef 7ffb | 7f00 00ff | d048 83c4 | 08e9 0c00 

  0x0000018228cf0874: ;   {runtime_call}
  0x0000018228cf0874: 0000 48b8 | c04b ef7f | fb7f 0000 | ffd0 4883 | c420 4159 | 4158 5a49 | 8d8f b803 | 0000 41c7 
  0x0000018228cf0894: 8744 0400 | 0004 0000 

  0x0000018228cf089c: ;   {runtime_call}
  0x0000018228cf089c: 0048 b8ac | 1660 b0fb | 7f00 00ff | d041 c787 | 4404 0000 | 0500 0000 | f083 4424 | c000 493b 
  0x0000018228cf08bc: af48 0400 | 000f 870e | 0000 0041 | 83bf 4004 | 0000 000f | 8428 0000 | 0048 8945 | f849 8bcf 
  0x0000018228cf08dc: 4c8b e448 | 83ec 2048 

  0x0000018228cf08e4: ;   {runtime_call}
  0x0000018228cf08e4: 83e4 f048 | b860 cfba | 7ffb 7f00 | 00ff d049 | 8be4 4d33 | e448 8b45 | f841 c787 | 4404 0000 
  0x0000018228cf0904: 0800 0000 | 4183 bfc0 | 0400 0002 | 0f84 c700 

  0x0000018228cf0914: ;   {external_word}
  0x0000018228cf0914: 0000 49ba | 355b 4580 | fb7f 0000 | 4180 3a00 | 0f84 4c00 | 0000 4889 

  0x0000018228cf092c: ;   {metadata({method} {0x0000018233430ff0} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018228cf092c: 45f8 48ba | e80f 4333 | 8201 0000 | 498b cf48 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec 
  0x0000018228cf094c: ;   {runtime_call}
  0x0000018228cf094c: 0848 b8c0 | 4bef 7ffb | 7f00 00ff | d048 83c4 | 08e9 0c00 

  0x0000018228cf0960: ;   {runtime_call}
  0x0000018228cf0960: 0000 48b8 | c04b ef7f | fb7f 0000 | ffd0 4883 | c420 488b | 45f8 49c7 | 8798 0300 | 0000 0000 
  0x0000018228cf0980: 0049 c787 | a003 0000 | 0000 0000 | 4885 c00f | 8425 0000 | 00a8 030f | 8508 0000 | 0048 8b00 
  0x0000018228cf09a0: e915 0000 | 00a8 010f | 8509 0000 | 0048 8b40 | fee9 0400 | 0000 488b | 40ff 498b | 8f28 0400 
  0x0000018228cf09c0: 00c7 8100 | 0100 0000 | 0000 00c9 | 4983 7f08 | 000f 8501 | 0000 00c3 

  0x0000018228cf09d8: ;   {runtime_call StubRoutines (initial stubs)}
  0x0000018228cf09d8: e923 059f | ff48 8945 | f84c 8be4 | 4883 ec20 | 4883 e4f0 

  0x0000018228cf09ec: ;   {runtime_call}
  0x0000018228cf09ec: 48b8 a083 | ef7f fb7f | 0000 ffd0 | 498b e44d | 33e4 488b | 45f8 e90f | ffff fff4 
[/MachCode]


Compiled method (c1) 9190 2348       3       java.lang.ClassLoader::defineClass (43 bytes)
 total in heap  [0x0000018221600890,0x00000182216011f0] = 2400
 relocation     [0x00000182216009f0,0x0000018221600a90] = 160
 main code      [0x0000018221600aa0,0x0000018221600f40] = 1184
 stub code      [0x0000018221600f40,0x0000018221600fb8] = 120
 metadata       [0x0000018221600fb8,0x0000018221600fe8] = 48
 scopes data    [0x0000018221600fe8,0x00000182216010d8] = 240
 scopes pcs     [0x00000182216010d8,0x00000182216011b8] = 224
 dependencies   [0x00000182216011b8,0x00000182216011c8] = 16
 nul chk table  [0x00000182216011c8,0x00000182216011f0] = 40

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000018233431370} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # this:     rdx:rdx   = 'java/lang/ClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi       = int
  # parm3:    rsi       = int
  # parm4:    rcx:rcx   = 'java/security/ProtectionDomain'
  #           [sp+0xc0]  (sp of caller)
  0x0000018221600aa0: 448b 5208 | 49bb 0000 | 0033 8201 | 0000 4d03 | d34c 3bd0 

  0x0000018221600ab4: ;   {runtime_call ic_miss_stub}
  0x0000018221600ab4: 0f85 c6d9 | 1207 660f | 1f44 0000 
[Verified Entry Point]
  0x0000018221600ac0: 8984 2400 | 80ff ff55 | 4881 ecb0 | 0000 0090 | 4181 7f20 | 0200 0000 

  0x0000018221600ad8: ;   {runtime_call StubRoutines (final stubs)}
  0x0000018221600ad8: 7405 e8e1 | 7e11 0748 | 8954 2478 

  0x0000018221600ae4: ;   {metadata(method data for {method} {0x0000018233431370} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018221600ae4: 48bb b825 | 3675 8201 | 0000 8b83 | cc00 0000 | 83c0 0289 | 83cc 0000 | 0025 fe07 | 0000 85c0 
  0x0000018221600b04: 0f84 5803 | 0000 89b4 | 2494 0000 | 0089 bc24 | 9000 0000 | 4c89 8c24 | 8800 0000 

  0x0000018221600b20: ;   {metadata(method data for {method} {0x0000018233431370} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018221600b20: 488b da48 | b8b8 2536 | 7582 0100 | 0048 8380 | 1001 0000 | 0149 8bd8 | 4c8b c34c | 8bc9 488b 
  0x0000018221600b40: ca48 8bd1 | 4889 9c24 | 8000 0000 

  0x0000018221600b4c: ;   {optimized virtual_call}
  0x0000018221600b4c: 6666 90e8 

  0x0000018221600b50: ; ImmutableOopMap {[120]=Oop [128]=Oop [136]=Oop }
                      ;*invokevirtual preDefineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@4
  0x0000018221600b50: 4c1c e8ff 

  0x0000018221600b54: ;   {other}
  0x0000018221600b54: 0f1f 8400 | c402 0000 | 4889 8424 | 9800 0000 | 488b 5424 

  0x0000018221600b68: ;   {metadata(method data for {method} {0x0000018233431370} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018221600b68: 7849 b8b8 | 2536 7582 | 0100 0049 | 8380 4801 

  0x0000018221600b78: ;   {metadata(method data for {method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600b78: 0000 0149 | b8d0 2c36 | 7582 0100 | 0041 8b90 | cc00 0000 | 83c2 0241 | 8990 cc00 | 0000 81e2 
  0x0000018221600b98: feff 1f00 | 85d2 0f84 | df02 0000 

  0x0000018221600ba4: ; implicit exception: dispatches to 0x0000018221600ea4
  0x0000018221600ba4: 483b 004c 

  0x0000018221600ba8: ;   {metadata(method data for {method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600ba8: 8bc0 48ba | d02c 3675 | 8201 0000 | 4883 8210 | 0100 0001 | 448b 4010 

  0x0000018221600bc0: ;   {metadata(method data for {method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600bc0: 4d85 c048 | bad0 2c36 | 7582 0100 | 0048 c7c6 | 5801 0000 | 7507 48c7 | c648 0100 | 0048 8b3c 
  0x0000018221600be0: 3248 8d7f | 0148 893c | 320f 850f 

  0x0000018221600bec: ;   {oop(nullptr)}
  0x0000018221600bec: 0000 0048 | bb00 0000 | 0000 0000 | 00e9 a901 | 0000 493b | 0049 8bd0 

  0x0000018221600c04: ;   {metadata(method data for {method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600c04: 48be d02c | 3675 8201 | 0000 4883 | 8668 0100 | 0001 418b | 500c 4885 

  0x0000018221600c1c: ;   {metadata(method data for {method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600c1c: d248 bed0 | 2c36 7582 | 0100 0048 | c7c7 b001 | 0000 7507 | 48c7 c7a0 | 0100 0048 | 8b1c 3e48 
  0x0000018221600c3c: 8d5b 0148 | 891c 3e0f | 850f 0000 

  0x0000018221600c48: ;   {oop(nullptr)}
  0x0000018221600c48: 0048 bb00 | 0000 0000 | 0000 00e9 | 4f01 0000 

  0x0000018221600c58: ;   {metadata(method data for {method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600c58: 48be d02c | 3675 8201 | 0000 4883 | 86c0 0100 | 0001 483b | 024c 8bc2 

  0x0000018221600c70: ;   {metadata(method data for {method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600c70: 48be d02c | 3675 8201 | 0000 4883 | 86f8 0100 

  0x0000018221600c80: ;   {metadata(method data for {method} {0x000001823301a838} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000018221600c80: 0001 49b8 | a020 3675 | 8201 0000 | 418b b0cc | 0000 0083 | c602 4189 | b0cc 0000 | 0081 e6fe 
  0x0000018221600ca0: ff1f 0085 | f60f 8408 | 0200 004c 

  0x0000018221600cac: ;   {metadata(method data for {method} {0x000001823301a838} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000018221600cac: 8bc2 48be | a020 3675 | 8201 0000 | 4883 8610 | 0100 0001 

  0x0000018221600cc0: ;   {metadata(method data for {method} {0x0000018233424bf8} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000018221600cc0: 49b8 c0ce | 3575 8201 | 0000 418b | b0cc 0000 | 0083 c602 | 4189 b0cc | 0000 0081 | e6fe ff1f 
  0x0000018221600ce0: 0085 f60f | 84eb 0100 | 008b 7238 

  0x0000018221600cec: ; implicit exception: dispatches to 0x0000018221600ef5
  0x0000018221600cec: 483b 064c 

  0x0000018221600cf0: ;   {metadata(method data for {method} {0x0000018233424bf8} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000018221600cf0: 8bc6 48bf | c0ce 3575 | 8201 0000 | 458b 4008 | 49ba 0000 | 0033 8201 | 0000 4d03 | c24c 3b87 
  0x0000018221600d10: 2001 0000 | 750d 4883 | 8728 0100 | 0001 e960 | 0000 004c | 3b87 3001 | 0000 750d | 4883 8738 
  0x0000018221600d30: 0100 0001 | e94a 0000 | 0048 83bf | 2001 0000 | 0075 174c | 8987 2001 | 0000 48c7 | 8728 0100 
  0x0000018221600d50: 0001 0000 | 00e9 2900 | 0000 4883 | bf30 0100 | 0000 7517 | 4c89 8730 | 0100 0048 | c787 3801 
  0x0000018221600d70: 0000 0100 | 0000 e908 | 0000 0048 | 8387 1001 | 0000 014c | 8bc2 488b | d60f 1f40 | 0048 b808 
  0x0000018221600d90: 3421 3382 

  0x0000018221600d94: ;   {virtual_call}
  0x0000018221600d94: 0100 00e8 

  0x0000018221600d98: ; ImmutableOopMap {[120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.net.URL::toExternalForm@5
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000018221600d98: e4d4 dcff 

  0x0000018221600d9c: ;   {other}
  0x0000018221600d9c: 0f1f 8400 | 0c05 0001 | 488b d848 | 8b84 2498 | 0000 008b | b424 9400 | 0000 8bbc | 2490 0000 
  0x0000018221600dbc: 004c 8b8c | 2488 0000 | 004c 8b84 | 2480 0000 | 0048 8b54 

  0x0000018221600dd0: ;   {metadata(method data for {method} {0x0000018233431370} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018221600dd0: 2478 48b9 | b825 3675 | 8201 0000 | 4883 8180 | 0100 0001 | 4c8b da49 | 8bd3 488b | c848 891c 
  0x0000018221600df0: 2466 0f1f 

  0x0000018221600df4: ;   {static_call}
  0x0000018221600df4: 4400 00e8 

  0x0000018221600df8: ; ImmutableOopMap {[120]=Oop [152]=Oop }
                      ;*invokestatic defineClass1 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@27
  0x0000018221600df8: 64f9 6e07 

  0x0000018221600dfc: ;   {other}
  0x0000018221600dfc: 0f1f 8400 | 6c05 0002 | 488b 5424 

  0x0000018221600e08: ;   {metadata(method data for {method} {0x0000018233431370} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018221600e08: 7849 b8b8 | 2536 7582 | 0100 0049 | 8380 9001 | 0000 014c | 8bc0 4c8b | 8c24 9800 | 0000 488b 
  0x0000018221600e28: 5424 7848 | 8984 24a0 | 0000 000f 

  0x0000018221600e34: ;   {optimized virtual_call}
  0x0000018221600e34: 1f40 00e8 

  0x0000018221600e38: ; ImmutableOopMap {[160]=Oop }
                      ;*invokevirtual postDefineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@37
  0x0000018221600e38: e410 e8ff 

  0x0000018221600e3c: ;   {other}
  0x0000018221600e3c: 0f1f 8400 | ac05 0003 | 488b 8424 | a000 0000 | 4881 c4b0 | 0000 005d 

  0x0000018221600e54: ;   {poll_return}
  0x0000018221600e54: 493b a748 | 0400 000f | 8799 0000 

  0x0000018221600e60: ;   {metadata({method} {0x0000018233431370} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018221600e60: 00c3 49ba | 6813 4333 | 8201 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x0000018221600e78: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000018221600e78: ffe8 820a 

  0x0000018221600e7c: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rcx=Oop [120]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::defineClass@-1
  0x0000018221600e7c: 1e07 e987 

  0x0000018221600e80: ;   {metadata({method} {0x0000018233431058} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000018221600e80: fcff ff49 | ba50 1043 | 3382 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000018221600e98: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000018221600e98: ffff e861 

  0x0000018221600e9c: ; ImmutableOopMap {rax=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::defineClassSourceLocation@-1
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000018221600e9c: 0a1e 07e9 | 00fd ffff 

  0x0000018221600ea4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000018221600ea4: e8d7 b51d 

  0x0000018221600ea8: ; ImmutableOopMap {rax=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual getCodeSource {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@1
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000018221600ea8: 07e8 d2b5 

  0x0000018221600eac: ; ImmutableOopMap {rax=Oop r8=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual getLocation {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@12
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000018221600eac: 1d07 e8cd 

  0x0000018221600eb0: ; ImmutableOopMap {rdx=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {metadata({method} {0x000001823301a838} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000018221600eb0: b51d 0749 | ba30 a801 | 3382 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000018221600ec8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000018221600ec8: ffff e831 

  0x0000018221600ecc: ; ImmutableOopMap {rdx=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - java.net.URL::toString@-1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000018221600ecc: 0a1e 07e9 | d7fd ffff 

  0x0000018221600ed4: ;   {metadata({method} {0x0000018233424bf8} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000018221600ed4: 49ba f04b | 4233 8201 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x0000018221600ee8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000018221600ee8: ffff ffe8 

  0x0000018221600eec: ; ImmutableOopMap {rdx=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - java.net.URL::toExternalForm@-1
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000018221600eec: 100a 1e07 | e9f4 fdff 

  0x0000018221600ef4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000018221600ef4: ffe8 86b5 

  0x0000018221600ef8: ; ImmutableOopMap {rdx=Oop rsi=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.net.URL::toExternalForm@5
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {internal_word}
  0x0000018221600ef8: 1d07 49ba | 540e 6021 | 8201 0000 | 4d89 9760 

  0x0000018221600f08: ;   {runtime_call SafepointBlob}
  0x0000018221600f08: 0400 00e9 | 7037 1307 | 498b 87f8 | 0400 0049 | c787 f804 | 0000 0000 | 0000 49c7 | 8700 0500 
  0x0000018221600f28: 0000 0000 | 0048 81c4 | b000 0000 

  0x0000018221600f34: ;   {runtime_call unwind_exception Runtime1 stub}
  0x0000018221600f34: 5de9 46a6 | 1d07 f4f4 | f4f4 f4f4 
[Stub Code]
  0x0000018221600f40: ;   {no_reloc}
  0x0000018221600f40: 0f1f 4400 

  0x0000018221600f44: ;   {static_stub}
  0x0000018221600f44: 0048 bb00 | 0000 0000 

  0x0000018221600f4c: ;   {runtime_call nmethod}
  0x0000018221600f4c: 0000 00e9 | fbff ffff 

  0x0000018221600f54: ;   {static_stub}
  0x0000018221600f54: 48bb 0000 | 0000 0000 

  0x0000018221600f5c: ;   {runtime_call nmethod}
  0x0000018221600f5c: 0000 e9fb 

  0x0000018221600f60: ;   {static_stub}
  0x0000018221600f60: ffff ff48 | bb00 0000 | 0000 0000 

  0x0000018221600f6c: ;   {runtime_call nmethod}
  0x0000018221600f6c: 00e9 fbff 

  0x0000018221600f70: ;   {static_stub}
  0x0000018221600f70: ffff 48bb | 0000 0000 | 0000 0000 

  0x0000018221600f7c: ;   {runtime_call nmethod}
  0x0000018221600f7c: e9fb ffff 

  0x0000018221600f80: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x0000018221600f80: ffe8 7ad6 

  0x0000018221600f84: ;   {external_word}
  0x0000018221600f84: 1d07 48b9 | 907f 1b80 | fb7f 0000 | 4883 e4f0 

  0x0000018221600f94: ;   {runtime_call}
  0x0000018221600f94: 48b8 f042 | de7f fb7f | 0000 ffd0 

  0x0000018221600fa0: ;   {section_word}
  0x0000018221600fa0: f449 baa1 | 0f60 2182 | 0100 0041 

  0x0000018221600fac: ;   {runtime_call DeoptimizationBlob}
  0x0000018221600fac: 52e9 ee2f | 1307 f4f4 | f4f4 f4f4 
[/MachCode]


Compiled method (c1) 9207 3119   !   3       org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass (67 bytes)
 total in heap  [0x00000182217e5810,0x00000182217e63f0] = 3040
 relocation     [0x00000182217e5970,0x00000182217e5a18] = 168
 main code      [0x00000182217e5a20,0x00000182217e6020] = 1536
 stub code      [0x00000182217e6020,0x00000182217e6090] = 112
 oops           [0x00000182217e6090,0x00000182217e6098] = 8
 metadata       [0x00000182217e6098,0x00000182217e60d8] = 64
 scopes data    [0x00000182217e60d8,0x00000182217e6228] = 336
 scopes pcs     [0x00000182217e6228,0x00000182217e6338] = 272
 dependencies   [0x00000182217e6338,0x00000182217e6350] = 24
 handler table  [0x00000182217e6350,0x00000182217e63c8] = 120
 nul chk table  [0x00000182217e63c8,0x00000182217e63f0] = 40

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader'
  # this:     rdx:rdx   = 'org/eclipse/osgi/internal/loader/ModuleClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi:rdi   = 'org/eclipse/osgi/internal/loader/classpath/ClasspathEntry'
  #           [sp+0xb0]  (sp of caller)
  0x00000182217e5a20: 448b 5208 | 49bb 0000 | 0033 8201 | 0000 4d03 | d34c 3bd0 

  0x00000182217e5a34: ;   {runtime_call ic_miss_stub}
  0x00000182217e5a34: 0f85 468a | f406 660f | 1f44 0000 
[Verified Entry Point]
  0x00000182217e5a40: 8984 2400 | 80ff ff55 | 4881 eca0 | 0000 0090 | 4181 7f20 | 0200 0000 

  0x00000182217e5a58: ;   {runtime_call StubRoutines (final stubs)}
  0x00000182217e5a58: 7405 e861 

  0x00000182217e5a5c: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5a5c: 2ff3 0648 | be50 dfa6 | 7582 0100 | 008b 9ecc | 0000 0083 | c302 899e | cc00 0000 | 81e3 fe07 
  0x00000182217e5a7c: 0000 85db | 0f84 5304 | 0000 4889 | 7c24 704c | 894c 2468 

  0x00000182217e5a90: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5a90: 488b f248 | bb50 dfa6 | 7582 0100 | 008b 7608 | 49ba 0000 | 0033 8201 | 0000 4903 | f248 3bb3 
  0x00000182217e5ab0: 2001 0000 | 750d 4883 | 8328 0100 | 0001 e960 | 0000 0048 | 3bb3 3001 | 0000 750d | 4883 8338 
  0x00000182217e5ad0: 0100 0001 | e94a 0000 | 0048 83bb | 2001 0000 | 0075 1748 | 89b3 2001 | 0000 48c7 | 8328 0100 
  0x00000182217e5af0: 0001 0000 | 00e9 2900 | 0000 4883 | bb30 0100 | 0000 7517 | 4889 b330 | 0100 0048 | c783 3801 
  0x00000182217e5b10: 0000 0100 | 0000 e908 | 0000 0048 | 8383 1001 

  0x00000182217e5b20: ;   {metadata(method data for {method} {0x000001827561c958} 'getClassLoadingLock' '(Ljava/lang/String;)Ljava/lang/Object;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5b20: 0000 0148 | bef8 6d98 | 7582 0100 | 008b 9ecc | 0000 0083 | c302 899e | cc00 0000 | 81e3 feff 
  0x00000182217e5b40: 1f00 85db | 0f84 b003 | 0000 8b72 | 5048 3b06 

  0x00000182217e5b50: ;   {metadata(method data for {method} {0x000001827561c958} 'getClassLoadingLock' '(Ljava/lang/String;)Ljava/lang/Object;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5b50: 488b de48 | b8f8 6d98 | 7582 0100 | 0048 8380 | 1001 0000 | 0149 8bd8 | 4c8b c348 | 8954 2458 
  0x00000182217e5b70: 488b d648 | 895c 2460 | 0f1f 8000 

  0x00000182217e5b7c: ;   {optimized virtual_call}
  0x00000182217e5b7c: 0000 00e8 

  0x00000182217e5b80: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop }
                      ;*invokevirtual getLock {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::getClassLoadingLock@5 (line 437)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@8 (line 312)
  0x00000182217e5b80: 7cf3 e3ff 

  0x00000182217e5b84: ;   {other}
  0x00000182217e5b84: 0f1f 8400 | 7403 0000 | 4889 4424 | 7848 8d94 | 2480 0000 | 0048 8bf0 | 4889 7208 

  0x00000182217e5ba0: ; implicit exception: dispatches to 0x00000182217e5f20
  0x00000182217e5ba0: 488b 0648 | 83c8 0148 | 8902 f048 | 0fb1 160f | 8412 0000 | 0048 2bc4 | 4825 07f0 | ffff 4889 
  0x00000182217e5bc0: 020f 855e | 0300 0049 | ff87 4805 | 0000 488b 

  0x00000182217e5bd0: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5bd0: 5424 5848 | be50 dfa6 | 7582 0100 | 0048 8386 | 4801 0000 

  0x00000182217e5be4: ;   {metadata(method data for {method} {0x0000018233431478} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000182217e5be4: 0148 bad8 | 8123 7582 | 0100 008b | b2cc 0000 | 0083 c602 | 89b2 cc00 | 0000 81e6 | feff 1f00 
  0x00000182217e5c04: 85f6 0f84 | 2c03 0000 

  0x00000182217e5c0c: ;   {metadata(method data for {method} {0x0000018233431478} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000182217e5c0c: 48ba d881 | 2375 8201 | 0000 4883 | 8210 0100 | 0001 488b | 5424 600f 

  0x00000182217e5c24: ;   {static_call}
  0x00000182217e5c24: 1f40 00e8 

  0x00000182217e5c28: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*invokestatic checkName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@1
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@17 (line 313)
  0x00000182217e5c28: b46a 5d07 

  0x00000182217e5c2c: ;   {other}
  0x00000182217e5c2c: 0f1f 8400 | 1c04 0001 

  0x00000182217e5c34: ;   {metadata(method data for {method} {0x0000018233431478} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000182217e5c34: 85c0 49b8 | d881 2375 | 8201 0000 | 48c7 c220 | 0100 0075 | 0748 c7c2 | 3001 0000 | 498b 3410 
  0x00000182217e5c54: 488d 7601 | 4989 3410 | 0f85 0f00 

  0x00000182217e5c60: ;   {oop(nullptr)}
  0x00000182217e5c60: 0000 48be | 0000 0000 | 0000 0000 | e936 0000 | 0048 8b54 

  0x00000182217e5c74: ;   {metadata(method data for {method} {0x0000018233431478} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000182217e5c74: 2458 49b8 | d881 2375 | 8201 0000 | 4983 8040 | 0100 0001 | 4c8b 4424 | 6048 8b54 | 2458 0f1f 
  0x00000182217e5c94: ;   {optimized virtual_call}
  0x00000182217e5c94: 4400 00e8 

  0x00000182217e5c98: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*invokevirtual findLoadedClass0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@11
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@17 (line 313)
  0x00000182217e5c98: 44e2 4b07 

  0x00000182217e5c9c: ;   {other}
  0x00000182217e5c9c: 0f1f 8400 | 8c04 0002 | 488b f048 

  0x00000182217e5ca8: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5ca8: 85f6 49b8 | 50df a675 | 8201 0000 | 49c7 c190 | 0100 0074 | 0749 c7c1 | 8001 0000 | 4b8b 3c08 
  0x00000182217e5cc8: 488d 7f01 | 4b89 3c08 | 0f84 0e00 | 0000 488b | de41 b800 | 0000 00e9 | 6900 0000 | 488b 7c24 
  0x00000182217e5ce8: 704c 8b4c | 2468 4c8b | 4424 6048 | 8b54 2458 

  0x00000182217e5cf8: ; implicit exception: dispatches to 0x00000182217e5f59
  0x00000182217e5cf8: 418b 710c 

  0x00000182217e5cfc: ; implicit exception: dispatches to 0x00000182217e5f5e
  0x00000182217e5cfc: 483b 0748 

  0x00000182217e5d00: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5d00: 8bcf 48bb | 50df a675 | 8201 0000 | 4883 83b8 | 0100 0001 | 8b4f 1048 

  0x00000182217e5d18: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5d18: 8bfa 48bb | 50df a675 | 8201 0000 | 4883 83d8 | 0100 0001 | bf00 0000 | 0066 0f1f 

  0x00000182217e5d34: ;   {optimized virtual_call}
  0x00000182217e5d34: 4400 00e8 

  0x00000182217e5d38: ; ImmutableOopMap {[120]=Oop [136]=Oop }
                      ;*invokevirtual defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@37 (line 315)
  0x00000182217e5d38: 84ad e1ff 

  0x00000182217e5d3c: ;   {other}
  0x00000182217e5d3c: 0f1f 8400 | 2c05 0003 | 488b d841 | b801 0000 | 0048 8d84 | 2480 0000 | 0048 8b10 | 4885 d20f 
  0x00000182217e5d5c: 840f 0000 | 0048 8b70 | 08f0 480f | b116 0f85 | f301 0000 | 49ff 8f48 

  0x00000182217e5d74: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5d74: 0500 0048 | ba50 dfa6 | 7582 0100 | 00ff 8210 

  0x00000182217e5d84: ;   {metadata('org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x00000182217e5d84: 0200 0048 | baf0 5111 | 3482 0100 | 0049 8b87 | b801 0000 | 488d 7818 | 493b bfc8 | 0100 000f 
  0x00000182217e5da4: 87d0 0100 | 0049 89bf | b801 0000 | 48c7 0001 | 0000 0048 | 8bca 49ba | 0000 0033 | 8201 0000 
  0x00000182217e5dc4: 492b ca89 | 4808 4833 | c989 480c | 4833 c948 | 8948 1048 

  0x00000182217e5dd8: ;   {metadata(method data for {method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5dd8: 8bf0 48bf | 50df a675 | 8201 0000 | 4883 8728 | 0200 0001 

  0x00000182217e5dec: ;   {metadata(method data for {method} {0x00000182757679a0} '<init>' '(Ljava/lang/Class;Z)V' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x00000182217e5dec: 48be 68e3 | a675 8201 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x00000182217e5e0c: ff0f 8473 | 0100 0048 

  0x00000182217e5e14: ;   {metadata(method data for {method} {0x00000182757679a0} '<init>' '(Ljava/lang/Class;Z)V' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x00000182217e5e14: 8bf0 48bf | 68e3 a675 | 8201 0000 | 4883 8710 | 0100 0001 

  0x00000182217e5e28: ;   {metadata(method data for {method} {0x0000018233470e28} '<init>' '()V' in 'java/lang/Object')}
  0x00000182217e5e28: 48be 6067 | 0075 8201 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x00000182217e5e48: ff0f 8458 | 0100 004c | 8bd3 4489 | 5010 488d | 3048 c1ee | 0948 bf00 | 0036 1d82 | 0100 00c6 
  0x00000182217e5e68: 043e 0041 | 83e0 0144 | 8840 0c48 | 81c4 a000 

  0x00000182217e5e78: ;   {poll_return}
  0x00000182217e5e78: 0000 5d49 | 3ba7 4804 | 0000 0f87 | 4001 0000 | c349 8b87 | f804 0000 | 4d33 d24d | 8997 f804 
  0x00000182217e5e98: 0000 4d33 | d24d 8997 | 0005 0000 | 488b f048 | 8d84 2480 | 0000 0048 | 8b38 4885 | ff0f 840f 
  0x00000182217e5eb8: 0000 0048 | 8b58 08f0 | 480f b13b | 0f85 1401 | 0000 49ff | 8f48 0500 | 0048 8bc6 | e938 0100 
  0x00000182217e5ed8: ;   {metadata({method} {0x00000182755d7ca8} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5ed8: 0049 baa0 | 7c5d 7582 | 0100 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x00000182217e5ef0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x00000182217e5ef0: e80b baff 

  0x00000182217e5ef4: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rdi=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@-1 (line 310)
  0x00000182217e5ef4: 06e9 8cfb 

  0x00000182217e5ef8: ;   {metadata({method} {0x000001827561c958} 'getClassLoadingLock' '(Ljava/lang/String;)Ljava/lang/Object;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x00000182217e5ef8: ffff 49ba | 50c9 6175 | 8201 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x00000182217e5f10: ;   {runtime_call counter_overflow Runtime1 stub}
  0x00000182217e5f10: ffe8 eab9 

  0x00000182217e5f14: ; ImmutableOopMap {rdx=Oop r8=Oop [104]=Oop [112]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::getClassLoadingLock@-1 (line 437)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@8 (line 312)
  0x00000182217e5f14: ff06 e92f 

  0x00000182217e5f18: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x00000182217e5f18: fcff ffe8 

  0x00000182217e5f1c: ; ImmutableOopMap {rdx=Oop r8=Oop rsi=Oop [104]=Oop [112]=Oop }
                      ;*invokevirtual getLock {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::getClassLoadingLock@5 (line 437)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@8 (line 312)
  0x00000182217e5f1c: 6065 ff06 

  0x00000182217e5f20: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x00000182217e5f20: e85b 65ff 

  0x00000182217e5f24: ; ImmutableOopMap {rsi=Oop [88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@14 (line 312)
  0x00000182217e5f24: 0648 8974 | 2408 4889 

  0x00000182217e5f2c: ;   {runtime_call monitorenter_nofpu Runtime1 stub}
  0x00000182217e5f2c: 1424 e8cd 

  0x00000182217e5f30: ; ImmutableOopMap {rsi=Oop [88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@14 (line 312)
  0x00000182217e5f30: 98ff 06e9 | 96fc ffff 

  0x00000182217e5f38: ;   {metadata({method} {0x0000018233431478} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000182217e5f38: 49ba 7014 | 4333 8201 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x00000182217e5f4c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x00000182217e5f4c: ffff ffe8 

  0x00000182217e5f50: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::findLoadedClass@-1
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@17 (line 313)
  0x00000182217e5f50: acb9 ff06 | e9b3 fcff 

  0x00000182217e5f58: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x00000182217e5f58: ffe8 2265 

  0x00000182217e5f5c: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rdi=Oop [120]=Oop [136]=Oop }
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@32 (line 315)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x00000182217e5f5c: ff06 e81d 

  0x00000182217e5f60: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rdi=Oop [120]=Oop [136]=Oop }
                      ;*invokevirtual getDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@34 (line 315)
  0x00000182217e5f60: 65ff 0648 | 8d84 2480 | 0000 0048 

  0x00000182217e5f6c: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x00000182217e5f6c: 8904 24e8 | 8c9e ff06 | e9fe fdff | ff48 8bd2 

  0x00000182217e5f7c: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x00000182217e5f7c: e8ff 6dff 

  0x00000182217e5f80: ; ImmutableOopMap {rbx=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@55 (line 319)
  0x00000182217e5f80: 06e9 51fe 

  0x00000182217e5f84: ;   {metadata({method} {0x00000182757679a0} '<init>' '(Ljava/lang/Class;Z)V' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x00000182217e5f84: ffff 49ba | 9879 7675 | 8201 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x00000182217e5f9c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x00000182217e5f9c: ffe8 5eb9 

  0x00000182217e5fa0: ; ImmutableOopMap {rbx=Oop rax=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader$DefineClassResult::<init>@-1 (line 96)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@63 (line 319)
  0x00000182217e5fa0: ff06 e96c 

  0x00000182217e5fa4: ;   {metadata({method} {0x0000018233470e28} '<init>' '()V' in 'java/lang/Object')}
  0x00000182217e5fa4: feff ff49 | ba20 0e47 | 3382 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x00000182217e5fbc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x00000182217e5fbc: ffff e83d 

  0x00000182217e5fc0: ; ImmutableOopMap {rbx=Oop rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.Object::<init>@-1
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader$DefineClassResult::<init>@1 (line 96)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@63 (line 319)
  0x00000182217e5fc0: b9ff 06e9 | 87fe ffff 

  0x00000182217e5fc8: ;   {internal_word}
  0x00000182217e5fc8: 49ba 7b5e | 7e21 8201 | 0000 4d89 | 9760 0400 

  0x00000182217e5fd8: ;   {runtime_call SafepointBlob}
  0x00000182217e5fd8: 00e9 a2e6 | f406 488d | 8424 8000 | 0000 4889 

  0x00000182217e5fe8: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x00000182217e5fe8: 0424 e811 | 9eff 06e9 | ddfe ffff | 498b 87f8 | 0400 0049 | c787 f804 | 0000 0000 | 0000 49c7 
  0x00000182217e6008: 8700 0500 | 0000 0000 | 0048 81c4 | a000 0000 

  0x00000182217e6018: ;   {runtime_call unwind_exception Runtime1 stub}
  0x00000182217e6018: 5de9 6255 | ff06 f4f4 
[Stub Code]
  0x00000182217e6020: ;   {no_reloc}
  0x00000182217e6020: 48bb 0000 | 0000 0000 

  0x00000182217e6028: ;   {runtime_call nmethod}
  0x00000182217e6028: 0000 e9fb 

  0x00000182217e602c: ;   {static_stub}
  0x00000182217e602c: ffff ff48 | bb00 0000 | 0000 0000 

  0x00000182217e6038: ;   {runtime_call nmethod}
  0x00000182217e6038: 00e9 fbff 

  0x00000182217e603c: ;   {static_stub}
  0x00000182217e603c: ffff 48bb | 0000 0000 | 0000 0000 

  0x00000182217e6048: ;   {runtime_call nmethod}
  0x00000182217e6048: e9fb ffff 

  0x00000182217e604c: ;   {static_stub}
  0x00000182217e604c: ff48 bb00 | 0000 0000 

  0x00000182217e6054: ;   {runtime_call nmethod}
  0x00000182217e6054: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x00000182217e605c: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x00000182217e605c: e89f 85ff 

  0x00000182217e6060: ;   {external_word}
  0x00000182217e6060: 0648 b990 | 7f1b 80fb | 7f00 0048 

  0x00000182217e606c: ;   {runtime_call}
  0x00000182217e606c: 83e4 f048 | b8f0 42de | 7ffb 7f00 | 00ff d0f4 
[Deopt Handler Code]
  0x00000182217e607c: ;   {section_word}
  0x00000182217e607c: 49ba 7c60 | 7e21 8201 | 0000 4152 

  0x00000182217e6088: ;   {runtime_call DeoptimizationBlob}
  0x00000182217e6088: e913 dff4 | 06f4 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000182794fbed0, length=23, elements={
0x000001821dfb08e0, 0x00000182322d9900, 0x00000182322dbb90, 0x00000182322dec30,
0x00000182322e2960, 0x00000182322e7560, 0x00000182322e4950, 0x00000182322e8b30,
0x0000018232300210, 0x000001821e01dec0, 0x000001827431bc60, 0x0000018274627480,
0x0000018274c0a130, 0x00000182747e3670, 0x0000018274c8fc60, 0x00000182796df270,
0x0000018279a79f60, 0x00000182743c0110, 0x0000018279a18280, 0x0000018279a15490,
0x0000018279a17bf0, 0x0000018279a15b20, 0x0000018279a14e00
}

Java Threads: ( => current thread )
  0x000001821dfb08e0 JavaThread "main"                              [_thread_blocked, id=4188, stack(0x0000004376400000,0x0000004376500000) (1024K)]
  0x00000182322d9900 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7620, stack(0x0000004376800000,0x0000004376900000) (1024K)]
  0x00000182322dbb90 JavaThread "Finalizer"                  daemon [_thread_blocked, id=6688, stack(0x0000004376900000,0x0000004376a00000) (1024K)]
  0x00000182322dec30 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=5732, stack(0x0000004376a00000,0x0000004376b00000) (1024K)]
  0x00000182322e2960 JavaThread "Attach Listener"            daemon [_thread_blocked, id=6732, stack(0x0000004376b00000,0x0000004376c00000) (1024K)]
  0x00000182322e7560 JavaThread "Service Thread"             daemon [_thread_blocked, id=3324, stack(0x0000004376c00000,0x0000004376d00000) (1024K)]
  0x00000182322e4950 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=4972, stack(0x0000004376d00000,0x0000004376e00000) (1024K)]
  0x00000182322e8b30 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=5008, stack(0x0000004376e00000,0x0000004376f00000) (1024K)]
  0x0000018232300210 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=4520, stack(0x0000004376f00000,0x0000004377000000) (1024K)]
  0x000001821e01dec0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=2604, stack(0x0000004377000000,0x0000004377100000) (1024K)]
  0x000001827431bc60 JavaThread "Notification Thread"        daemon [_thread_blocked, id=7292, stack(0x0000004377100000,0x0000004377200000) (1024K)]
  0x0000018274627480 JavaThread "Active Thread: Equinox Container: 2985bce5-7aae-48f2-8001-4e03e1758e07"        [_thread_blocked, id=7144, stack(0x0000004377600000,0x0000004377700000) (1024K)]
  0x0000018274c0a130 JavaThread "Refresh Thread: Equinox Container: 2985bce5-7aae-48f2-8001-4e03e1758e07" daemon [_thread_blocked, id=2948, stack(0x0000004377700000,0x0000004377800000) (1024K)]
  0x00000182747e3670 JavaThread "Framework Event Dispatcher: Equinox Container: 2985bce5-7aae-48f2-8001-4e03e1758e07" daemon [_thread_blocked, id=7528, stack(0x0000004377800000,0x0000004377900000) (1024K)]
  0x0000018274c8fc60 JavaThread "Start Level: Equinox Container: 2985bce5-7aae-48f2-8001-4e03e1758e07" daemon [_thread_in_vm, id=2176, stack(0x0000004377900000,0x0000004377a00000) (1024K)]
  0x00000182796df270 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=6292, stack(0x0000004377a00000,0x0000004377b00000) (1024K)]
  0x0000018279a79f60 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=3404, stack(0x0000004377b00000,0x0000004377c00000) (1024K)]
  0x00000182743c0110 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=2136, stack(0x0000004377c00000,0x0000004377d00000) (1024K)]
  0x0000018279a18280 JavaThread "Worker-JM"                         [_thread_blocked, id=6244, stack(0x0000004377e00000,0x0000004377f00000) (1024K)]
=>0x0000018279a15490 JavaThread "Worker-0: Updating Maven Dependencies"        [_thread_in_vm, id=7612, stack(0x0000004377f00000,0x0000004378000000) (1024K)]
  0x0000018279a17bf0 JavaThread "Worker-1: Repository registry initialization"        [_thread_in_vm, id=6784, stack(0x0000004378000000,0x0000004378100000) (1024K)]
  0x0000018279a15b20 JavaThread "Worker-2"                          [_thread_blocked, id=1056, stack(0x0000004378200000,0x0000004378300000) (1024K)]
  0x0000018279a14e00 JavaThread "Worker-3"                          [_thread_blocked, id=3660, stack(0x0000004378300000,0x0000004378400000) (1024K)]
Total: 23

Other Threads:
  0x000001821e06ec10 VMThread "VM Thread"                           [id=5472, stack(0x0000004376700000,0x0000004376800000) (1024K)]
  0x000001823228cb00 WatcherThread "VM Periodic Task Thread"        [id=2336, stack(0x0000004376600000,0x0000004376700000) (1024K)]
  0x000001821dfcf830 WorkerThread "GC Thread#0"                     [id=5228, stack(0x0000004376500000,0x0000004376600000) (1024K)]
  0x0000018274cfd890 WorkerThread "GC Thread#1"                     [id=7180, stack(0x0000004377300000,0x0000004377400000) (1024K)]
  0x0000018274656390 WorkerThread "GC Thread#2"                     [id=5196, stack(0x0000004377400000,0x0000004377500000) (1024K)]
  0x0000018274656730 WorkerThread "GC Thread#3"                     [id=1372, stack(0x0000004377500000,0x0000004377600000) (1024K)]
Total: 6

Threads with active compile tasks:
C2 CompilerThread0  9229 3889       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffb8046c300] Metaspace_lock - owner thread: 0x0000018279a15490

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000018233000000-0x0000018233ba0000-0x0000018233ba0000), size 12189696, SharedBaseAddress: 0x0000018233000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000018234000000-0x0000018274000000, reserved size: 1073741824
Narrow klass base: 0x0000018233000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 4 total, 4 available
 Memory: 4094M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 4

Heap:
 PSYoungGen      total 28672K, used 16496K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 24576K, 67% used [0x00000000eab00000,0x00000000ebb1c268,0x00000000ec300000)
  from space 4096K, 0% used [0x00000000ec480000,0x00000000ec480000,0x00000000ec880000)
  to   space 3584K, 0% used [0x00000000ec880000,0x00000000ec880000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 12436K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 18% used [0x00000000c0000000,0x00000000c0c25380,0x00000000c4300000)
 Metaspace       used 23558K, committed 24256K, reserved 1114112K
  class space    used 2245K, committed 2560K, reserved 1048576K

Card table byte_map: [0x000001821d960000,0x000001821db70000] _byte_map_base: 0x000001821d360000

Marking Bits: (ParMarkBitMap*) 0x00007ffb80473260
 Begin Bits: [0x00000182301b0000, 0x00000182311b0000)
 End Bits:   [0x00000182311b0000, 0x00000182321b0000)

Polling page: 0x000001821bd30000

Metaspace:

Usage:
  Non-class:     20.81 MB used.
      Class:      2.19 MB used.
       Both:     23.01 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      21.19 MB ( 33%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.50 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      23.69 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  9.98 MB
       Class:  13.46 MB
        Both:  23.44 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 616.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 379.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1517.
num_chunk_merges: 9.
num_chunk_splits: 999.
num_chunks_enlarged: 642.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1705Kb max_used=1705Kb free=118294Kb
 bounds [0x0000018228c80000, 0x0000018228ef0000, 0x00000182301b0000]
CodeHeap 'profiled nmethods': size=120000Kb used=8220Kb max_used=8220Kb free=111779Kb
 bounds [0x00000182211b0000, 0x00000182219c0000, 0x00000182286e0000]
CodeHeap 'non-nmethods': size=5760Kb used=1309Kb max_used=1324Kb free=4450Kb
 bounds [0x00000182286e0000, 0x0000018228950000, 0x0000018228c80000]
 total_blobs=4523 nmethods=3928 adapters=502
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 8.769 Thread 0x0000018232300210 3919       3       java.lang.invoke.BoundMethodHandle::fieldCount (8 bytes)
Event: 8.769 Thread 0x0000018232300210 nmethod 3919 0x00000182219b1a10 code [0x00000182219b1be0, 0x00000182219b1ed0]
Event: 8.769 Thread 0x0000018232300210 3923       3       jdk.internal.org.objectweb.asm.FieldWriter::computeFieldInfoSize (86 bytes)
Event: 8.770 Thread 0x0000018232300210 nmethod 3923 0x00000182219b2010 code [0x00000182219b2200, 0x00000182219b24e0]
Event: 8.770 Thread 0x0000018232300210 3924       3       jdk.internal.org.objectweb.asm.FieldWriter::putFieldInfo (269 bytes)
Event: 8.771 Thread 0x0000018232300210 nmethod 3924 0x00000182219b2610 code [0x00000182219b28a0, 0x00000182219b3190]
Event: 8.771 Thread 0x0000018232300210 3920       3       java.lang.invoke.LambdaForm::expressionCount (11 bytes)
Event: 8.771 Thread 0x0000018232300210 nmethod 3920 0x00000182219b3510 code [0x00000182219b36a0, 0x00000182219b37b8]
Event: 8.803 Thread 0x0000018232300210 3925       3       java.lang.invoke.ClassSpecializer$SpeciesData::transformHelper (56 bytes)
Event: 8.803 Thread 0x0000018232300210 nmethod 3925 0x00000182219b3890 code [0x00000182219b3ac0, 0x00000182219b4270]
Event: 8.807 Thread 0x0000018232300210 3926       3       java.lang.invoke.MemberName::asSpecial (89 bytes)
Event: 8.808 Thread 0x0000018232300210 nmethod 3926 0x00000182219b4510 code [0x00000182219b47e0, 0x00000182219b5448]
Event: 8.812 Thread 0x0000018232300210 3927       3       java.lang.invoke.InfoFromMemberName::getModifiers (8 bytes)
Event: 8.813 Thread 0x0000018232300210 nmethod 3927 0x00000182219b5890 code [0x00000182219b5a40, 0x00000182219b5bb8]
Event: 8.813 Thread 0x0000018232300210 3928       3       java.lang.invoke.MemberName::hashCode (43 bytes)
Event: 8.814 Thread 0x0000018232300210 nmethod 3928 0x00000182219b5c90 code [0x00000182219b5ee0, 0x00000182219b6a20]
Event: 8.814 Thread 0x0000018232300210 3929       3       java.lang.Byte::hashCode (8 bytes)
Event: 8.814 Thread 0x0000018232300210 nmethod 3929 0x00000182219b6d90 code [0x00000182219b6f40, 0x00000182219b70a8]
Event: 8.828 Thread 0x0000018232300210 3930       1       org.eclipse.core.internal.jobs.InternalJob::previous (5 bytes)
Event: 8.829 Thread 0x0000018232300210 nmethod 3930 0x0000018228e2a390 code [0x0000018228e2a520, 0x0000018228e2a5e8]

GC Heap History (16 events):
Event: 1.842 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4338K, committed 4544K, reserved 1114112K
  class space    used 437K, committed 512K, reserved 1048576K
}
Event: 1.853 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3230K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec727ab0,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 4338K, committed 4544K, reserved 1114112K
  class space    used 437K, committed 512K, reserved 1048576K
}
Event: 2.802 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28830K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 78% used [0x00000000ec400000,0x00000000ec727ab0,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 8042K, committed 8384K, reserved 1114112K
  class space    used 790K, committed 960K, reserved 1048576K
}
Event: 2.814 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4084K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfd168,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1625K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 2% used [0x00000000c0000000,0x00000000c01966a8,0x00000000c4300000)
 Metaspace       used 8042K, committed 8384K, reserved 1114112K
  class space    used 790K, committed 960K, reserved 1048576K
}
Event: 3.425 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29684K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfd168,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1625K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 2% used [0x00000000c0000000,0x00000000c01966a8,0x00000000c4300000)
 Metaspace       used 9225K, committed 9536K, reserved 1114112K
  class space    used 926K, committed 1088K, reserved 1048576K
}
Event: 3.437 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4091K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fee50,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5664K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c0588318,0x00000000c4300000)
 Metaspace       used 9225K, committed 9536K, reserved 1114112K
  class space    used 926K, committed 1088K, reserved 1048576K
}
Event: 4.088 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29691K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fee50,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5664K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 8% used [0x00000000c0000000,0x00000000c0588318,0x00000000c4300000)
 Metaspace       used 12528K, committed 12928K, reserved 1114112K
  class space    used 1255K, committed 1472K, reserved 1048576K
}
Event: 4.097 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4072K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfa100,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 7301K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 10% used [0x00000000c0000000,0x00000000c0721430,0x00000000c4300000)
 Metaspace       used 12528K, committed 12928K, reserved 1114112K
  class space    used 1255K, committed 1472K, reserved 1048576K
}
Event: 5.201 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29672K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfa100,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 7301K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 10% used [0x00000000c0000000,0x00000000c0721430,0x00000000c4300000)
 Metaspace       used 16499K, committed 17024K, reserved 1114112K
  class space    used 1647K, committed 1856K, reserved 1048576K
}
Event: 5.211 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4085K [0x00000000eab00000, 0x00000000ecf80000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fd548,0x00000000ec800000)
  to   space 6144K, 0% used [0x00000000ec980000,0x00000000ec980000,0x00000000ecf80000)
 ParOldGen       total 68608K, used 8651K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 12% used [0x00000000c0000000,0x00000000c0872db8,0x00000000c4300000)
 Metaspace       used 16499K, committed 17024K, reserved 1114112K
  class space    used 1647K, committed 1856K, reserved 1048576K
}
Event: 7.507 GC heap before
{Heap before GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 29173K [0x00000000eab00000, 0x00000000ecf80000, 0x0000000100000000)
  eden space 25088K, 100% used [0x00000000eab00000,0x00000000ec380000,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fd548,0x00000000ec800000)
  to   space 6144K, 0% used [0x00000000ec980000,0x00000000ec980000,0x00000000ecf80000)
 ParOldGen       total 68608K, used 8651K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 12% used [0x00000000c0000000,0x00000000c0872db8,0x00000000c4300000)
 Metaspace       used 18725K, committed 19520K, reserved 1114112K
  class space    used 1890K, committed 2240K, reserved 1048576K
}
Event: 7.516 GC heap after
{Heap after GC invocations=6 (full 0):
 PSYoungGen      total 29696K, used 4642K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec300000)
  from space 5120K, 90% used [0x00000000ec980000,0x00000000ece08af0,0x00000000ece80000)
  to   space 5120K, 0% used [0x00000000ec480000,0x00000000ec480000,0x00000000ec980000)
 ParOldGen       total 68608K, used 8659K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 12% used [0x00000000c0000000,0x00000000c0874db8,0x00000000c4300000)
 Metaspace       used 18725K, committed 19520K, reserved 1114112K
  class space    used 1890K, committed 2240K, reserved 1048576K
}
Event: 8.021 GC heap before
{Heap before GC invocations=7 (full 0):
 PSYoungGen      total 29696K, used 28159K [0x00000000eab00000, 0x00000000ece80000, 0x0000000100000000)
  eden space 24576K, 95% used [0x00000000eab00000,0x00000000ec1f71f8,0x00000000ec300000)
  from space 5120K, 90% used [0x00000000ec980000,0x00000000ece08af0,0x00000000ece80000)
  to   space 5120K, 0% used [0x00000000ec480000,0x00000000ec480000,0x00000000ec980000)
 ParOldGen       total 68608K, used 8659K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 12% used [0x00000000c0000000,0x00000000c0874db8,0x00000000c4300000)
 Metaspace       used 20756K, committed 21504K, reserved 1114112K
  class space    used 2048K, committed 2368K, reserved 1048576K
}
Event: 8.031 GC heap after
{Heap after GC invocations=7 (full 0):
 PSYoungGen      total 28672K, used 3977K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec300000)
  from space 4096K, 97% used [0x00000000ec480000,0x00000000ec862738,0x00000000ec880000)
  to   space 3584K, 0% used [0x00000000ec880000,0x00000000ec880000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 9910K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c09ad8d8,0x00000000c4300000)
 Metaspace       used 20756K, committed 21504K, reserved 1114112K
  class space    used 2048K, committed 2368K, reserved 1048576K
}
Event: 8.031 GC heap before
{Heap before GC invocations=8 (full 1):
 PSYoungGen      total 28672K, used 3977K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec300000)
  from space 4096K, 97% used [0x00000000ec480000,0x00000000ec862738,0x00000000ec880000)
  to   space 3584K, 0% used [0x00000000ec880000,0x00000000ec880000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 9910K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c09ad8d8,0x00000000c4300000)
 Metaspace       used 20756K, committed 21504K, reserved 1114112K
  class space    used 2048K, committed 2368K, reserved 1048576K
}
Event: 8.099 GC heap after
{Heap after GC invocations=8 (full 1):
 PSYoungGen      total 28672K, used 0K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec300000)
  from space 4096K, 0% used [0x00000000ec480000,0x00000000ec480000,0x00000000ec880000)
  to   space 3584K, 0% used [0x00000000ec880000,0x00000000ec880000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 12436K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 18% used [0x00000000c0000000,0x00000000c0c25380,0x00000000c4300000)
 Metaspace       used 20743K, committed 21504K, reserved 1114112K
  class space    used 2045K, committed 2368K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.017 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 0.183 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.215 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 0.227 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 0.230 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 0.235 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.270 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 0.579 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 2.851 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.507 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
Event: 4.510 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 6.942 Thread 0x0000018274c8fc60 DEOPT PACKING pc=0x0000018228ca84bc sp=0x00000043779f2fe0
Event: 6.942 Thread 0x0000018274c8fc60 DEOPT UNPACKING pc=0x0000018228733a9c sp=0x00000043779f2f70 mode 2
Event: 6.942 Thread 0x0000018274c8fc60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018228cc678c relative=0x000000000000006c
Event: 6.942 Thread 0x0000018274c8fc60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018228cc678c method=java.lang.AbstractStringBuilder.isLatin1()Z @ 10 c2
Event: 6.942 Thread 0x0000018274c8fc60 DEOPT PACKING pc=0x0000018228cc678c sp=0x00000043779f2fe0
Event: 6.942 Thread 0x0000018274c8fc60 DEOPT UNPACKING pc=0x0000018228733a9c sp=0x00000043779f2f70 mode 2
Event: 6.943 Thread 0x0000018274c8fc60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018228cb10e8 relative=0x0000000000000048
Event: 6.943 Thread 0x0000018274c8fc60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018228cb10e8 method=java.lang.StringLatin1.canEncode(I)Z @ 4 c2
Event: 6.943 Thread 0x0000018274c8fc60 DEOPT PACKING pc=0x0000018228cb10e8 sp=0x00000043779f2f70
Event: 6.943 Thread 0x0000018274c8fc60 DEOPT UNPACKING pc=0x0000018228733a9c sp=0x00000043779f2f00 mode 2
Event: 7.710 Thread 0x0000018274c8fc60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018228e100a4 relative=0x0000000000000084
Event: 7.710 Thread 0x0000018274c8fc60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018228e100a4 method=java.lang.invoke.LambdaForm$BasicType.basicType(C)Ljava/lang/invoke/LambdaForm$BasicType; @ 1 c2
Event: 7.710 Thread 0x0000018274c8fc60 DEOPT PACKING pc=0x0000018228e100a4 sp=0x00000043779f8260
Event: 7.710 Thread 0x0000018274c8fc60 DEOPT UNPACKING pc=0x0000018228733a9c sp=0x00000043779f81f8 mode 2
Event: 7.710 Thread 0x0000018274c8fc60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018228dcf0b4 relative=0x0000000000000a74
Event: 7.710 Thread 0x0000018274c8fc60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018228dcf0b4 method=jdk.internal.org.objectweb.asm.Frame.execute(IILjdk/internal/org/objectweb/asm/Symbol;Ljdk/internal/org/objectweb/asm/SymbolTable;)V @ 1 c2
Event: 7.710 Thread 0x0000018274c8fc60 DEOPT PACKING pc=0x0000018228dcf0b4 sp=0x00000043779f8040
Event: 7.710 Thread 0x0000018274c8fc60 DEOPT UNPACKING pc=0x0000018228733a9c sp=0x00000043779f7fb8 mode 2
Event: 8.823 Thread 0x0000018274c8fc60 DEOPT PACKING pc=0x0000018221618774 sp=0x00000043779f4280
Event: 8.823 Thread 0x0000018274c8fc60 DEOPT UNPACKING pc=0x0000018228734223 sp=0x00000043779f3810 mode 0

Classes loaded (20 events):
Event: 7.816 Loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 7.817 Loading class java/lang/ThreadLocal$SuppliedThreadLocal done
Event: 7.830 Loading class java/io/FileWriter
Event: 7.830 Loading class java/io/FileWriter done
Event: 7.972 Loading class java/lang/NegativeArraySizeException
Event: 7.972 Loading class java/lang/NegativeArraySizeException done
Event: 8.717 Loading class java/lang/runtime/ObjectMethods
Event: 8.718 Loading class java/lang/runtime/ObjectMethods done
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers done
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers$1
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers$1 done
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers$2
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers$2 done
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers$3
Event: 8.748 Loading class java/lang/invoke/MethodHandleImpl$Makers$3 done
Event: 8.758 Loading class java/lang/runtime/ObjectMethods$1
Event: 8.758 Loading class java/lang/runtime/ObjectMethods$1 done
Event: 8.806 Loading class java/lang/StringIndexOutOfBoundsException
Event: 8.806 Loading class java/lang/StringIndexOutOfBoundsException done

Classes unloaded (7 events):
Event: 8.045 Thread 0x000001821e06ec10 Unloading class 0x0000018234199400 'java/lang/invoke/LambdaForm$MH+0x0000018234199400'
Event: 8.045 Thread 0x000001821e06ec10 Unloading class 0x0000018234199000 'java/lang/invoke/LambdaForm$MH+0x0000018234199000'
Event: 8.045 Thread 0x000001821e06ec10 Unloading class 0x0000018234198c00 'java/lang/invoke/LambdaForm$MH+0x0000018234198c00'
Event: 8.045 Thread 0x000001821e06ec10 Unloading class 0x0000018234198800 'java/lang/invoke/LambdaForm$MH+0x0000018234198800'
Event: 8.045 Thread 0x000001821e06ec10 Unloading class 0x0000018234198400 'java/lang/invoke/LambdaForm$BMH+0x0000018234198400'
Event: 8.045 Thread 0x000001821e06ec10 Unloading class 0x0000018234198000 'java/lang/invoke/LambdaForm$DMH+0x0000018234198000'
Event: 8.045 Thread 0x000001821e06ec10 Unloading class 0x0000018234196c00 'java/lang/invoke/LambdaForm$DMH+0x0000018234196c00'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 4.905 Thread 0x0000018274c8fc60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec0c00c8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ec0c00c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.407 Thread 0x0000018274c8fc60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf9ba30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eaf9ba30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.109 Thread 0x0000018274c8fc60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb411420}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x00000000eb411420) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.731 Thread 0x0000018274c8fc60 Exception <a 'java/io/FileNotFoundException'{0x00000000ebb741a0}> (0x00000000ebb741a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 6.732 Thread 0x0000018274c8fc60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebb7c570}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000ebb7c570) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.710 Thread 0x0000018274c8fc60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaffe6f8}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000eaffe6f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.711 Thread 0x0000018274c8fc60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb001d88}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eb001d88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.015 Thread 0x0000018274c8fc60 Exception <a 'java/io/FileNotFoundException'{0x00000000ec12ddc0}> (0x00000000ec12ddc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 8.015 Thread 0x0000018274c8fc60 Exception <a 'java/io/FileNotFoundException'{0x00000000ec12ebe8}> (0x00000000ec12ebe8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 8.015 Thread 0x0000018274c8fc60 Exception <a 'java/io/FileNotFoundException'{0x00000000ec12fb18}> (0x00000000ec12fb18) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 8.574 Thread 0x0000018274c8fc60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb5da308}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb5da308) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.718 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb929f08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb929f08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.719 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb92e6e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb92e6e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.758 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eba2fd70}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eba2fd70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.759 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eba33a68}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x00000000eba33a68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.760 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eba3ad70}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x00000000eba3ad70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.761 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eba416a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x00000000eba416a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.766 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eba48138}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x00000000eba48138) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.766 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eba4c508}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000eba4c508) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.774 Thread 0x0000018279a17bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eba6a938}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object)'> (0x00000000eba6a938) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 4.895 Executing VM operation: ICBufferFull
Event: 4.895 Executing VM operation: ICBufferFull done
Event: 5.168 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.168 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.201 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 5.211 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.316 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.316 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.325 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.325 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 6.326 Executing VM operation: Cleanup
Event: 6.326 Executing VM operation: Cleanup done
Event: 7.326 Executing VM operation: Cleanup
Event: 7.326 Executing VM operation: Cleanup done
Event: 7.507 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 7.516 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.021 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 8.099 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 8.802 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 8.829 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.086 Thread 0x000001821dfb08e0 Thread added: 0x0000018232300210
Event: 0.145 Thread 0x000001821dfb08e0 Thread added: 0x000001821e01dec0
Event: 0.865 Thread 0x000001821dfb08e0 Thread added: 0x000001827431bc60
Event: 1.152 Thread 0x0000018232300210 Thread added: 0x00000182743c0110
Event: 2.571 Thread 0x000001821dfb08e0 Thread added: 0x0000018274627480
Event: 2.846 Thread 0x000001821dfb08e0 Thread added: 0x0000018274c0a130
Event: 2.853 Thread 0x0000018274c0a130 Thread added: 0x00000182747e3670
Event: 2.859 Thread 0x000001821dfb08e0 Thread added: 0x0000018274c8fc60
Event: 3.285 Thread 0x0000018274c8fc60 Thread added: 0x00000182796df270
Event: 3.548 Thread 0x0000018274c8fc60 Thread added: 0x0000018279a79f60
Event: 3.650 Thread 0x00000182743c0110 Thread exited: 0x00000182743c0110
Event: 3.686 Thread 0x0000018274c8fc60 Thread added: 0x00000182743c0110
Event: 3.995 Thread 0x0000018232300210 Thread added: 0x0000018274c05f50
Event: 4.340 Thread 0x0000018274c8fc60 Thread added: 0x0000018279a18280
Event: 4.634 Thread 0x0000018274c05f50 Thread exited: 0x0000018274c05f50
Event: 4.782 Thread 0x0000018274c8fc60 Thread added: 0x0000018279a15490
Event: 4.833 Thread 0x0000018274c8fc60 Thread added: 0x0000018279a17bf0
Event: 5.223 Thread 0x0000018232300210 Thread added: 0x0000018274c06320
Event: 5.800 Thread 0x0000018274c06320 Thread exited: 0x0000018274c06320
Event: 8.714 Thread 0x0000018279a17bf0 Thread added: 0x0000018279a15b20


Dynamic libraries:
0x00007ff75f730000 - 0x00007ff75f73e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffbb9f90000 - 0x00007ffbba170000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffbb7970000 - 0x00007ffbb7a1e000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffbb6eb0000 - 0x00007ffbb7116000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffbb7310000 - 0x00007ffbb7406000 	C:\Windows\System32\ucrtbase.dll
0x00007ffbb1120000 - 0x00007ffbb1138000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffbb0b40000 - 0x00007ffbb0b5e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffbb7f20000 - 0x00007ffbb80af000 	C:\Windows\System32\USER32.dll
0x00007ffbb72f0000 - 0x00007ffbb7310000 	C:\Windows\System32\win32u.dll
0x00007ffbb7a20000 - 0x00007ffbb7a48000 	C:\Windows\System32\GDI32.dll
0x00007ffbb6430000 - 0x00007ffbb65c4000 	C:\Windows\System32\gdi32full.dll
0x00007ffbb6390000 - 0x00007ffbb642b000 	C:\Windows\System32\msvcp_win.dll
0x00007ffbafe00000 - 0x00007ffbb0069000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f\COMCTL32.dll
0x00007ffbb9ec0000 - 0x00007ffbb9f5d000 	C:\Windows\System32\msvcrt.dll
0x00007ffbb8570000 - 0x00007ffbb8878000 	C:\Windows\System32\combase.dll
0x00007ffbb7460000 - 0x00007ffbb757f000 	C:\Windows\System32\RPCRT4.dll
0x00007ffbb65d0000 - 0x00007ffbb6642000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffbb7780000 - 0x00007ffbb77ad000 	C:\Windows\System32\IMM32.DLL
0x00007ffbb1640000 - 0x00007ffbb164c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffbaace0000 - 0x00007ffbaad6d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ffb7f7c0000 - 0x00007ffb80550000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffbb8880000 - 0x00007ffbb8921000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffbb8510000 - 0x00007ffbb856b000 	C:\Windows\System32\sechost.dll
0x00007ffbb62e0000 - 0x00007ffbb632c000 	C:\Windows\System32\POWRPROF.dll
0x00007ffbb7810000 - 0x00007ffbb787c000 	C:\Windows\System32\WS2_32.dll
0x00007ffbb47c0000 - 0x00007ffbb47e3000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffbb1a90000 - 0x00007ffbb1a9a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffbb4730000 - 0x00007ffbb475a000 	C:\Windows\SYSTEM32\WINMMBASE.dll
0x00007ffbb7410000 - 0x00007ffbb745a000 	C:\Windows\System32\cfgmgr32.dll
0x00007ffbb6370000 - 0x00007ffbb6381000 	C:\Windows\System32\kernel.appcore.dll
0x00007ffbb15f0000 - 0x00007ffbb15fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffb9e7e0000 - 0x00007ffb9e9a8000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffbaf590000 - 0x00007ffbaf5b9000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffbb1110000 - 0x00007ffbb111f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffbb0600000 - 0x00007ffbb061f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffbb8930000 - 0x00007ffbb9d67000 	C:\Windows\System32\SHELL32.dll
0x00007ffbb8340000 - 0x00007ffbb83e6000 	C:\Windows\System32\shcore.dll
0x00007ffbb66b0000 - 0x00007ffbb6df7000 	C:\Windows\System32\windows.storage.dll
0x00007ffbb7720000 - 0x00007ffbb7771000 	C:\Windows\System32\shlwapi.dll
0x00007ffbb6330000 - 0x00007ffbb634b000 	C:\Windows\System32\profapi.dll
0x00007ffbb0350000 - 0x00007ffbb0368000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffbb05f0000 - 0x00007ffbb0600000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffbb0260000 - 0x00007ffbb033e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffbb5b10000 - 0x00007ffbb5b76000 	C:\Windows\system32\mswsock.dll
0x00007ffbab5b0000 - 0x00007ffbab5c6000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffbb0340000 - 0x00007ffbb0350000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffba1610000 - 0x00007ffba1655000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffbb9d70000 - 0x00007ffbb9eb9000 	C:\Windows\System32\ole32.dll
0x00007ffbab3d0000 - 0x00007ffbab3da000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
0x00007ffbab3c0000 - 0x00007ffbab3cb000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll
0x00007ffbb82d0000 - 0x00007ffbb82d8000 	C:\Windows\System32\PSAPI.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.16299.64_none_cc9304e22215ca8f;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6a2084654daf15e3f33836b0104d5d77-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1337745770de6b52d7081e04c199055d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17.0.14+7
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\python313\Scripts\;C:\python313\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\ffmpeg-master-latest-win64-gpl;C:\ffmpeg\bin;C:\Program Files\Git\cmd;C:\Windows\System32;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\java\jdk-24;C:\Program Files\Java\jdk-24\bin;C:\src\flutter\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\JetBrains\PyCharm Community Edition 2024.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;C:\dart-sdk\bin;C:\Program Files\Java\jdk-17.0.14+7\bin;C:\gradle-8.13\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=snk
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 23 Stepping 7, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 16299 (10.0.16299.15)
OS uptime: 0 days 1:16 hours

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 23 stepping 7 microcode 0x70b, cx8, cmov, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, tsc, clflush
Processor Information for the first 4 processors :
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500

Memory: 4k page, system-wide physical 4094M (855M free)
TotalPageFile size 4094M (AvailPageFile size 385M)
current process WorkingSet (physical memory assigned to process): 150M, peak: 150M
current process commit charge ("private bytes"): 234M, peak: 235M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
