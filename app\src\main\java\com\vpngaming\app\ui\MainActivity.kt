package com.vpngaming.app.ui

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import com.vpngaming.app.ui.screen.MainScreen
import com.vpngaming.app.ui.theme.VPNGamingTheme
import com.vpngaming.app.ui.viewmodel.VpnViewModel
import com.vpngaming.app.vpn.VpnManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * النشاط الرئيسي للتطبيق
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var vpnManager: VpnManager

    private val vpnViewModel: VpnViewModel by viewModels()

    // مُعالج طلب إذن VPN
    private val vpnPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            // تم منح الإذن، يمكن المتابعة مع الاتصال
            handleVpnPermissionGranted()
        } else {
            // تم رفض الإذن
            handleVpnPermissionDenied()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            VPNGamingTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen(
                        vpnViewModel = vpnViewModel,
                        onConnectRequest = { server ->
                            requestVpnPermissionAndConnect(server)
                        },
                        onDisconnectRequest = {
                            vpnViewModel.disconnect()
                        }
                    )
                }
            }
        }
    }

    /**
     * طلب إذن VPN والاتصال
     */
    private fun requestVpnPermissionAndConnect(server: com.vpngaming.app.data.model.VpnServer?) {
        lifecycleScope.launch {
            val vpnIntent = vpnManager.checkVpnPermission()
            if (vpnIntent != null) {
                // يحتاج إذن VPN
                vpnPermissionLauncher.launch(vpnIntent)
                // حفظ الخادم المطلوب للاتصال بعد منح الإذن
                pendingServer = server
            } else {
                // الإذن موجود، يمكن الاتصال مباشرة
                if (server != null) {
                    vpnViewModel.connect(server)
                } else {
                    vpnViewModel.connectToBestServer()
                }
            }
        }
    }

    private var pendingServer: com.vpngaming.app.data.model.VpnServer? = null

    /**
     * معالجة منح إذن VPN
     */
    private fun handleVpnPermissionGranted() {
        lifecycleScope.launch {
            val server = pendingServer
            if (server != null) {
                vpnViewModel.connect(server)
            } else {
                vpnViewModel.connectToBestServer()
            }
            pendingServer = null
        }
    }

    /**
     * معالجة رفض إذن VPN
     */
    private fun handleVpnPermissionDenied() {
        // يمكن عرض رسالة للمستخدم توضح أهمية الإذن
        pendingServer = null
    }

    override fun onDestroy() {
        super.onDestroy()
        // تنظيف الموارد
        vpnManager.cleanup()
    }
}
